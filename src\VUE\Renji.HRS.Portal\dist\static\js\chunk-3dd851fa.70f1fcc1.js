(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3dd851fa"],{"500a":function(e,t,n){},7265:function(e,t,n){"use strict";var r=n("500a"),o=n.n(r);o.a},b2c0:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container",staticStyle:{padding:"0px"}},[n("layout2",{scopedSlots:e._u([{key:"header",fn:function(){return[n("el-row",[n("el-col",{attrs:{span:2}},[n("span",[e._v("用户组")])]),n("el-col",{attrs:{span:22}},[n("el-select",{staticStyle:{width:"30%"},attrs:{filterable:""},on:{change:e.usergroupchange},model:{value:e.usergroupid,callback:function(t){e.usergroupid=t},expression:"usergroupid"}},e._l(e.usergroupoptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)]},proxy:!0},{key:"main",fn:function(){return[n("el-tabs",{on:{"tab-click":e.handleTabClickMethod},model:{value:e.actionName,callback:function(t){e.actionName=t},expression:"actionName"}},[n("el-tab-pane",{attrs:{label:"模块信息",name:"first"}},[n("el-row",{staticStyle:{"margin-bottom":"15px"}},[n("el-col",[n("el-button",{attrs:{type:"primary"},on:{click:e.savefirstMethod}},[e._v("保 存")])],1)],1),n("el-row",[n("el-col",{staticClass:"tree",attrs:{span:10}},[n("el-tree",{ref:"firsttree",attrs:{data:e.firstData,"show-checkbox":"","node-key":"id","default-checked-keys":e.firstDefaultCheckedKeys,props:e.firstdefaultProps}})],1)],1)],1),n("el-tab-pane",{attrs:{label:"组织结构",name:"second"}},[n("el-row",{staticStyle:{"margin-bottom":"15px"}},[n("el-col",[n("el-button",{attrs:{type:"primary"},on:{click:e.savesecondMethod}},[e._v("保 存")])],1)],1),n("el-row",[n("el-col",{staticClass:"tree",attrs:{span:10}},[n("el-tree",{ref:"secondtree",attrs:{data:e.secondData,"show-checkbox":"","node-key":"id","default-checked-keys":e.secondDefaultCheckedKeys,"default-expanded-keys":e.secondDefaultexpandedKeys,props:e.seconddefaultProps}})],1)],1)],1),n("el-tab-pane",{attrs:{label:"人事分页",name:"fourth"}},[n("el-row",{staticStyle:{"margin-bottom":"15px"}},[n("el-col",[n("el-button",{attrs:{type:"primary"},on:{click:e.savefourthMethod}},[e._v("保 存")])],1)],1),n("el-row",[n("el-col",{attrs:{span:20}},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.fourthlistLoading,expression:"fourthlistLoading"}],ref:"fourthtableList",staticStyle:{width:"100%"},attrs:{data:e.fourthPageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"selection-change":e.fourthSelectChange}},[n("el-table-column",{attrs:{type:"selection"}}),n("el-table-column",{attrs:{label:"说明",sortable:"custom",prop:"ControlRightPanel.Desc"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.controlRightPanelDesc))])]}}])}),n("el-table-column",{attrs:{label:"用户组名称",sortable:"custom",prop:"Role.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.userGroupName))])]}}])}),n("el-table-column",{attrs:{label:"控件ID",sortable:"custom",prop:"ControlRightPanel.Code"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.controlRightPanelCode))])]}}])}),n("el-table-column",{attrs:{label:"控件类型",sortable:"custom",prop:"ControlRightPanel.EnumType"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.controlRightPanelTypeDesc))])]}}])}),n("el-table-column",{attrs:{label:"权限",sortable:"custom",prop:"EnumUserRight","min-width":"130px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("el-radio-group",{model:{value:r.enumUserRight,callback:function(t){e.$set(r,"enumUserRight",t)},expression:"row.enumUserRight"}},[n("el-radio-button",{attrs:{label:"0"}},[e._v("无权限")]),n("el-radio-button",{attrs:{label:"1"}},[e._v("只读")]),n("el-radio-button",{attrs:{label:"2"}},[e._v("可写")]),n("el-radio-button",{attrs:{label:"3"}},[e._v("完全")])],1)]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.fourthGetPageList}})],1)],1)],1)],1)]},proxy:!0}])})],1)},o=[],a=(n("99af"),n("d81d"),n("d3b7"),n("f9ac")),i=n("d368"),u={components:{},data:function(){return{fourthlistLoading:!1,usergroupoptions:[],usergroupid:[],firstData:[],firstDefaultCheckedKeys:[],firstdefaultProps:{children:"children",label:"name"},secondData:[],secondDefaultCheckedKeys:[],secondDefaultexpandedKeys:[],seconddefaultProps:{children:"children",label:"name"},actionName:"first",fourthPageList:[],listQuery:{queryCondition:{},total:1,pageIndex:1,pageSize:10},fourthSelectRow:[]}},created:function(){this.loadUserGroupsMothod(),this.loadModuleInfoMothod(),this.loadDeptMothod()},methods:{sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.loadSelectPage()},loadData:function(){switch(this.actionName){case"first":this.loadSelectModules();break;case"second":this.loadSelectDepts();break;case"fourth":this.loadSelectPage();break;default:break}},handleTabClickMethod:function(e,t){this.loadData()},usergroupchange:function(e){this.loadData()},loadUserGroupsMothod:function(){var e=this;a["a"].dropdownUserGroups().then((function(t){e.usergroupoptions=t.data})).catch((function(e){return console.log(e)}))},loadModuleInfoMothod:function(){var e=this;a["a"].queryModuleInfos().then((function(t){e.firstData=t.data})).catch((function(e){return console.log(e)}))},loadDeptMothod:function(){var e=this;i["a"].QueryOrganization().then((function(t){e.secondData=t.data,e.secondData&&e.secondData.length>0&&e.secondDefaultexpandedKeys.push(e.secondData[0].id)})).catch((function(e){return console.log(e)}))},loadSelectModules:function(){var e=this;this.usergroupid&&a["a"].getRightSettingByUserGroup({usergroupid:this.usergroupid}).then((function(t){e.firstDefaultCheckedKeys=t.data.map((function(e){return e.permissionId})),e.$refs.firsttree.setCheckedKeys(e.firstDefaultCheckedKeys)})).catch((function(e){return console.log(e)}))},loadSelectDepts:function(){var e=this;this.usergroupid&&a["a"].getRightOfDeptByUserGroup({usergroupid:this.usergroupid}).then((function(t){e.secondDefaultCheckedKeys=t.data.map((function(e){return e.departmentId})),e.$refs.secondtree.setCheckedKeys(e.secondDefaultCheckedKeys)})).catch((function(e){return console.log(e)}))},loadSelectPage:function(){this.fourthGetPageList()},savefirstMethod:function(){var e=this;if(this.usergroupid){var t=this.$refs.firsttree.getCheckedKeys().concat(this.$refs.firsttree.getHalfCheckedKeys());a["a"].saveRightSetting({userGroupId:this.usergroupid,moduleinfoids:t}).then((function(t){t.succeed?e.$notice.message("保存成功","success"):e.$notice.resultTip(t)})).catch((function(e){return console.log(e)}))}else this.$message.error("请选择用户组")},savesecondMethod:function(){var e=this;if(this.usergroupid){var t=this.$refs.secondtree.getCheckedKeys();a["a"].saveRightOfDept({userGroupId:this.usergroupid,deptIds:t}).then((function(t){t.succeed?e.$notice.message("保存成功","success"):e.$notice.resultTip(t)})).catch((function(e){return console.log(e)}))}else this.$message.error("请选择用户组")},fourthGetPageList:function(){var e=this;this.usergroupid&&(this.listQuery.userGroupId=this.usergroupid,this.fourthlistLoading=!0,a["a"].queryControlRight(this.listQuery).then((function(t){t.succeed?(e.fourthPageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.fourthlistLoading=!1})))},savefourthMethod:function(){var e=this;this.usergroupid?a["a"].saveControlRights(this.fourthSelectRow).then((function(t){t.succeed?e.$notice.message("保存成功","success"):e.$notice.resultTip(t)})).catch((function(e){return console.log(e)})):this.$message.error("请选择用户组")},fourthSelectChange:function(e){this.fourthSelectRow=e}}},s=u,l=(n("7265"),n("2877")),c=Object(l["a"])(s,r,o,!1,null,"c1a6a3f2",null);t["default"]=c.exports},d368:function(e,t,n){"use strict";var r=n("cfe3"),o="Organization",a=new r["a"](o);t["a"]={QueryOrganizationHiddenTop:function(e){return a.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return a.get("QueryOrganization",e)},QueryDepartment:function(e){return a.get("QueryDepartment",e)},GetDepartment:function(e){return a.get("GetDepartment",e)},AddDepartment:function(e){return a.post("AddDepartment",e)},UpdateDepartment:function(e){return a.post("UpdateDepartment",e)},MoveDepartment:function(e){return a.post("MoveDepartment",e)},MergeDepartment:function(e){return a.post("MergeDepartment",e)},DeleteDepartment:function(e){return a.post("DeleteDepartment",e)},queryPosition:function(e){return a.post("QueryPosition",e)},getPosition:function(e){return a.get("GetPosition",e)},addPosition:function(e){return a.post("AddPosition",e)},updatePosition:function(e){return a.post("UpdatePosition",e)},deletePosition:function(e){return a.post("DeletePosition",e)},GetStation:function(e){return a.get("GetStation",e)},AddStation:function(e){return a.post("AddStation",e)},UpdateStation:function(e){return a.post("UpdateStation",e)},DeleteStation:function(e){return a.post("DeleteStation",e)},QueryPositionStationTree:function(e){return a.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return a.post("AllocatePosition",e)},DeletePositionStation:function(e){return a.post("DeletePositionStation",e)},queryDeptByUser:function(e){return a.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return a.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return a.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),a.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return a.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return a.get("GetStationAllowance",e)},addStationAllowance:function(e){return a.post("AddStationAllowance",e)},updateStationAllowance:function(e){return a.post("UpdateStationAllowance",e)},querySeniority:function(e){return a.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),a.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return a.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return a.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return a.get("GetSeniority",e)},addSeniority:function(e){return a.post("AddSeniority",e)},updateSeniority:function(e){return a.post("UpdateSeniority",e)},querySalaryScale:function(e){return a.get("QuerySalaryScale",e)},getSalaryScale:function(e){return a.get("GetSalaryScale",e)},addSalaryScale:function(e){return a.post("AddSalaryScale",e)},updateSalaryScale:function(e){return a.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return a.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),a.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return a.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return a.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return a.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return a.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return a.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return a.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return a.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return a.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return a.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return a.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return a.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return a.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return a.post("DeleteTelephoneFee",e)}}},f9ac:function(e,t,n){"use strict";var r=n("cfe3"),o="SysManage",a=new r["a"](o);t["a"]={queryDict:function(e){return a.get("QueryDict",e)},queryDictType:function(e){return a.post("QueryDictType",e)},addDict:function(e){return a.post("AddDict",e)},deleteDict:function(e){return a.post("DeleteDict",e)},updateDict:function(e){return a.post("UpdateDict",e)},getDict:function(e){return a.get("GetDict",e)},querySysSetting:function(e){return a.get("QuerySysSetting",e)},addSysSetting:function(e){return a.post("AddSysSetting",e)},deleteSysSetting:function(e){return a.post("DeleteSysSetting",e)},updateSysSetting:function(e){return a.post("UpdateSysSetting",e)},getSysSetting:function(e){return a.get("GetSysSetting",e)},queryLanguage:function(e){return a.get("QueryLanguage",e)},getEnumInfos:function(e){return a.get("GetEnumInfos",e)},queryUserGroups:function(e){return a.post("QueryUserGroups",e)},saveUserGroup:function(e){return a.post("SaveUserGroup",e)},deleteUserGroup:function(e){return a.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return a.get("DropdownUserGroups",e)},queryUsers:function(e){return a.post("QueryUsers",e)},saveUser:function(e){return a.post("SaveUser",e)},deleteUser:function(e){return a.post("DeleteUser",e)},initPwd:function(e){return a.post("InitPwd",e)},getUserById:function(e){return a.get("GetUserById",e)},queryEmployees:function(e){return a.post("QueryEmployees",e)},queryModuleInfos:function(e){return a.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return a.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return a.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return a.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return a.post("SaveRightOfDept",e)},queryControlRight:function(e){return a.post("QueryControlRight",e)},saveControlRights:function(e){return a.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return a.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return a.get("QueryStationTree",e)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(e){return a.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return a.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)}}}}]);