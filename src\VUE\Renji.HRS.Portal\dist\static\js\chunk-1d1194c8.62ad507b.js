(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1d1194c8"],{"1b77":function(t,e,r){"use strict";var n=r("7b35"),o=r.n(n);o.a},"7b35":function(t,e,r){},cbd2:function(t,e,r){"use strict";var n=r("cfe3"),o="AttendanceManage",a=new n["a"](o);e["a"]={getAttMonthShiftRecord:function(t){return a.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return a.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return a.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return a.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return a.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return a.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return a.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return a.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return a.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return a.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return a.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return a.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return a.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return a.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return a.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return a.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return a.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return a.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return a.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return a.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return a.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return a.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return a.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return a.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return a.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return a.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return a.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return a.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return a.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return a.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return a.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return a.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return a.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return a.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return a.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return a.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return a.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return a.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return a.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return a.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return a.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return a.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return a.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return a.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return a.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return a.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return a.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return a.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return a.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return a.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return a.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return a.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return a.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return a.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return a.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return a.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return a.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return a.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return a.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return a.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return a.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},d368:function(t,e,r){"use strict";var n=r("cfe3"),o="Organization",a=new n["a"](o);e["a"]={QueryOrganizationHiddenTop:function(t){return a.get("QueryOrganizationHiddenTop",t)},QueryOrganization:function(t){return a.get("QueryOrganization",t)},QueryDepartment:function(t){return a.get("QueryDepartment",t)},GetDepartment:function(t){return a.get("GetDepartment",t)},AddDepartment:function(t){return a.post("AddDepartment",t)},UpdateDepartment:function(t){return a.post("UpdateDepartment",t)},MoveDepartment:function(t){return a.post("MoveDepartment",t)},MergeDepartment:function(t){return a.post("MergeDepartment",t)},DeleteDepartment:function(t){return a.post("DeleteDepartment",t)},queryPosition:function(t){return a.post("QueryPosition",t)},getPosition:function(t){return a.get("GetPosition",t)},addPosition:function(t){return a.post("AddPosition",t)},updatePosition:function(t){return a.post("UpdatePosition",t)},deletePosition:function(t){return a.post("DeletePosition",t)},GetStation:function(t){return a.get("GetStation",t)},AddStation:function(t){return a.post("AddStation",t)},UpdateStation:function(t){return a.post("UpdateStation",t)},DeleteStation:function(t){return a.post("DeleteStation",t)},QueryPositionStationTree:function(t){return a.get("QueryPositionStationTree",t)},AllocatePosition:function(t){return a.post("AllocatePosition",t)},DeletePositionStation:function(t){return a.post("DeletePositionStation",t)},queryDeptByUser:function(t){return a.get("QueryDeptByUser",t)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(t){return a.get("QueryTwoLevelStation",t)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(t){return a.get("QueryStationAllowance",t)},downloadStationAllowanceTemplate:function(t){return a.post("DownloadStationAllowanceTemplate",{data:t,responseType:"arraybuffer"})},importStationAllowance:function(t,e){return e.append("file",t),a.postForm("ImportStationAllowance",e)},exportStationAllowance:function(t){return a.post("ExportStationAllowance",{data:t,responseType:"arraybuffer"})},getStationAllowance:function(t){return a.get("GetStationAllowance",t)},addStationAllowance:function(t){return a.post("AddStationAllowance",t)},updateStationAllowance:function(t){return a.post("UpdateStationAllowance",t)},querySeniority:function(t){return a.get("QuerySeniority",t)},importSeniority:function(t,e){return e.append("file",t),a.postForm("ImportSeniority",e)},downloadSeniorityTemplate:function(t){return a.post("DownloadSeniorityTemplate",{data:t,responseType:"arraybuffer"})},exportSeniority:function(t){return a.post("ExportSeniority",{data:t,responseType:"arraybuffer"})},getSeniority:function(t){return a.get("GetSeniority",t)},addSeniority:function(t){return a.post("AddSeniority",t)},updateSeniority:function(t){return a.post("UpdateSeniority",t)},querySalaryScale:function(t){return a.get("QuerySalaryScale",t)},getSalaryScale:function(t){return a.get("GetSalaryScale",t)},addSalaryScale:function(t){return a.post("AddSalaryScale",t)},updateSalaryScale:function(t){return a.post("UpdateSalaryScale",t)},deleteSalaryScale:function(t){return a.post("DeleteSalaryScale",t)},importSalaryScale:function(t,e){return e.append("file",t),a.postForm("ImportSalaryScale",e)},exportSalaryScale:function(t){return a.post("ExportSalaryScale",{data:t,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(t){return a.get("QuerySalaryScaleByStationId",t)},queryStationAllowanceByStationId:function(t){return a.get("QueryStationAllowanceByStationId",t)},queryCarSubsidy:function(t){return a.get("QueryCarSubsidy",t)},getCarSubsidy:function(t){return a.get("GetCarSubsidy",t)},addCarSubsidy:function(t){return a.post("AddCarSubsidy",t)},updateCarSubsidy:function(t){return a.post("UpdateCarSubsidy",t)},deleteCarSubsidy:function(t){return a.post("DeleteCarSubsidy",t)},queryTelephoneFee:function(t){return a.get("QueryTelephoneFee",t)},getTelephoneFee:function(t){return a.get("GetTelephoneFee",t)},addTelephoneFee:function(t){return a.post("AddTelephoneFee",t)},updateTelephoneFee:function(t){return a.post("UpdateTelephoneFee",t)},deleteTelephoneFee:function(t){return a.post("DeleteTelephoneFee",t)}}},f9ac:function(t,e,r){"use strict";var n=r("cfe3"),o="SysManage",a=new n["a"](o);e["a"]={queryDict:function(t){return a.get("QueryDict",t)},queryDictType:function(t){return a.post("QueryDictType",t)},addDict:function(t){return a.post("AddDict",t)},deleteDict:function(t){return a.post("DeleteDict",t)},updateDict:function(t){return a.post("UpdateDict",t)},getDict:function(t){return a.get("GetDict",t)},querySysSetting:function(t){return a.get("QuerySysSetting",t)},addSysSetting:function(t){return a.post("AddSysSetting",t)},deleteSysSetting:function(t){return a.post("DeleteSysSetting",t)},updateSysSetting:function(t){return a.post("UpdateSysSetting",t)},getSysSetting:function(t){return a.get("GetSysSetting",t)},queryLanguage:function(t){return a.get("QueryLanguage",t)},getEnumInfos:function(t){return a.get("GetEnumInfos",t)},queryUserGroups:function(t){return a.post("QueryUserGroups",t)},saveUserGroup:function(t){return a.post("SaveUserGroup",t)},deleteUserGroup:function(t){return a.post("DeleteUserGroup",t)},dropdownUserGroups:function(t){return a.get("DropdownUserGroups",t)},queryUsers:function(t){return a.post("QueryUsers",t)},saveUser:function(t){return a.post("SaveUser",t)},deleteUser:function(t){return a.post("DeleteUser",t)},initPwd:function(t){return a.post("InitPwd",t)},getUserById:function(t){return a.get("GetUserById",t)},queryEmployees:function(t){return a.post("QueryEmployees",t)},queryModuleInfos:function(t){return a.get("QueryModuleInfos",t)},getRightSettingByUserGroup:function(t){return a.get("GetRightSettingByUserGroup",t)},saveRightSetting:function(t){return a.post("SaveRightSetting",t)},getRightOfDeptByUserGroup:function(t){return a.get("GetRightOfDeptByUserGroup",t)},saveRightOfDept:function(t){return a.post("SaveRightOfDept",t)},queryControlRight:function(t){return a.post("QueryControlRight",t)},saveControlRights:function(t){return a.post("SaveControlRights",t)},getControlRightByCurrentUser:function(t){return a.get("GetControlRightByCurrentUser",t)},queryStationTree:function(t){return a.get("QueryStationTree",t)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(t){return a.get("QueryStationSelector",t)},querySalaryScaleSelector:function(t){return a.get("QuerySalaryScaleSelector",t)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(t){return a.get("QueryStationAllowance",t)}}},fb43:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container"},[r("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[r("div",{staticClass:"block"},[r("el-form",{attrs:{inline:!0,size:"small"}},[r("el-row",[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"节日加班日期"}},[r("el-date-picker",{staticClass:"input_headerDate",attrs:{type:"date",placeholder:"请选择","value-format":"yyyy-MM-dd"},on:{change:t.dateChange},model:{value:t.recordDate,callback:function(e){t.recordDate=e},expression:"recordDate"}})],1)],1)],1)],1)],1),r("c-tree",{attrs:{options:t.treeData,props:t.treeProps,"expanded-keys":t.treeExpandedKeys},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[r("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"制表人"}},[r("span",[t._v(" "+t._s(t.headModel.documentMaker)+" ")])])],1),1==t.headModel.enumStatus?r("el-col",{attrs:{span:12}},[r("el-button",{attrs:{type:"primary"},on:{click:t.saveRecord}},[t._v("暂存")])],1):t._e()],1)],1),r("div",{staticClass:"tip_div"},[r("span",{staticClass:"tip_content"},[t._v(t._s(t.headModel.tip))])]),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"},on:{"sort-change":t.sortChange}},[r("el-table-column",{attrs:{prop:"EmpCode",label:"工号",align:"center",width:"130",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.empCode))])]}}])}),r("el-table-column",{attrs:{prop:"DisplayName",label:"姓名",align:"center",width:"160",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.empName))])]}}])}),r("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.hireStyleName))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"加班类型",align:"center",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[1==t.headModel.enumStatus?r("el-select",{attrs:{placeholder:"请选择"},model:{value:n.enumOverTimeType,callback:function(e){t.$set(n,"enumOverTimeType",e)},expression:"row.enumOverTimeType"}},t._l(t.overTimeType,(function(t){return r("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1):r("span",[t._v(t._s(n.enumOverTimeTypeDesc))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"人事修改人",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.updator))])]}}])})],1),r("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},o=[],a=(r("99af"),r("fb6a"),r("d3b7"),r("25f0"),r("4d90"),r("d368")),i=r("cbd2"),c=r("f9ac"),u={components:{},data:function(){return{total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,recordDate:this.getNowTime(),headModel:{},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},currentNode:null,tableData:[],allData:[],treeExpandedKeys:[],overTimeType:[]}},created:function(){this.loadTree(),this.loadOverTimeType()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),r=t.getMonth(),n=t.getDate();r+=1,r=r.toString().padStart(2,"0"),n=n.toString().padStart(2,"0");var o="".concat(e,"-").concat(r,"-").concat(n);return o},loadTree:function(){var t=this;this.treeLoading=!0,a["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})).finally((function(){t.treeLoading=!1})),this.resetCurrentNode()},loadOverTimeType:function(){var t=this;c["a"].getEnumInfos({enumType:"OverTimeType"}).then((function(e){t.overTimeType=e.data.datas})).catch((function(t){console.log(t)}))},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttHolidayOTRecord()},dateChange:function(){this.listQuery.pageIndex=1,this.getAttHolidayOTRecord()},sortChange:function(t,e,r){this.listQuery.pageIndex=1;var n="";"descending"===t.order?n="desc":"ascending"===t.order&&(n="asc"),this.listQuery.order=n?t.prop+" "+n:"",this.getAttHolidayOTRecord()},getAttHolidayOTRecord:function(){var t=this;if(this.currentNode&&this.recordDate){var e={RecordDate:this.recordDate,DeptId:this.currentNode.id};i["a"].getAttHolidayOTRecord(e).then((function(e){e.succeed?(t.headModel=e.data,t.queryAttHolidayOTRecordDetail(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}else this.$notice.message("请选择部门和节日加班日期。","info")},queryAttHolidayOTRecordDetail:function(t){var e=this,r={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordDate,Order:this.listQuery.order};i["a"].queryAttHolidayOTRecordDetail(r).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},saveRecord:function(){var t=this;this.recordDate&&this.currentNode&&(this.headModel.details=this.allData,i["a"].saveAttHolidayOTRecord(this.headModel).then((function(e){e.succeed?(t.getAttHolidayOTRecord(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))),this.currentNode?this.recordDate||this.$notice.message("请选择加班日期","warning"):this.$notice.message("请选择部门","warning")}}},l=u,d=(r("1b77"),r("2877")),s=Object(d["a"])(l,n,o,!1,null,null,null);e["default"]=s.exports}}]);