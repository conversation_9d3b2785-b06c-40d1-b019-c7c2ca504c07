(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-aaddf3d8"],{"9c2b":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"月份"}},[n("el-date-picker",{staticClass:"input_class",attrs:{type:"month",placeholder:"请选择月份","value-format":"yyyy-MM",size:"small",clearable:!1},model:{value:t.headModel.recordMonth,callback:function(e){t.$set(t.headModel,"recordMonth",e)},expression:"headModel.recordMonth"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"部门"}},[n("c-select-tree",{attrs:{options:t.treeData,"tree-props":t.treeProps},model:{value:t.headModel.dept,callback:function(e){t.$set(t.headModel,"dept",e)},expression:"headModel.dept"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工号"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empCode,callback:function(e){t.$set(t.headModel,"empCode",e)},expression:"headModel.empCode"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"姓名"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empName,callback:function(e){t.$set(t.headModel,"empName",e)},expression:"headModel.empName"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:""}},[n("el-button",{attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"显示有假期项"}},[n("el-checkbox",{model:{value:t.headModel.onlyDayOffValue,callback:function(e){t.$set(t.headModel,"onlyDayOffValue",e)},expression:"headModel.onlyDayOffValue"}})],1),n("el-form-item",{attrs:{label:"显示有卫贴项"}},[n("el-checkbox",{model:{value:t.headModel.onlyH1Value,callback:function(e){t.$set(t.headModel,"onlyH1Value",e)},expression:"headModel.onlyH1Value"}})],1)],1)],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[n("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"120",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empUid))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"120",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"120",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empName))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"部门",align:"center",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empDept))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"应发公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(0===r.generalHoliday?"":r.generalHoliday))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"剩余公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.historyH12=Math.abs(r.historyH12)}},model:{value:r.historyH12,callback:function(e){t.$set(r,"historyH12",t._n(e))},expression:"row.historyH12"}}):n("span",[t._v(t._s(0===r.historyH12?"":r.historyH12))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h12=Math.abs(r.h12)}},model:{value:r.h12,callback:function(e){t.$set(r,"h12",t._n(e))},expression:"row.h12"}}):n("span",[t._v(t._s(r.h12))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"上月卫贴标准",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.preMonthH1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"本月卫贴标准",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h1=Math.abs(r.h1)}},model:{value:r.h1,callback:function(e){t.$set(r,"h1",t._n(e))},expression:"row.h1"}}):n("span",[t._v(t._s(r.h1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"病假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h2=Math.abs(r.h2)}},model:{value:r.h2,callback:function(e){t.$set(r,"h2",t._n(e))},expression:"row.h2"}}):n("span",[t._v(t._s(r.h2))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"事假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h3=Math.abs(r.h3)}},model:{value:r.h3,callback:function(e){t.$set(r,"h3",t._n(e))},expression:"row.h3"}}):n("span",[t._v(t._s(r.h3))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"产假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h4=Math.abs(r.h4)}},model:{value:r.h4,callback:function(e){t.$set(r,"h4",t._n(e))},expression:"row.h4"}}):n("span",[t._v(t._s(r.h4))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h5=Math.abs(r.h5)}},model:{value:r.h5,callback:function(e){t.$set(r,"h5",t._n(e))},expression:"row.h5"}}):n("span",[t._v(t._s(r.h5))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h6=Math.abs(r.h6)}},model:{value:r.h6,callback:function(e){t.$set(r,"h6",t._n(e))},expression:"row.h6"}}):n("span",[t._v(t._s(r.h6))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"计生假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h7=Math.abs(r.h7)}},model:{value:r.h7,callback:function(e){t.$set(r,"h7",t._n(e))},expression:"row.h7"}}):n("span",[t._v(t._s(r.h7))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h8=Math.abs(r.h8)}},model:{value:r.h8,callback:function(e){t.$set(r,"h8",t._n(e))},expression:"row.h8"}}):n("span",[t._v(t._s(r.h8))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h9=Math.abs(r.h9)}},model:{value:r.h9,callback:function(e){t.$set(r,"h9",t._n(e))},expression:"row.h9"}}):n("span",[t._v(t._s(r.h9))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h10=Math.abs(r.h10)}},model:{value:r.h10,callback:function(e){t.$set(r,"h10",t._n(e))},expression:"row.h10"}}):n("span",[t._v(t._s(r.h10))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return r.h11=Math.abs(r.h11)}},model:{value:r.h11,callback:function(e){t.$set(r,"h11",t._n(e))},expression:"row.h11"}}):n("span",[t._v(t._s(r.h11))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"当前考勤员状态",align:"center",width:"110"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.statue))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"当前防保科状态",align:"center",width:"110"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.prophylacticStatusDesc))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"操作",width:"170"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.confirmEdit(r)}}},[t._v(" 更新 ")]):t._e(),r.edit?n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.cancelEdit(r)}}},[t._v(" 取消 ")]):t._e(),r.edit?t._e():n("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(e){return t.Edit(r)}}},[t._v(" 编辑 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},o=[],a=(n("99af"),n("d81d"),n("d3b7"),n("25f0"),n("4d90"),n("d368")),i=n("cbd2"),l={components:{},data:function(){return{headModel:{recordMonth:this.getNowTime()},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,treeExpandedKeys:[],currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:10},listLoading:!1,tableData:[],editOringinData:{}}},created:function(){this.loadTree()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth();n=n.toString().padStart(2,"0"),"00"===n&&(e-=1,n="12");var r="".concat(e,"-").concat(n);return r},loadTree:function(){var t=this;a["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){},Edit:function(t){t.edit=!t.edit},cancelEdit:function(t){t.edit=!1,t.h1=t.originalH1,t.h2=t.originalH2,t.h3=t.originalH3,t.h4=t.originalH4,t.h5=t.originalH5,t.h6=t.originalH6,t.h7=t.originalH7,t.h8=t.originalH8,t.h9=t.originalH9,t.h10=t.originalH10,t.h11=t.originalH11,t.h12=t.originalH12,t.historyH12=t.originalHistoryH12},confirmEdit:function(t){var e=this,n=this;t.edit=!1,t.recordMonth=this.headModel.recordMonth,i["a"].updateAttDayOffRecordDetail(t).then((function(r){if(r.succeed){var o=r.data;t.id=o.id,t.recordId=o.recordId,n.$set(t,"statue",o.statue),n.$set(t,"prophylacticStatusDesc",o.prophylacticStatusDesc),t.updator=o.updator,t.originalH1=o.h1,t.originalH2=o.h2,t.originalH3=o.h3,t.originalH4=o.h4,t.originalH5=o.h5,t.originalH6=o.h6,t.originalH7=o.h7,t.originalH8=o.h8,t.originalH9=o.h9,t.originalH10=o.h10,t.originalH11=o.h11,t.originalH12=o.h12,t.originalHistoryH12=o.historyH12}else e.$notice.resultTip(r)})).catch((function(t){e.getSearchResult(),console.log(t)}))},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},getSearchResult:function(){var t=this,e={RecordMonth:this.headModel.recordMonth,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,OnlyDayOffValue:this.headModel.onlyDayOffValue,OnlyH1Value:this.headModel.onlyH1Value,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};i["a"].searchAttDayOffRecordDetail_Update(e).then((function(e){t.listLoading=!1,e.succeed?(t.total=e.data.recordCount,t.tableData=e.data.datas.map((function(e){return t.$set(e,"edit",!1),e.originalH1=e.h1,e.originalH2=e.h2,e.originalH3=e.h3,e.originalH4=e.h4,e.originalH5=e.h5,e.originalH6=e.h6,e.originalH7=e.h7,e.originalH8=e.h8,e.originalH9=e.h9,e.originalH10=e.h10,e.originalH11=e.h11,e.originalH12=e.h12,e.originalHistoryH12=e.historyH12,e}))):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()}}},c=l,u=n("2877"),s=Object(u["a"])(c,r,o,!1,null,null,null);e["default"]=s.exports},cbd2:function(t,e,n){"use strict";var r=n("cfe3"),o="AttendanceManage",a=new r["a"](o);e["a"]={getAttMonthShiftRecord:function(t){return a.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return a.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return a.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return a.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return a.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return a.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return a.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return a.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return a.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return a.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return a.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return a.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return a.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return a.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return a.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return a.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return a.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return a.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return a.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return a.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return a.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return a.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return a.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return a.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return a.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return a.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return a.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return a.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return a.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return a.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return a.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return a.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return a.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return a.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return a.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return a.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return a.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return a.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return a.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return a.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return a.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return a.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return a.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return a.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return a.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return a.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return a.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return a.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return a.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return a.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return a.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return a.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return a.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return a.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return a.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return a.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return a.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return a.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return a.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return a.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return a.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},d368:function(t,e,n){"use strict";var r=n("cfe3"),o="Organization",a=new r["a"](o);e["a"]={QueryOrganizationHiddenTop:function(t){return a.get("QueryOrganizationHiddenTop",t)},QueryOrganization:function(t){return a.get("QueryOrganization",t)},QueryDepartment:function(t){return a.get("QueryDepartment",t)},GetDepartment:function(t){return a.get("GetDepartment",t)},AddDepartment:function(t){return a.post("AddDepartment",t)},UpdateDepartment:function(t){return a.post("UpdateDepartment",t)},MoveDepartment:function(t){return a.post("MoveDepartment",t)},MergeDepartment:function(t){return a.post("MergeDepartment",t)},DeleteDepartment:function(t){return a.post("DeleteDepartment",t)},queryPosition:function(t){return a.post("QueryPosition",t)},getPosition:function(t){return a.get("GetPosition",t)},addPosition:function(t){return a.post("AddPosition",t)},updatePosition:function(t){return a.post("UpdatePosition",t)},deletePosition:function(t){return a.post("DeletePosition",t)},GetStation:function(t){return a.get("GetStation",t)},AddStation:function(t){return a.post("AddStation",t)},UpdateStation:function(t){return a.post("UpdateStation",t)},DeleteStation:function(t){return a.post("DeleteStation",t)},QueryPositionStationTree:function(t){return a.get("QueryPositionStationTree",t)},AllocatePosition:function(t){return a.post("AllocatePosition",t)},DeletePositionStation:function(t){return a.post("DeletePositionStation",t)},queryDeptByUser:function(t){return a.get("QueryDeptByUser",t)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(t){return a.get("QueryTwoLevelStation",t)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(t){return a.get("QueryStationAllowance",t)},downloadStationAllowanceTemplate:function(t){return a.post("DownloadStationAllowanceTemplate",{data:t,responseType:"arraybuffer"})},importStationAllowance:function(t,e){return e.append("file",t),a.postForm("ImportStationAllowance",e)},exportStationAllowance:function(t){return a.post("ExportStationAllowance",{data:t,responseType:"arraybuffer"})},getStationAllowance:function(t){return a.get("GetStationAllowance",t)},addStationAllowance:function(t){return a.post("AddStationAllowance",t)},updateStationAllowance:function(t){return a.post("UpdateStationAllowance",t)},querySeniority:function(t){return a.get("QuerySeniority",t)},importSeniority:function(t,e){return e.append("file",t),a.postForm("ImportSeniority",e)},downloadSeniorityTemplate:function(t){return a.post("DownloadSeniorityTemplate",{data:t,responseType:"arraybuffer"})},exportSeniority:function(t){return a.post("ExportSeniority",{data:t,responseType:"arraybuffer"})},getSeniority:function(t){return a.get("GetSeniority",t)},addSeniority:function(t){return a.post("AddSeniority",t)},updateSeniority:function(t){return a.post("UpdateSeniority",t)},querySalaryScale:function(t){return a.get("QuerySalaryScale",t)},getSalaryScale:function(t){return a.get("GetSalaryScale",t)},addSalaryScale:function(t){return a.post("AddSalaryScale",t)},updateSalaryScale:function(t){return a.post("UpdateSalaryScale",t)},deleteSalaryScale:function(t){return a.post("DeleteSalaryScale",t)},importSalaryScale:function(t,e){return e.append("file",t),a.postForm("ImportSalaryScale",e)},exportSalaryScale:function(t){return a.post("ExportSalaryScale",{data:t,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(t){return a.get("QuerySalaryScaleByStationId",t)},queryStationAllowanceByStationId:function(t){return a.get("QueryStationAllowanceByStationId",t)},queryCarSubsidy:function(t){return a.get("QueryCarSubsidy",t)},getCarSubsidy:function(t){return a.get("GetCarSubsidy",t)},addCarSubsidy:function(t){return a.post("AddCarSubsidy",t)},updateCarSubsidy:function(t){return a.post("UpdateCarSubsidy",t)},deleteCarSubsidy:function(t){return a.post("DeleteCarSubsidy",t)},queryTelephoneFee:function(t){return a.get("QueryTelephoneFee",t)},getTelephoneFee:function(t){return a.get("GetTelephoneFee",t)},addTelephoneFee:function(t){return a.post("AddTelephoneFee",t)},updateTelephoneFee:function(t){return a.post("UpdateTelephoneFee",t)},deleteTelephoneFee:function(t){return a.post("DeleteTelephoneFee",t)}}}}]);