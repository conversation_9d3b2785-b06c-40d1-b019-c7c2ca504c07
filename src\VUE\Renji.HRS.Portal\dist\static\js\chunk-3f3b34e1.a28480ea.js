(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f3b34e1"],{d368:function(e,t,n){"use strict";var a=n("cfe3"),o="Organization",r=new a["a"](o);t["a"]={QueryOrganizationHiddenTop:function(e){return r.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return r.get("QueryOrganization",e)},QueryDepartment:function(e){return r.get("QueryDepartment",e)},GetDepartment:function(e){return r.get("GetDepartment",e)},AddDepartment:function(e){return r.post("AddDepartment",e)},UpdateDepartment:function(e){return r.post("UpdateDepartment",e)},MoveDepartment:function(e){return r.post("MoveDepartment",e)},MergeDepartment:function(e){return r.post("MergeDepartment",e)},DeleteDepartment:function(e){return r.post("DeleteDepartment",e)},queryPosition:function(e){return r.post("QueryPosition",e)},getPosition:function(e){return r.get("GetPosition",e)},addPosition:function(e){return r.post("AddPosition",e)},updatePosition:function(e){return r.post("UpdatePosition",e)},deletePosition:function(e){return r.post("DeletePosition",e)},GetStation:function(e){return r.get("GetStation",e)},AddStation:function(e){return r.post("AddStation",e)},UpdateStation:function(e){return r.post("UpdateStation",e)},DeleteStation:function(e){return r.post("DeleteStation",e)},QueryPositionStationTree:function(e){return r.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return r.post("AllocatePosition",e)},DeletePositionStation:function(e){return r.post("DeletePositionStation",e)},queryDeptByUser:function(e){return r.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return r.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return r.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return r.get("QuerySenioritySelect")},queryStationAllowance:function(e){return r.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return r.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),r.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return r.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return r.get("GetStationAllowance",e)},addStationAllowance:function(e){return r.post("AddStationAllowance",e)},updateStationAllowance:function(e){return r.post("UpdateStationAllowance",e)},querySeniority:function(e){return r.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),r.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return r.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return r.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return r.get("GetSeniority",e)},addSeniority:function(e){return r.post("AddSeniority",e)},updateSeniority:function(e){return r.post("UpdateSeniority",e)},querySalaryScale:function(e){return r.get("QuerySalaryScale",e)},getSalaryScale:function(e){return r.get("GetSalaryScale",e)},addSalaryScale:function(e){return r.post("AddSalaryScale",e)},updateSalaryScale:function(e){return r.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return r.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),r.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return r.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return r.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return r.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return r.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return r.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return r.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return r.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return r.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return r.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return r.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return r.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return r.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return r.post("DeleteTelephoneFee",e)}}},e47a:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.headModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"发薪月份"}},[n("el-date-picker",{attrs:{type:"month",placeholder:"请选择月份",editable:!1,clearable:!1,"value-format":"yyyy-MM"},model:{value:e.headModel.recordMonth,callback:function(t){e.$set(e.headModel,"recordMonth",t)},expression:"headModel.recordMonth"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"部门"}},[n("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},model:{value:e.headModel.dept,callback:function(t){e.$set(e.headModel,"dept",t)},expression:"headModel.dept"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工号"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empCode,callback:function(t){e.$set(e.headModel,"empCode",t)},expression:"headModel.empCode"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"员工姓名"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empName,callback:function(t){e.$set(e.headModel,"empName",t)},expression:"headModel.empName"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:""}},[n("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),n("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"显示值大于0的项"}},[n("el-checkbox",{model:{value:e.headModel.gtZeroValue,callback:function(t){e.$set(e.headModel,"gtZeroValue",t)},expression:"headModel.gtZeroValue"}})],1)],1)],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[n("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.empUid))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"150",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.empName))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"部门",align:"center",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.empDept))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"行政值班费（元）",align:"center",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.edit?n("el-input-number",{staticClass:"edit-input",attrs:{min:0,size:"small"},on:{input:function(e){return a.h2=Math.abs(a.h2)}},model:{value:a.h2,callback:function(t){e.$set(a,"h2",t)},expression:"row.h2"}}):n("span",[e._v(e._s(a.h2))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.edit?n("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(t){return e.confirmEdit(a)}}},[e._v(" 更新 ")]):e._e(),a.edit?n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.cancelEdit(a)}}},[e._v(" 取消 ")]):e._e(),a.edit?e._e():n("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.Edit(a)}}},[e._v(" 编辑 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[20,50,100],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.paginationChanged}})]},proxy:!0}])})],1)},o=[],r=(n("99af"),n("d81d"),n("d3b7"),n("25f0"),n("3ca3"),n("4d90"),n("ddb0"),n("2b3d"),n("d368")),i=n("cfe3"),l="PayrollSet",d=new i["a"](l),u={searchEmployeePayRollSalaryAddModel:function(e){return d.get("SearchEmployeePayRollSalaryAddModel",e)},updateEmployeePayRollSalaryAdd:function(e){return d.post("UpdateEmployeePayRollSalaryAdd",e)},getEmployeePayRollSalaryAddExcel:function(e){return d.getFile("GetEmployeePayRollSalaryAddExcel",e)}},s={components:{},data:function(){return{headModel:{recordMonth:this.getNowTime(),gtZeroValue:!0},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,treeExpandedKeys:[],currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],editOringinData:{}}},created:function(){this.loadTree()},methods:{getNowTime:function(){var e=new Date,t=e.getFullYear(),n=e.getMonth();n=n.toString().padStart(2,"0"),"00"===n&&(t-=1,n="12");var a="".concat(t,"-").concat(n);return a},loadTree:function(){var e=this;r["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){},Edit:function(e){e.edit=!e.edit},cancelEdit:function(e){e.edit=!1,e.h2=e.originalH2},confirmEdit:function(e){var t=this;isNaN(e.h2)?this.$message.error("请输入数值"):(e.edit=!1,e.recordMonth=this.headModel.recordMonth,u.updateEmployeePayRollSalaryAdd(e).then((function(n){if(n.succeed)var a=n.data;else t.$notice.resultTip(n);e.id=a.id,e.lastEditor=a.lastEditor,e.lastEditTime=a.lastEditTime,e.originalH2=a.h2})).catch((function(e){console.log(e)})))},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},getSearchResult:function(){var e=this;this.listLoading=!0;var t={RecordMonth:this.headModel.recordMonth,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,GtZeroValue:this.headModel.gtZeroValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};u.searchEmployeePayRollSalaryAddModel(t).then((function(t){t.succeed?(e.total=t.data.recordCount,e.tableData=t.data.datas.map((function(t){return e.$set(t,"edit",!1),t.originalH2=t.h2,t}))):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()},exportData:function(){var e={RecordMonth:this.headModel.recordMonth,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,GtZeroValue:this.headModel.gtZeroValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};u.getEmployeePayRollSalaryAddExcel(e).then((function(e){var t=new Blob([e],{type:e.type}),n="行政值班费.xlsx";if(window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(t,n);else{var a=document.createElement("a"),o=window.URL.createObjectURL(t);a.href=o,a.download=n,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(o)}}))}}},c=s,p=n("2877"),y=Object(p["a"])(c,a,o,!1,null,null,null);t["default"]=y.exports}}]);