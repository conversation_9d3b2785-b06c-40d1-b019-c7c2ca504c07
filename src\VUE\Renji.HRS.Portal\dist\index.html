<!DOCTYPE html><html><head><meta charset=utf-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon href=favicon.ico><title>仁济医院HRS</title><link href=static/css/app.e0311bc4.css rel=preload as=style><link href=static/css/chunk-elementUI.68c70ad5.css rel=preload as=style><link href=static/css/chunk-libs.3dfb7769.css rel=preload as=style><link href=static/js/app.da75fadf.js rel=preload as=script><link href=static/js/chunk-elementUI.593f16ed.js rel=preload as=script><link href=static/js/chunk-libs.33b73b74.js rel=preload as=script><link href=static/css/chunk-elementUI.68c70ad5.css rel=stylesheet><link href=static/css/chunk-libs.3dfb7769.css rel=stylesheet><link href=static/css/app.e0311bc4.css rel=stylesheet></head><body><div id=app></div><script>(function(c){function e(e){for(var u,d,f=e[0],k=e[1],t=e[2],b=0,r=[];b<f.length;b++)d=f[b],Object.prototype.hasOwnProperty.call(a,d)&&a[d]&&r.push(a[d][0]),a[d]=0;for(u in k)Object.prototype.hasOwnProperty.call(k,u)&&(c[u]=k[u]);o&&o(e);while(r.length)r.shift()();return h.push.apply(h,t||[]),n()}function n(){for(var c,e=0;e<h.length;e++){for(var n=h[e],u=!0,d=1;d<n.length;d++){var f=n[d];0!==a[f]&&(u=!1)}u&&(h.splice(e--,1),c=k(k.s=n[0]))}return c}var u={},d={runtime:0},a={runtime:0},h=[];function f(c){return k.p+"static/js/"+({"chunk-commons":"chunk-commons"}[c]||c)+"."+{"chunk-0277d8b6":"3d7ddd39","chunk-06af1cf6":"55ee395b","chunk-1389d7de":"27bde597","chunk-16c07da1":"43ea1ca0","chunk-1733542a":"d9b1a79e","chunk-17b5f6db":"eb109e37","chunk-1d1194c8":"62ad507b","chunk-201914c4":"146dbc72","chunk-2038084b":"2ddbadd6","chunk-20f65060":"ec13908c","chunk-210b7b10":"f6645c35","chunk-246be99c":"1b0b0080","chunk-2bd5dc36":"f8401441","chunk-2d0b345a":"aa29992d","chunk-2d0be366":"138acabc","chunk-1614599d":"341cf1f6","chunk-1c43aae0":"c6c2c72f","chunk-1f6c5e0d":"ec01e5e3","chunk-3f3d017c":"d48798f0","chunk-5384b60f":"9722405e","chunk-09c4cee2":"b6a72482","chunk-d1d43ee6":"71a994d8","chunk-797cd543":"189dce2a","chunk-77d3f4be":"2fb38d58","chunk-2d0c8871":"cdfed508","chunk-2d0d7855":"045e5add","chunk-3dd851fa":"70f1fcc1","chunk-42fb5610":"1906fd28","chunk-434bac75":"17d32d76","chunk-490e4f3a":"d5ca6e24","chunk-49904030":"0a6b334d","chunk-4d3614a9":"e20c7675","chunk-5981dbfd":"09d22b83","chunk-5a29bb5a":"71784c52","chunk-60795d4a":"a499c3de","chunk-67c77f90":"336705b6","chunk-67cc30b4":"999e4251","chunk-6a948198":"4dd4334d","chunk-6c6b1dc4":"424fcc2c","chunk-6e7ab977":"5365582c","chunk-711fb495":"37dcc402","chunk-7cd31563":"a7d2b164","chunk-97f4226a":"a9d79e4a","chunk-9e9c9b90":"5c5cdf13","chunk-3f3b34e1":"a28480ea","chunk-43ef787a":"7e78b999","chunk-681f446a":"3f2bba31","chunk-745a5816":"e6ae5ce4","chunk-78e69d0a":"cae83a97","chunk-4b4d25fa":"6e2ccb82","chunk-06b9bafe":"f5661d12","chunk-bac93b2e":"134e3849","chunk-d41593c6":"b8e9e87a","chunk-a59b5c86":"e69fb1bc","chunk-aaddf3d8":"a3ab284e","chunk-ae2fb0a2":"09f37691","chunk-afbb93a4":"f9842554","chunk-b7da1e8a":"0244147f","chunk-commons":"cd4a1106","chunk-ce671318":"16781983","chunk-04463465":"c04f786e","chunk-26e6d432":"e87ce7ea","chunk-2d0c5166":"b715cfe0","chunk-376bb478":"ab72104b","chunk-a796ad08":"dc198371","chunk-abbddc7e":"94fb506d","chunk-dae82f6a":"1652ddc0"}[c]+".js"}function k(e){if(u[e])return u[e].exports;var n=u[e]={i:e,l:!1,exports:{}};return c[e].call(n.exports,n,n.exports,k),n.l=!0,n.exports}k.e=function(c){var e=[],n={"chunk-06af1cf6":1,"chunk-1389d7de":1,"chunk-16c07da1":1,"chunk-1733542a":1,"chunk-1d1194c8":1,"chunk-201914c4":1,"chunk-20f65060":1,"chunk-246be99c":1,"chunk-1c43aae0":1,"chunk-1f6c5e0d":1,"chunk-5384b60f":1,"chunk-09c4cee2":1,"chunk-d1d43ee6":1,"chunk-797cd543":1,"chunk-77d3f4be":1,"chunk-3dd851fa":1,"chunk-434bac75":1,"chunk-4d3614a9":1,"chunk-5981dbfd":1,"chunk-60795d4a":1,"chunk-67cc30b4":1,"chunk-711fb495":1,"chunk-7cd31563":1,"chunk-681f446a":1,"chunk-78e69d0a":1,"chunk-4b4d25fa":1,"chunk-06b9bafe":1,"chunk-bac93b2e":1,"chunk-d41593c6":1,"chunk-commons":1,"chunk-ce671318":1,"chunk-04463465":1,"chunk-26e6d432":1,"chunk-376bb478":1,"chunk-a796ad08":1,"chunk-abbddc7e":1};d[c]?e.push(d[c]):0!==d[c]&&n[c]&&e.push(d[c]=new Promise((function(e,n){for(var u="static/css/"+({"chunk-commons":"chunk-commons"}[c]||c)+"."+{"chunk-0277d8b6":"31d6cfe0","chunk-06af1cf6":"bf62d2e8","chunk-1389d7de":"15e14b00","chunk-16c07da1":"0efa5f78","chunk-1733542a":"0cfe7532","chunk-17b5f6db":"31d6cfe0","chunk-1d1194c8":"fd9b54fc","chunk-201914c4":"84f43554","chunk-2038084b":"31d6cfe0","chunk-20f65060":"fd9b54fc","chunk-210b7b10":"31d6cfe0","chunk-246be99c":"d6aa672d","chunk-2bd5dc36":"31d6cfe0","chunk-2d0b345a":"31d6cfe0","chunk-2d0be366":"31d6cfe0","chunk-1614599d":"31d6cfe0","chunk-1c43aae0":"87b4a403","chunk-1f6c5e0d":"f668ef16","chunk-3f3d017c":"31d6cfe0","chunk-5384b60f":"38bac4d5","chunk-09c4cee2":"01738eed","chunk-d1d43ee6":"58f98417","chunk-797cd543":"51796acc","chunk-77d3f4be":"f67fc4fd","chunk-2d0c8871":"31d6cfe0","chunk-2d0d7855":"31d6cfe0","chunk-3dd851fa":"a17ef81a","chunk-42fb5610":"31d6cfe0","chunk-434bac75":"45d17427","chunk-490e4f3a":"31d6cfe0","chunk-49904030":"31d6cfe0","chunk-4d3614a9":"58224666","chunk-5981dbfd":"7ee64123","chunk-5a29bb5a":"31d6cfe0","chunk-60795d4a":"655808db","chunk-67c77f90":"31d6cfe0","chunk-67cc30b4":"a7bf35cd","chunk-6a948198":"31d6cfe0","chunk-6c6b1dc4":"31d6cfe0","chunk-6e7ab977":"31d6cfe0","chunk-711fb495":"77c6bf05","chunk-7cd31563":"0cfe7532","chunk-97f4226a":"31d6cfe0","chunk-9e9c9b90":"31d6cfe0","chunk-3f3b34e1":"31d6cfe0","chunk-43ef787a":"31d6cfe0","chunk-681f446a":"085edb9d","chunk-745a5816":"31d6cfe0","chunk-78e69d0a":"8a99e286","chunk-4b4d25fa":"67454ce2","chunk-06b9bafe":"29356763","chunk-bac93b2e":"29356763","chunk-d41593c6":"c4666115","chunk-a59b5c86":"31d6cfe0","chunk-aaddf3d8":"31d6cfe0","chunk-ae2fb0a2":"31d6cfe0","chunk-afbb93a4":"31d6cfe0","chunk-b7da1e8a":"31d6cfe0","chunk-commons":"36ae4a39","chunk-ce671318":"3d87f568","chunk-04463465":"73ad370f","chunk-26e6d432":"5816947f","chunk-2d0c5166":"31d6cfe0","chunk-376bb478":"2fa2ec00","chunk-a796ad08":"9e5e2287","chunk-abbddc7e":"e645d8c8","chunk-dae82f6a":"31d6cfe0"}[c]+".css",a=k.p+u,h=document.getElementsByTagName("link"),f=0;f<h.length;f++){var t=h[f],b=t.getAttribute("data-href")||t.getAttribute("href");if("stylesheet"===t.rel&&(b===u||b===a))return e()}var r=document.getElementsByTagName("style");for(f=0;f<r.length;f++){t=r[f],b=t.getAttribute("data-href");if(b===u||b===a)return e()}var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",o.onload=e,o.onerror=function(e){var u=e&&e.target&&e.target.src||a,h=new Error("Loading CSS chunk "+c+" failed.\n("+u+")");h.code="CSS_CHUNK_LOAD_FAILED",h.request=u,delete d[c],o.parentNode.removeChild(o),n(h)},o.href=a;var i=document.getElementsByTagName("head")[0];i.appendChild(o)})).then((function(){d[c]=0})));var u=a[c];if(0!==u)if(u)e.push(u[2]);else{var h=new Promise((function(e,n){u=a[c]=[e,n]}));e.push(u[2]=h);var t,b=document.createElement("script");b.charset="utf-8",b.timeout=120,k.nc&&b.setAttribute("nonce",k.nc),b.src=f(c);var r=new Error;t=function(e){b.onerror=b.onload=null,clearTimeout(o);var n=a[c];if(0!==n){if(n){var u=e&&("load"===e.type?"missing":e.type),d=e&&e.target&&e.target.src;r.message="Loading chunk "+c+" failed.\n("+u+": "+d+")",r.name="ChunkLoadError",r.type=u,r.request=d,n[1](r)}a[c]=void 0}};var o=setTimeout((function(){t({type:"timeout",target:b})}),12e4);b.onerror=b.onload=t,document.head.appendChild(b)}return Promise.all(e)},k.m=c,k.c=u,k.d=function(c,e,n){k.o(c,e)||Object.defineProperty(c,e,{enumerable:!0,get:n})},k.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},k.t=function(c,e){if(1&e&&(c=k(c)),8&e)return c;if(4&e&&"object"===typeof c&&c&&c.__esModule)return c;var n=Object.create(null);if(k.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:c}),2&e&&"string"!=typeof c)for(var u in c)k.d(n,u,function(e){return c[e]}.bind(null,u));return n},k.n=function(c){var e=c&&c.__esModule?function(){return c["default"]}:function(){return c};return k.d(e,"a",e),e},k.o=function(c,e){return Object.prototype.hasOwnProperty.call(c,e)},k.p="",k.oe=function(c){throw console.error(c),c};var t=window["webpackJsonp"]=window["webpackJsonp"]||[],b=t.push.bind(t);t.push=e,t=t.slice();for(var r=0;r<t.length;r++)e(t[r]);var o=b;n()})([]);</script><script src=static/js/chunk-elementUI.593f16ed.js></script><script src=static/js/chunk-libs.33b73b74.js></script><script src=static/js/app.da75fadf.js></script></body></html>