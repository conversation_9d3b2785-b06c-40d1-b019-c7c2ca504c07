(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-434bac75"],{"0bb4":function(t,e,i){"use strict";var n=i("cfe3"),s="Notice",o=new n["a"](s);e["a"]={queryMsgCompany:function(t){return o.post("QueryMsgCompany",t)},saveMsgCompany:function(t){return o.post("SaveMsgCompany",t)},deleteMsgCompany:function(t){return o.post("DeleteMsgCompany",t)},queryMsgPerson:function(t){return o.post("QueryMsgPerson",t)},saveMsgPerson:function(t){return o.post("SaveMsgPerson",t)},deleteMsgPerson:function(t){return o.post("DeleteMsgPerson",t)},getMsgReadInfoByMsgPerson:function(t){return o.get("GetMsgReadInfoByMsgPerson",t)},getEmpByDept:function(t){return o.get("GetEmpByDept",t)},queryMsgCompanyToEffective:function(t){return o.post("QueryMsgCompanyToEffective",t)},getMsgCompanyModelById:function(t){return o.get("GetMsgCompanyModelById",t)},queryMsgPersonToEffective:function(t){return o.post("QueryMsgPersonToEffective",t)},qetMsgPersonModelById:function(t){return o.get("GetMsgPersonModelById",t)}}},3467:function(t,e,i){},"691f":function(t,e,i){"use strict";var n=i("3467"),s=i.n(n);s.a},"7abe":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("el-row",{attrs:{gutter:50}},[i("el-col",{attrs:{span:6}},[i("div",{staticClass:"blockContain gg",class:{active:1==t.currentItemIndex},on:{click:function(e){return t.CheckItem(1)}}},[i("div",{staticClass:"svgContainer"},[i("svg-icon",{staticClass:"svg",attrs:{"icon-class":"公告_home"}}),i("div",{staticClass:"svg_t"},[i("span",[t._v("公告")])])],1)])]),i("el-col",{attrs:{span:6}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],staticClass:"blockContain gztx",class:{active:2==t.currentItemIndex},on:{click:function(e){return t.CheckItem(2)}}},[i("div",{staticClass:"svgContainer"},[i("svg-icon",{staticClass:"svg",attrs:{"icon-class":"工作提醒_home"}}),i("div",{staticClass:"svg_t"},[i("span",[t._v("工作提醒")])])],1)])]),i("el-col",{attrs:{span:6}},[i("div",{staticClass:"blockContain wdxx",class:{active:3==t.currentItemIndex},on:{click:function(e){return t.CheckItem(3)}}},[i("div",{staticClass:"svgContainer"},[i("svg-icon",{staticClass:"svg",attrs:{"icon-class":"我的消息_home"}}),i("div",{staticClass:"svg_t"},[i("span",[t._v("我的消息")])])],1)])]),i("el-col",{attrs:{span:6}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],staticClass:"blockContain wdrw",class:{active:4==t.currentItemIndex},on:{click:function(e){return t.CheckItem(4)}}},[i("div",{staticClass:"svgContainer"},[i("svg-icon",{staticClass:"svg",attrs:{"icon-class":"我的任务_home"}}),i("div",{staticClass:"svg_t"},[i("span",[t._v("我的任务")])])],1)])])],1),1==t.currentItemIndex?i("div",{staticClass:"detailContainer ggDetailContainer"},[i("div",{staticClass:"t"},[i("div",{staticClass:"t_right"},[i("span",{staticClass:"more",on:{click:t.more1}},[t._v("更多>")])]),i("div",{staticClass:"t_left"},[i("span",[i("svg-icon",{attrs:{"icon-class":"竖线"}}),t._v("公告")],1)])]),i("div",[i("ul",t._l(t.listObj,(function(e){return i("li",{key:e.id},[t._v(t._s(e.title))])})),0)])]):2==t.currentItemIndex?i("div",{staticClass:"detailContainer gztxDetailContainer"},[i("div",{staticClass:"t"},[t._m(0),i("div",{staticClass:"t_left"},[i("span",[i("svg-icon",{attrs:{"icon-class":"竖线"}}),t._v("工作提醒")],1)])]),i("div",[i("ul",t._l(t.listObj,(function(e,n){return i("li",{key:n},[t._v(" "+t._s(e.contnet)+"---------"+t._s(e.date)+" ")])})),0)])]):3==t.currentItemIndex?i("div",{staticClass:"detailContainer wdxxDetailContainer"},[i("div",{staticClass:"t"},[i("div",{staticClass:"t_right"},[i("span",{staticClass:"more",on:{click:t.more3}},[t._v("更多>")])]),i("div",{staticClass:"t_left"},[i("span",[i("svg-icon",{attrs:{"icon-class":"竖线"}}),t._v("我的消息")],1)])]),i("div",[i("ul",t._l(t.listObj,(function(e){return i("li",{key:e.id},[t._v(t._s(e.title))])})),0)])]):4==t.currentItemIndex?i("div",{staticClass:"detailContainer wdrwDetailContainer"},[i("div",{staticClass:"t"},[t._m(1),i("div",{staticClass:"t_left"},[i("span",[i("svg-icon",{attrs:{"icon-class":"竖线"}}),t._v("我的任务")],1)])]),i("div",[i("ul",t._l(t.listObj,(function(e,n){return i("li",{key:n},[t._v(" "+t._s(e.contnet)+"---------"+t._s(e.date)+" ")])})),0)])]):t._e(),i("el-dialog",{attrs:{title:"部门信息列表",width:"60%",visible:t.dialogVisibleList1},on:{"update:visible":function(e){t.dialogVisibleList1=e},close:t.close1}},[i("el-form",{ref:"ref_searchFrom1",attrs:{inline:!0,model:t.listQuery1}},[i("el-form-item",[i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择字段"},model:{value:t.listQuery1.entityColumn,callback:function(e){t.$set(t.listQuery1,"entityColumn",e)},expression:"listQuery1.entityColumn"}},t._l(t.dataColumns,(function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t}})})),1)],1),i("el-form-item",[i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:t.listQuery1.queryCondition.EnumOperation,callback:function(e){t.$set(t.listQuery1.queryCondition,"EnumOperation",e)},expression:"listQuery1.queryCondition.EnumOperation"}},t._l(t.selectConditionOptions,(function(t){return i("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1)],1),i("el-form-item",[i("el-input",{attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:t.listQuery1.queryCondition.Keywords,callback:function(e){t.$set(t.listQuery1.queryCondition,"Keywords",e)},expression:"listQuery1.queryCondition.Keywords"}})],1),i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.search1(!1)}}},[t._v("查询")])],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading1,expression:"listLoading1"}],ref:"tableList1",staticStyle:{width:"100%"},attrs:{data:t.pageList1,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":t.sortChange1}},[i("el-table-column",{attrs:{label:"标题",sortable:"custom",prop:"title"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[i("span",[i("el-link",{attrs:{type:"primary"},on:{click:function(e){return t.oponinfo1(n.id)}}},[t._v(t._s(n.title))])],1)]}}])}),i("el-table-column",{attrs:{label:"有效期开始时间",sortable:"custom",prop:"availStartDate"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[i("span",[t._v(t._s(n.availStartDate?new Date(n.availStartDate).Format("yyyy-MM-dd"):""))])]}}])}),i("el-table-column",{attrs:{label:"有效期结束时间",sortable:"custom",prop:"availEndDate"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[i("span",[t._v(t._s(n.availEndDate?new Date(n.availEndDate).Format("yyyy-MM-dd"):""))])]}}])})],1),i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.listQuery1.total>0,expression:"listQuery1.total > 0"}],attrs:{total:t.listQuery1.total,"page-sizes":[10,20,50],page:t.listQuery1.pageIndex,limit:t.listQuery1.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery1,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery1,"pageSize",e)},pagination:t.getPageList1}})],1),i("el-dialog",{attrs:{title:"通知",width:"60%",visible:t.dialogVisibleInfo1},on:{"update:visible":function(e){t.dialogVisibleInfo1=e},close:t.closeinfo1}},[i("el-form",{ref:"ref_info1",attrs:{model:t.info1,"label-width":"120px"}},[i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"有效期开始时间"}},[i("el-date-picker",{attrs:{readonly:!0,type:"date"},model:{value:t.info1.availStartDate,callback:function(e){t.$set(t.info1,"availStartDate",e)},expression:"info1.availStartDate"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"有效期结束时间"}},[i("el-date-picker",{attrs:{readonly:!0,type:"date"},model:{value:t.info1.availEndDate,callback:function(e){t.$set(t.info1,"availEndDate",e)},expression:"info1.availEndDate"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"发布人"}},[i("el-input",{attrs:{readonly:!0},model:{value:t.info1.creatorName,callback:function(e){t.$set(t.info1,"creatorName",e)},expression:"info1.creatorName"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"标题"}},[i("el-input",{attrs:{readonly:!0},model:{value:t.info1.title,callback:function(e){t.$set(t.info1,"title",e)},expression:"info1.title"}})],1)],1)],1),i("el-row",[i("el-col",[i("el-form-item",{attrs:{label:"内容"}},[i("el-input",{attrs:{readonly:!0,type:"textarea",rows:5},model:{value:t.info1.content,callback:function(e){t.$set(t.info1,"content",e)},expression:"info1.content"}})],1)],1)],1)],1)],1),i("el-dialog",{attrs:{title:"员工信息列表",width:"60%",visible:t.dialogVisibleList3},on:{"update:visible":function(e){t.dialogVisibleList3=e},close:t.close3}},[i("el-form",{ref:"ref_searchFrom3",attrs:{inline:!0,model:t.listQuery3}},[i("el-form-item",[i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择字段"},model:{value:t.listQuery3.entityColumn,callback:function(e){t.$set(t.listQuery3,"entityColumn",e)},expression:"listQuery3.entityColumn"}},t._l(t.dataColumns,(function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t}})})),1)],1),i("el-form-item",[i("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:t.listQuery3.queryCondition.EnumOperation,callback:function(e){t.$set(t.listQuery3.queryCondition,"EnumOperation",e)},expression:"listQuery3.queryCondition.EnumOperation"}},t._l(t.selectConditionOptions,(function(t){return i("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1)],1),i("el-form-item",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:t.listQuery3.queryCondition.Keywords,callback:function(e){t.$set(t.listQuery3.queryCondition,"Keywords",e)},expression:"listQuery3.queryCondition.Keywords"}})],1),i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.search3(!1)}}},[t._v("查询")])],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading3,expression:"listLoading3"}],ref:"tableList1",staticStyle:{width:"100%"},attrs:{data:t.pageList3,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":t.sortChange3}},[i("el-table-column",{attrs:{label:"标题",sortable:"custom",prop:"title"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[i("span",[i("el-link",{attrs:{type:"primary"},on:{click:function(e){return t.oponinfo3(n.id)}}},[t._v(t._s(n.title))])],1)]}}])}),i("el-table-column",{attrs:{label:"有效期开始时间",sortable:"custom",prop:"availStartDate"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[i("span",[t._v(t._s(n.availStartDate?new Date(n.availStartDate).Format("yyyy-MM-dd"):""))])]}}])}),i("el-table-column",{attrs:{label:"有效期结束时间",sortable:"custom",prop:"availEndDate"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[i("span",[t._v(t._s(n.availEndDate?new Date(n.availEndDate).Format("yyyy-MM-dd"):""))])]}}])})],1),i("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.listQuery3.total>0,expression:"listQuery3.total > 0"}],attrs:{total:t.listQuery3.total,"page-sizes":[10,20,50],page:t.listQuery3.pageIndex,limit:t.listQuery3.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery3,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery3,"pageSize",e)},pagination:t.getPageList3}})],1),i("el-dialog",{attrs:{title:"通知",width:"60%",visible:t.dialogVisibleInfo3},on:{"update:visible":function(e){t.dialogVisibleInfo3=e},close:t.closeinfo3}},[i("el-form",{ref:"ref_info3",attrs:{model:t.info3,"label-width":"120px"}},[i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"有效期开始时间"}},[i("el-date-picker",{attrs:{readonly:!0,type:"date"},model:{value:t.info3.availStartDate,callback:function(e){t.$set(t.info3,"availStartDate",e)},expression:"info3.availStartDate"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"有效期结束时间"}},[i("el-date-picker",{attrs:{readonly:!0,type:"date"},model:{value:t.info3.availEndDate,callback:function(e){t.$set(t.info3,"availEndDate",e)},expression:"info3.availEndDate"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"发布人"}},[i("el-input",{attrs:{readonly:!0},model:{value:t.info3.creatorName,callback:function(e){t.$set(t.info3,"creatorName",e)},expression:"info3.creatorName"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"标题"}},[i("el-input",{attrs:{readonly:!0},model:{value:t.info3.title,callback:function(e){t.$set(t.info3,"title",e)},expression:"info3.title"}})],1)],1)],1),i("el-row",[i("el-col",[i("el-form-item",{attrs:{label:"内容"}},[i("el-input",{attrs:{readonly:!0,type:"textarea",rows:5},model:{value:t.info3.content,callback:function(e){t.$set(t.info3,"content",e)},expression:"info3.content"}})],1)],1)],1)],1)],1)],1)},s=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"t_right"},[i("span",{staticClass:"more"},[t._v("更多>")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"t_right"},[i("span",{staticClass:"more"},[t._v("更多>")])])}],o=(i("d3b7"),i("f9ac")),a=i("0bb4"),r={data:function(){return{info3:{},dialogVisibleInfo3:!1,dialogVisibleList3:!1,pageList3:[],listLoading3:!1,info1:{},dialogVisibleInfo1:!1,dialogVisibleList1:!1,pageList1:[],listLoading1:!1,selectConditionOptions:[],dataColumns:[{value:"1",label:"标题",type:"System.String",columnName:"Title"},{value:"2",label:"有效期开始时间",type:"System.DateTime",columnName:"AvailStartDate"},{value:"3",label:"有效期结束时间",type:"System.DateTime",columnName:"AvailEndDate"}],currentItemIndex:1,ggBadge:10,gztxBadge:0,wdxxBadge:3,wdrwBadge:4,listObj:[],listQuery1:{queryCondition:{},total:1,pageIndex:1,pageSize:10},listQuery3:{queryCondition:{},total:1,pageIndex:1,pageSize:10}}},watch:{currentItemIndex:function(t,e){switch(t){case 1:this.queryMsgCompanyMothod();break;case 2:break;case 3:this.queryMsgPersonMothod();break;case 4:break;default:break}}},created:function(){this.queryMsgCompanyMothod(),this.loadConditions()},methods:{closeinfo1:function(){this.dialogVisibleInfo1=!1},closeinfo3:function(){this.dialogVisibleInfo3=!1},oponinfo1:function(t){var e=this;this.dialogVisibleInfo1=!0,a["a"].getMsgCompanyModelById({id:t}).then((function(t){e.info1=t.data}))},oponinfo3:function(t){var e=this;this.dialogVisibleInfo3=!0,a["a"].qetMsgPersonModelById({id:t}).then((function(t){e.info3=t.data}))},close1:function(){this.dialogVisibleList1=!1,this.listQuery1.entityColumn={},this.listQuery1.queryCondition={}},close3:function(){this.dialogVisibleList3=!1,this.listQuery3.entityColumn={},this.listQuery3.queryCondition={}},more1:function(){this.dialogVisibleList1=!0,this.search1()},more3:function(){this.dialogVisibleList3=!0,this.search3()},getPageList1:function(){var t=this;this.listLoading1=!0,this.listQuery1.entityColumn&&""!==this.listQuery1.entityColumn&&this.listQuery1.queryCondition.EnumOperation?(this.listQuery1.queryCondition.Keywords||(this.listQuery1.queryCondition.Keywords=""),this.listQuery1.queryCondition.EntityColumnName=this.listQuery1.entityColumn.columnName,this.listQuery1.queryCondition.EntityColumnType=this.listQuery1.entityColumn.type,this.listQuery1.queryCondition.EnumLogicRelationship=10,this.listQuery1.ConditionList=[this.listQuery1.queryCondition]):(this.listQuery1.ConditionList=[],this.listQuery1.queryCondition={}),a["a"].queryMsgCompanyToEffective(this.listQuery1).then((function(e){e.succeed?(t.pageList1=e.data.datas,t.listQuery1.total=e.data.recordCount,t.listQuery1.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(t){console.log(t)})).finally((function(){t.listLoading1=!1}))},getPageList3:function(){var t=this;this.listLoading3=!0,this.listQuery3.entityColumn&&""!==this.listQuery3.entityColumn&&this.listQuery3.queryCondition.EnumOperation?(this.listQuery3.queryCondition.Keywords||(this.listQuery3.queryCondition.Keywords=""),this.listQuery3.queryCondition.EntityColumnName=this.listQuery3.entityColumn.columnName,this.listQuery3.queryCondition.EntityColumnType=this.listQuery3.entityColumn.type,this.listQuery3.queryCondition.EnumLogicRelationship=10,this.listQuery3.ConditionList=[this.listQuery3.queryCondition]):(this.listQuery3.ConditionList=[],this.listQuery3.queryCondition={}),a["a"].queryMsgPersonToEffective(this.listQuery3).then((function(e){e.succeed?(t.pageList3=e.data.datas,t.listQuery3.total=e.data.recordCount,t.listQuery3.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(t){console.log(t)})).finally((function(){t.listLoading3=!1}))},search1:function(){this.listQuery1.pageIndex=1,this.getPageList1()},search3:function(){this.listQuery3.pageIndex=1,this.getPageList3()},sortChange1:function(t){"ascending"===t.order?this.listQuery1.order=t.prop+" asc":"descending"===t.order?this.listQuery1.order=t.prop+" desc":this.listQuery1.order="",this.search1()},sortChange3:function(t){"ascending"===t.order?this.listQuery3.order=t.prop+" asc":"descending"===t.order?this.listQuery3.order=t.prop+" desc":this.listQuery3.order="",this.search1()},loadConditions:function(){var t=this;o["a"].getEnumInfos({enumType:"Operations"}).then((function(e){t.selectConditionOptions=e.data.datas})).catch((function(t){console.log(t)}))},CheckItem:function(t){this.currentItemIndex=t},queryMsgCompanyMothod:function(){var t=this;a["a"].queryMsgCompanyToEffective({pageindex:1,pagesize:8}).then((function(e){t.listObj=e.data.datas}))},queryMsgPersonMothod:function(){var t=this;a["a"].queryMsgPersonToEffective({pageindex:1,pagesize:8}).then((function(e){t.listObj=e.data.datas}))}}},l=r,u=(i("691f"),i("2877")),c=Object(u["a"])(l,n,s,!1,null,"c6e732e2",null);e["default"]=c.exports},f9ac:function(t,e,i){"use strict";var n=i("cfe3"),s="SysManage",o=new n["a"](s);e["a"]={queryDict:function(t){return o.get("QueryDict",t)},queryDictType:function(t){return o.post("QueryDictType",t)},addDict:function(t){return o.post("AddDict",t)},deleteDict:function(t){return o.post("DeleteDict",t)},updateDict:function(t){return o.post("UpdateDict",t)},getDict:function(t){return o.get("GetDict",t)},querySysSetting:function(t){return o.get("QuerySysSetting",t)},addSysSetting:function(t){return o.post("AddSysSetting",t)},deleteSysSetting:function(t){return o.post("DeleteSysSetting",t)},updateSysSetting:function(t){return o.post("UpdateSysSetting",t)},getSysSetting:function(t){return o.get("GetSysSetting",t)},queryLanguage:function(t){return o.get("QueryLanguage",t)},getEnumInfos:function(t){return o.get("GetEnumInfos",t)},queryUserGroups:function(t){return o.post("QueryUserGroups",t)},saveUserGroup:function(t){return o.post("SaveUserGroup",t)},deleteUserGroup:function(t){return o.post("DeleteUserGroup",t)},dropdownUserGroups:function(t){return o.get("DropdownUserGroups",t)},queryUsers:function(t){return o.post("QueryUsers",t)},saveUser:function(t){return o.post("SaveUser",t)},deleteUser:function(t){return o.post("DeleteUser",t)},initPwd:function(t){return o.post("InitPwd",t)},getUserById:function(t){return o.get("GetUserById",t)},queryEmployees:function(t){return o.post("QueryEmployees",t)},queryModuleInfos:function(t){return o.get("QueryModuleInfos",t)},getRightSettingByUserGroup:function(t){return o.get("GetRightSettingByUserGroup",t)},saveRightSetting:function(t){return o.post("SaveRightSetting",t)},getRightOfDeptByUserGroup:function(t){return o.get("GetRightOfDeptByUserGroup",t)},saveRightOfDept:function(t){return o.post("SaveRightOfDept",t)},queryControlRight:function(t){return o.post("QueryControlRight",t)},saveControlRights:function(t){return o.post("SaveControlRights",t)},getControlRightByCurrentUser:function(t){return o.get("GetControlRightByCurrentUser",t)},queryStationTree:function(t){return o.get("QueryStationTree",t)},queryStationTypeSelector:function(){return o.get("QueryStationTypeSelector")},queryStationSelector:function(t){return o.get("QueryStationSelector",t)},querySalaryScaleSelector:function(t){return o.get("QuerySalaryScaleSelector",t)},queryTelephoneFeeSelector:function(){return o.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return o.get("QueryCarSubsidySelector")},queryStationAllowance:function(t){return o.get("QueryStationAllowance",t)}}}}]);