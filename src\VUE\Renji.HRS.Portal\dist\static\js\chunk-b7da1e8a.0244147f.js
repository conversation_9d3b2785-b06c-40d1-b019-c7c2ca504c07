(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b7da1e8a"],{"06c5":function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));n("a630"),n("fb6a"),n("b0c0"),n("d3b7"),n("25f0"),n("3ca3");var r=n("6b75");function o(t,e){if(t){if("string"===typeof t)return Object(r["a"])(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(r["a"])(t,e):void 0}}},3835:function(t,e,n){"use strict";function r(t){if(Array.isArray(t))return t}n.d(e,"a",(function(){return l}));n("a4d3"),n("e01a"),n("d28b"),n("d3b7"),n("3ca3"),n("ddb0");function o(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var n=[],r=!0,o=!1,a=void 0;try{for(var i,l=t[Symbol.iterator]();!(r=(i=l.next()).done);r=!0)if(n.push(i.value),e&&n.length===e)break}catch(c){o=!0,a=c}finally{try{r||null==l["return"]||l["return"]()}finally{if(o)throw a}}return n}}var a=n("06c5");function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){return r(t)||o(t,e)||Object(a["a"])(t,e)||i()}},"6b75":function(t,e,n){"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}n.d(e,"a",(function(){return r}))},"7b13":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"月份"}},[n("el-date-picker",{attrs:{type:"month",placeholder:"请选择月份",editable:!1,clearable:!1,"value-format":"yyyy-MM"},model:{value:t.headModel.recordMonth,callback:function(e){t.$set(t.headModel,"recordMonth",e)},expression:"headModel.recordMonth"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"部门"}},[n("c-select-tree",{attrs:{options:t.treeData,"tree-props":t.treeProps},model:{value:t.headModel.dept,callback:function(e){t.$set(t.headModel,"dept",e)},expression:"headModel.dept"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工号"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empCode,callback:function(e){t.$set(t.headModel,"empCode",e)},expression:"headModel.empCode"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"姓名"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empName,callback:function(e){t.$set(t.headModel,"empName",e)},expression:"headModel.empName"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:""}},[n("el-button",{attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"显示值大于0的项"}},[n("el-checkbox",{model:{value:t.headModel.gtZeroValue,callback:function(e){t.$set(t.headModel,"gtZeroValue",e)},expression:"headModel.gtZeroValue"}})],1)],1)],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[n("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"60",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empUid))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"60",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"60",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empName))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"部门",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.empDept))])]}}])}),n("el-table-column",{attrs:{label:"班次",align:"center"}},[n("el-table-column",{attrs:{prop:"name",label:"中班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.zb=Math.abs(r.zb)}},model:{value:r.zb,callback:function(e){t.$set(r,"zb",t._n(e))},expression:"row.zb"}}):n("span",[t._v(t._s(r.zb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"夜班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.yb=Math.abs(r.yb)}},model:{value:r.yb,callback:function(e){t.$set(r,"yb",t._n(e))},expression:"row.yb"}}):n("span",[t._v(t._s(r.yb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"24小时班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.b24=Math.abs(r.b24)}},model:{value:r.b24,callback:function(e){t.$set(r,"b24",t._n(e))},expression:"row.b24"}}):n("span",[t._v(t._s(r.b24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊中班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.jzzb=Math.abs(r.jzzb)}},model:{value:r.jzzb,callback:function(e){t.$set(r,"jzzb",t._n(e))},expression:"row.jzzb"}}):n("span",[t._v(t._s(r.jzzb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊夜班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.jzyb=Math.abs(r.jzyb)}},model:{value:r.jzyb,callback:function(e){t.$set(r,"jzyb",t._n(e))},expression:"row.jzyb"}}):n("span",[t._v(t._s(r.jzyb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"急诊24小时",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.jZ24=Math.abs(r.jZ24)}},model:{value:r.jZ24,callback:function(e){t.$set(r,"jZ24",t._n(e))},expression:"row.jZ24"}}):n("span",[t._v(t._s(r.jZ24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档中班",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.hlazb=Math.abs(r.hlazb)}},model:{value:r.hlazb,callback:function(e){t.$set(r,"hlazb",t._n(e))},expression:"row.hlazb"}}):n("span",[t._v(t._s(r.hlazb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档夜班",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.hlayb=Math.abs(r.hlayb)}},model:{value:r.hlayb,callback:function(e){t.$set(r,"hlayb",t._n(e))},expression:"row.hlayb"}}):n("span",[t._v(t._s(r.hlayb))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"护理A档24小时",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.hlA24=Math.abs(r.hlA24)}},model:{value:r.hlA24,callback:function(e){t.$set(r,"hlA24",t._n(e))},expression:"row.hlA24"}}):n("span",[t._v(t._s(r.hlA24))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"其他值班1",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.qT1=Math.abs(r.qT1)}},model:{value:r.qT1,callback:function(e){t.$set(r,"qT1",t._n(e))},expression:"row.qT1"}}):n("span",[t._v(t._s(r.qT1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"其他值班2",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-input",{staticClass:"edit-input",attrs:{type:"number",min:"0",size:"mini"},on:{input:function(t){return r.qT2=Math.abs(r.qT2)}},model:{value:r.qT2,callback:function(e){t.$set(r,"qT2",t._n(e))},expression:"row.qT2"}}):n("span",[t._v(t._s(r.qT2))])]}}])})],1),n("el-table-column",{attrs:{prop:"name",label:"修改人",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[n("span",[t._v(t._s(r.updator))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"操作",width:"170"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[r.edit?n("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.confirmEdit(r)}}},[t._v(" 更新 ")]):t._e(),r.edit?n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.cancelEdit(r)}}},[t._v(" 取消 ")]):t._e(),r.edit?t._e():n("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(e){return t.Edit(r)}}},[t._v(" 编辑 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},o=[],a=(n("99af"),n("d81d"),n("a9e3"),n("d3b7"),n("ac1f"),n("25f0"),n("4d90"),n("1276"),n("3835")),i=n("d368"),l=n("cbd2"),c={components:{},data:function(){return{headModel:{recordMonth:this.getNowTime(),gtZeroValue:!0},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,treeExpandedKeys:[],currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],editOringinData:{}}},created:function(){this.loadTree()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth();n=n.toString().padStart(2,"0"),"00"===n&&(e-=1,n="12");var r="".concat(e,"-").concat(n);return r},loadTree:function(){var t=this;i["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){},Edit:function(t){t.edit=!t.edit},cancelEdit:function(t){t.edit=!1,t.zb=t.originalZb,t.yb=t.originalYb,t.b24=t.originalB24,t.jzzb=t.originalJZZB,t.jzyb=t.originalJZYB,t.jZ24=t.originalJZ24,t.hlazb=t.originalHLAZB,t.hlayb=t.originalHLAYB,t.hlA24=t.originalHLA24,t.qT1=t.oringinQT1,t.qT2=t.oringinQT2},confirmEdit:function(t){t.recordMonth=this.headModel.recordMonth;var e=t.zb||0,n=t.yb||0,r=t.b24||0,o=t.jzzb||0,i=t.jzyb||0,l=t.jZ24||0,c=t.hlazb||0,u=t.hlayb||0,d=t.hlA24||0,s=t.qT1||0,p=t.qT2||0,f=e+n+r+o+i+l+c+u+d+s+p,y=this.headModel.recordMonth+"-01",h=y.split("-").map(Number),b=Object(a["a"])(h,2),g=b[0],m=b[1],S=new Date(g,m,0),A=S.getDate();f>A?this.$message.error("超过当月最大天数，请重新输入！"):this.update(t)},update:function(t){var e=this;t.edit=!1,t.recordMonth=this.headModel.recordMonth,l["a"].updateAttMonthShiftRecordDetail(t).then((function(n){if(n.succeed){var r=n.data;t.updator=r.updator,t.originalZb=r.zb,t.originalYb=r.yb,t.originalB24=r.b24,t.originalJZZB=r.jzzb,t.originalJZYB=r.jzyb,t.originalJZ24=r.jZ24,t.originalHLAZB=r.hlazb,t.originalHLAYB=r.hlayb,t.originalHLA24=r.hlA24,t.oringinQT1=r.qT1,t.oringinQT2=r.qT2}else e.$notice.resultTip(n)})).catch((function(t){console.log(t),e.getSearchResult()}))},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},getSearchResult:function(){var t=this,e={RecordMonth:this.headModel.recordMonth,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,GtZeroValue:this.headModel.gtZeroValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};l["a"].searchAttMonthShiftRecordDetail_Update(e).then((function(e){t.listLoading=!1,e.succeed?(t.total=e.data.recordCount,t.tableData=e.data.datas.map((function(e){return t.$set(e,"edit",!1),e.originalZb=e.zb,e.originalYb=e.yb,e.originalB24=e.b24,e.originalJZZB=e.jzzb,e.originalJZYB=e.jzyb,e.originalJZ24=e.jZ24,e.originalHLAZB=e.hlazb,e.originalHLAYB=e.hlayb,e.originalHLA24=e.hlA24,e.oringinQT1=e.qT1,e.oringinQT2=e.qT2,e}))):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()}}},u=c,d=n("2877"),s=Object(d["a"])(u,r,o,!1,null,null,null);e["default"]=s.exports},cbd2:function(t,e,n){"use strict";var r=n("cfe3"),o="AttendanceManage",a=new r["a"](o);e["a"]={getAttMonthShiftRecord:function(t){return a.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return a.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return a.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return a.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return a.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return a.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return a.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return a.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return a.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return a.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return a.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return a.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return a.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return a.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return a.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return a.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return a.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return a.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return a.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return a.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return a.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return a.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return a.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return a.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return a.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return a.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return a.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return a.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return a.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return a.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return a.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return a.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return a.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return a.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return a.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return a.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return a.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return a.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return a.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return a.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return a.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return a.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return a.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return a.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return a.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return a.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return a.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return a.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return a.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return a.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return a.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return a.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return a.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return a.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return a.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return a.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return a.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return a.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return a.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return a.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return a.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},d368:function(t,e,n){"use strict";var r=n("cfe3"),o="Organization",a=new r["a"](o);e["a"]={QueryOrganizationHiddenTop:function(t){return a.get("QueryOrganizationHiddenTop",t)},QueryOrganization:function(t){return a.get("QueryOrganization",t)},QueryDepartment:function(t){return a.get("QueryDepartment",t)},GetDepartment:function(t){return a.get("GetDepartment",t)},AddDepartment:function(t){return a.post("AddDepartment",t)},UpdateDepartment:function(t){return a.post("UpdateDepartment",t)},MoveDepartment:function(t){return a.post("MoveDepartment",t)},MergeDepartment:function(t){return a.post("MergeDepartment",t)},DeleteDepartment:function(t){return a.post("DeleteDepartment",t)},queryPosition:function(t){return a.post("QueryPosition",t)},getPosition:function(t){return a.get("GetPosition",t)},addPosition:function(t){return a.post("AddPosition",t)},updatePosition:function(t){return a.post("UpdatePosition",t)},deletePosition:function(t){return a.post("DeletePosition",t)},GetStation:function(t){return a.get("GetStation",t)},AddStation:function(t){return a.post("AddStation",t)},UpdateStation:function(t){return a.post("UpdateStation",t)},DeleteStation:function(t){return a.post("DeleteStation",t)},QueryPositionStationTree:function(t){return a.get("QueryPositionStationTree",t)},AllocatePosition:function(t){return a.post("AllocatePosition",t)},DeletePositionStation:function(t){return a.post("DeletePositionStation",t)},queryDeptByUser:function(t){return a.get("QueryDeptByUser",t)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(t){return a.get("QueryTwoLevelStation",t)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(t){return a.get("QueryStationAllowance",t)},downloadStationAllowanceTemplate:function(t){return a.post("DownloadStationAllowanceTemplate",{data:t,responseType:"arraybuffer"})},importStationAllowance:function(t,e){return e.append("file",t),a.postForm("ImportStationAllowance",e)},exportStationAllowance:function(t){return a.post("ExportStationAllowance",{data:t,responseType:"arraybuffer"})},getStationAllowance:function(t){return a.get("GetStationAllowance",t)},addStationAllowance:function(t){return a.post("AddStationAllowance",t)},updateStationAllowance:function(t){return a.post("UpdateStationAllowance",t)},querySeniority:function(t){return a.get("QuerySeniority",t)},importSeniority:function(t,e){return e.append("file",t),a.postForm("ImportSeniority",e)},downloadSeniorityTemplate:function(t){return a.post("DownloadSeniorityTemplate",{data:t,responseType:"arraybuffer"})},exportSeniority:function(t){return a.post("ExportSeniority",{data:t,responseType:"arraybuffer"})},getSeniority:function(t){return a.get("GetSeniority",t)},addSeniority:function(t){return a.post("AddSeniority",t)},updateSeniority:function(t){return a.post("UpdateSeniority",t)},querySalaryScale:function(t){return a.get("QuerySalaryScale",t)},getSalaryScale:function(t){return a.get("GetSalaryScale",t)},addSalaryScale:function(t){return a.post("AddSalaryScale",t)},updateSalaryScale:function(t){return a.post("UpdateSalaryScale",t)},deleteSalaryScale:function(t){return a.post("DeleteSalaryScale",t)},importSalaryScale:function(t,e){return e.append("file",t),a.postForm("ImportSalaryScale",e)},exportSalaryScale:function(t){return a.post("ExportSalaryScale",{data:t,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(t){return a.get("QuerySalaryScaleByStationId",t)},queryStationAllowanceByStationId:function(t){return a.get("QueryStationAllowanceByStationId",t)},queryCarSubsidy:function(t){return a.get("QueryCarSubsidy",t)},getCarSubsidy:function(t){return a.get("GetCarSubsidy",t)},addCarSubsidy:function(t){return a.post("AddCarSubsidy",t)},updateCarSubsidy:function(t){return a.post("UpdateCarSubsidy",t)},deleteCarSubsidy:function(t){return a.post("DeleteCarSubsidy",t)},queryTelephoneFee:function(t){return a.get("QueryTelephoneFee",t)},getTelephoneFee:function(t){return a.get("GetTelephoneFee",t)},addTelephoneFee:function(t){return a.post("AddTelephoneFee",t)},updateTelephoneFee:function(t){return a.post("UpdateTelephoneFee",t)},deleteTelephoneFee:function(t){return a.post("DeleteTelephoneFee",t)}}}}]);