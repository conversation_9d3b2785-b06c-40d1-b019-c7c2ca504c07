(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-commons"],{3462:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-upload",{attrs:{drag:"",action:"customize",multiple:!1,"auto-upload":e.autoUpload,accept:e.accept,"show-file-list":!0,"file-list":e.fileList,"on-preview":e.downloadFile,"http-request":e.uploadFile,"before-upload":e.beforeUpload,"on-remove":e.onRemove}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持以下文件类型："),a("br"),e._v(e._s(e.accept)+"，"),a("br"),e._v("且不超过"+e._s(e.limitSize)+"KB")])])],1)},l=[],o=(a("99af"),a("c975"),a("baa5"),a("b0c0"),a("a9e3"),a("5530")),n=a("2f62"),s=a("3f5e"),r={props:{value:{type:Object,default:function(){}},limitSize:{type:Number,default:10240},autoUpload:{type:Boolean,default:!0}},data:function(){return{fileUrl:"",fileName:"",fileList:[],accept:".jpeg,.jpg,.gif,.png,.bmp,.pdf,.docx,.doc,.xls,.xlsx,.rar,.txt"}},computed:Object(o["a"])({},Object(n["c"])({downloadFileUrl:function(e){return e.settings.downloadFileUrl}})),watch:{value:function(e){e&&(this.fileUrl="".concat(this.downloadFileUrl,"/").concat(e.id),this.fileName=e.fileName,this.fileList=[{id:e.id,name:this.fileName,url:this.fileUrl}])}},methods:{uploadFile:function(e){var t=this,a=e.file;s["a"].upload(a).then((function(e){if(e.succeed){var a=e.data;t.fileUrl="".concat(t.downloadFileUrl,"/").concat(a.id),t.fileName=a.fileName,t.fileList=[{id:a.id,name:t.fileName,url:t.fileUrl}],t.emitInput(a)}}))},beforeUpload:function(e){if(0===e.size)return this.$notice.message("请不要上传空文件","error"),!1;var t=e.size/1024<this.limitSize,a=e.name.substring(e.name.lastIndexOf(".")),i=-1!==this.accept.indexOf(a);return i?t?i&&t:(this.$notice.message("上传文件大小不能超过 ".concat(this.limitSize,"KB!"),"error"),!1):(this.$notice.message("选择支持的文件类型","error"),!1)},emitInput:function(e){this.$emit("input",e)},downloadFile:function(e){window.open(e.url)},onRemove:function(e,t){this.emitInput(void 0)}}},c=r,u=a("2877"),d=Object(u["a"])(c,i,l,!1,null,null,null);t["a"]=d.exports},"5b07":function(e,t,a){"use strict";var i=a("e510"),l=a.n(i);l.a},"5eee":function(e,t,a){},c958:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.showEmp,width:"80%",top:"5vh","append-to-body":!0,"modal-append-to-body":!1},on:{"update:visible":function(t){e.showEmp=t}}},[a("div",[a("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[a("c-tree",{attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",{attrs:{label:"员工编号",prop:"empCode"}},[a("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"displayName"}},[a("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",{attrs:{label:"性别",prop:"sex"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.listQuery.sex,callback:function(t){e.$set(e.listQuery,"sex",t)},expression:"listQuery.sex"}},e._l(e.genderDropdown,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.search()}}},[e._v("查询")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"员工编号",sortable:"custom",prop:"empCode"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.empCode))])]}}])}),a("el-table-column",{attrs:{label:"中文名",sortable:"custom",prop:"displayName"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.displayName))])]}}])}),a("el-table-column",{attrs:{label:"部门",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.deptName))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"选择",align:"left","header-align":"center",width:"320","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.selectRow(i)}}},[e._v(" 选择 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])})],1)])},l=[],o=(a("d3b7"),a("d368")),n=a("f9ac"),s={components:{},props:{},data:function(){return{showEmp:!1,listLoading:!1,treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:["1"],pageList:[],listQuery:{total:1,pageIndex:1,pageSize:10},genderDropdown:[{value:1,label:"男"},{value:2,label:"女"}]}},created:function(){this.loadTree()},methods:{treeNodeClick:function(e){this.listQuery.deptId=e.id,this.getPageList()},selectRow:function(e){this.showEmp=!1,this.$emit("selectRow",e)},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.listQuery.pageIndex=1,this.getPageList()},loadTree:function(){var e=this;o["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.listQuery.deptId=null,this.getPageList()},getPageList:function(){this.queryEmployeesMethods(this.listQuery)},queryEmployeesMethods:function(e){var t=this;this.listLoading=!0,n["a"].queryEmployees(e).then((function(e){e.succeed?(t.pageList=e.data.datas,t.listQuery.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e)})).finally((function(){t.listLoading=!1}))}}},r=s,c=a("2877"),u=Object(c["a"])(r,i,l,!1,null,"c0029c66",null);t["a"]=u.exports},e0ec:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-upload",{staticClass:"avatar-uploader",attrs:{action:"customize","http-request":e.uploadFile,"show-file-list":!1,"before-upload":e.beforeAvatarUpload}},[e.imageUrl?a("img",{staticClass:"avatar",attrs:{src:e.imageUrl}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),a("span",{staticClass:"choosePicture"},[a("i",{staticClass:"el-icon-picture"})])])],1)},l=[],o=(a("99af"),a("c975"),a("5530")),n=a("2f62"),s=a("3f5e"),r={props:{attachmentId:{type:String,default:""}},data:function(){return{imageUrl:""}},computed:Object(o["a"])({},Object(n["c"])({downloadFileUrl:function(e){return e.settings.downloadFileUrl}})),watch:{attachmentId:function(e){e&&(this.imageUrl="".concat(this.downloadFileUrl,"/").concat(e),console.log(this.imageUrl))}},methods:{uploadFile:function(e){var t=this,a=e.file;s["a"].upload(a).then((function(e){if(e.succeed){var a=e.data;t.imageUrl="".concat(t.downloadFileUrl,"/").concat(a.id),t.$emit("image",a.id),console.log("111aaa")}}))},beforeAvatarUpload:function(e){if(console.log(e),0===e.size)return this.$notice.message("请不要上传空文件","error"),!1;var t=e.size/1024/1024<2,a=-1!==e.type.indexOf("image");return a?t?a&&t:(this.$notice.message("上传头像图片大小不能超过 2MB!","error"),!1):(this.$notice.message("请选择图片文件","error"),!1)}}},c=r,u=(a("fbe0"),a("5b07"),a("2877")),d=Object(u["a"])(c,i,l,!1,null,null,null);t["a"]=d.exports},e510:function(e,t,a){},fbe0:function(e,t,a){"use strict";var i=a("5eee"),l=a.n(i);l.a}}]);