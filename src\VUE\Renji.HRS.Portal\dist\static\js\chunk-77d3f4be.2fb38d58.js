(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-77d3f4be"],{"06c5":function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));n("a630"),n("fb6a"),n("b0c0"),n("d3b7"),n("25f0"),n("3ca3");var o=n("6b75");function r(e,t){if(e){if("string"===typeof e)return Object(o["a"])(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(o["a"])(e,t):void 0}}},"19de":function(e,t){e.exports=function(e,t,n,o){var r="undefined"!==typeof o?[o,e]:[e],a=new Blob(r,{type:n||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(a,t);else{var l=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(a):window.webkitURL.createObjectURL(a),i=document.createElement("a");i.style.display="none",i.href=l,i.setAttribute("download",t),"undefined"===typeof i.download&&i.setAttribute("target","_blank"),document.body.appendChild(i),i.click(),setTimeout((function(){document.body.removeChild(i),window.URL.revokeObjectURL(l)}),200)}}},"6b75":function(e,t,n){"use strict";function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}n.d(t,"a",(function(){return o}))},"852c":function(e,t,n){"use strict";var o=n("89c8"),r=n.n(o);r.a},"89c8":function(e,t,n){},d368:function(e,t,n){"use strict";var o=n("cfe3"),r="Organization",a=new o["a"](r);t["a"]={QueryOrganizationHiddenTop:function(e){return a.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return a.get("QueryOrganization",e)},QueryDepartment:function(e){return a.get("QueryDepartment",e)},GetDepartment:function(e){return a.get("GetDepartment",e)},AddDepartment:function(e){return a.post("AddDepartment",e)},UpdateDepartment:function(e){return a.post("UpdateDepartment",e)},MoveDepartment:function(e){return a.post("MoveDepartment",e)},MergeDepartment:function(e){return a.post("MergeDepartment",e)},DeleteDepartment:function(e){return a.post("DeleteDepartment",e)},queryPosition:function(e){return a.post("QueryPosition",e)},getPosition:function(e){return a.get("GetPosition",e)},addPosition:function(e){return a.post("AddPosition",e)},updatePosition:function(e){return a.post("UpdatePosition",e)},deletePosition:function(e){return a.post("DeletePosition",e)},GetStation:function(e){return a.get("GetStation",e)},AddStation:function(e){return a.post("AddStation",e)},UpdateStation:function(e){return a.post("UpdateStation",e)},DeleteStation:function(e){return a.post("DeleteStation",e)},QueryPositionStationTree:function(e){return a.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return a.post("AllocatePosition",e)},DeletePositionStation:function(e){return a.post("DeletePositionStation",e)},queryDeptByUser:function(e){return a.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return a.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return a.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),a.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return a.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return a.get("GetStationAllowance",e)},addStationAllowance:function(e){return a.post("AddStationAllowance",e)},updateStationAllowance:function(e){return a.post("UpdateStationAllowance",e)},querySeniority:function(e){return a.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),a.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return a.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return a.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return a.get("GetSeniority",e)},addSeniority:function(e){return a.post("AddSeniority",e)},updateSeniority:function(e){return a.post("UpdateSeniority",e)},querySalaryScale:function(e){return a.get("QuerySalaryScale",e)},getSalaryScale:function(e){return a.get("GetSalaryScale",e)},addSalaryScale:function(e){return a.post("AddSalaryScale",e)},updateSalaryScale:function(e){return a.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return a.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),a.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return a.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return a.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return a.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return a.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return a.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return a.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return a.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return a.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return a.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return a.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return a.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return a.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return a.post("DeleteTelephoneFee",e)}}},e44c:function(e,t,n){"use strict";n("4160"),n("b64b"),n("159b");var o=n("cfe3"),r="HR",a=new o["a"](r);t["a"]={queryEmployee:function(e){return a.get("QueryEmployee",e)},queryEmployeeByCommonCondition:function(e){return a.post("QueryEmployeeByCommonCondition",e)},queryEmployeeStatus:function(){return a.get("QueryEmployeeStatus")},queryRank:function(){return a.get("QueryRank")},queryAdministrativePosition:function(){return a.get("queryAdministrativePosition")},queryMajorTechnical:function(){return a.get("queryMajorTechnical")},queryOfficialRank:function(){return a.get("QueryOfficialRank")},queryHireStyle:function(){return a.get("QueryHireStyle")},queryLeaveStyle:function(){return a.get("QueryLeaveStyle")},queryMarryList:function(){return a.get("QueryMarryList")},queryNationality:function(){return a.get("QueryNationality")},queryRegisterType:function(){return a.get("QueryRegisterType")},deleteEmployee:function(e){return a.post("DeleteEmployee",e)},queryDocumentType:function(){return a.get("QueryDocumentType")},addEmployee:function(e){return a.post("AddEmployee",e)},isEmpCodeExists:function(e){var t={id:e.id,empCode:e.empCode};return a.get("IsEmpCodeExists",t)},checkIdentityNumber:function(e){var t={id:e.id,identityNumber:e.identityNumber};return a.get("checkIdentityNumber",t)},getEmployee:function(e){return a.get("GetEmployee",e)},updateEmployee:function(e){return a.post("UpdateEmployee",e)},getPersonnelInformation:function(e){return a.get("GetPersonnelInformation",e)},postAddDocumentInformation:function(e){return a.get("PostAddDocumentInformation",e)},postUpdateDocumentInformation:function(e){return a.get("PostUpdateDocumentInformation",e)},postDeleteDocumentInformation:function(e){return a.get("PostDeleteDocumentInformation",e)},queryEmployeeByConditions:function(e){return a.post("QueryEmployeeByConditions",e)},queryEmployeeByCommonConditions:function(e){return a.post("QueryEmployeeByCommonConditions",e)},queryEmployeeQuerySetting:function(e){return a.get("QueryEmployeeQuerySetting",e)},querySettingColumns:function(){return a.get("QueryEmployeeInfoQuerySettingColumns")},queryOperations:function(e){return a.get("QueryOperationByColumnType",e)},addEmployeeQuerySetting:function(e){return a.post("AddEmployeeQuerySetting",e)},deleteEmployeeQuerySetting:function(e){return a.post("DeleteEmployeeQuerySetting",e)},queryDegrees:function(){return a.get("QueryDegrees")},queryEducation:function(){return a.get("QueryEducation")},QuerySocialSecurity:function(){return a.get("QuerySocialSecurity")},queryParty:function(){return a.get("QueryParty")},queryRecruitmentCategory:function(){return a.get("QueryRecruitmentCategory")},queryRecruitmentCompany:function(){return a.get("QueryRecruitmentCompany")},getEmployeeHR:function(e){return a.get("GetEmployeeHR",e)},updateEmployeeHR:function(e){return a.post("UpdateEmployeeHR",e)},updateCompanyAge:function(){return a.post("UpdateCompanyAge")},calculateGeneralHoliday:function(){return a.get("CalculateGeneralHoliday")},queryStation:function(e){return a.get("QueryStation",e)},queryPositionStation:function(e){return a.get("QueryPositionStation",e)},queryEmployeeStation:function(e){return a.get("QueryEmployeeStation",e)},addEmployeeStation:function(e){return a.post("AddEmployeeStation",e)},updateEmployeeStation:function(e){return a.post("UpdateEmployeeStation",e)},deleteEmployeeStation:function(e){return a.post("DeleteEmployeeStation",e)},queryLevel:function(){return a.get("QueryLevel")},queryEmployeeCertify:function(e){return a.get("QueryEmployeeCertify",e)},addEmployeeCertify:function(e){return a.post("AddEmployeeCertify",e)},updateEmployeeCertify:function(e){return a.post("UpdateEmployeeCertify",e)},deleteEmployeeCertify:function(e){return a.post("DeleteEmployeeCertify",e)},queryGraduation:function(){return a.get("QueryGraduation")},queryLearnWay:function(){return a.get("QueryLearnWay")},queryEmployeeEducation:function(e){return a.get("QueryEmployeeEducation",e)},addEmployeeEducation:function(e){return a.post("AddEmployeeEducation",e)},updateEmployeeEducation:function(e){return a.post("UpdateEmployeeEducation",e)},deleteEmployeeEducation:function(e){return a.post("DeleteEmployeeEducation",e)},queryEmployeeWork:function(e){return a.get("QueryEmployeeWork",e)},addEmployeeWork:function(e){return a.post("AddEmployeeWork",e)},updateEmployeeWork:function(e){return a.post("UpdateEmployeeWork",e)},deleteEmployeeWork:function(e){return a.post("DeleteEmployeeWork",e)},queryAbroadType:function(){return a.get("QueryAbroadType")},queryEmployeeAbroad:function(e){return a.get("QueryEmployeeAbroadInfo",e)},addEmployeeAbroad:function(e){return a.post("AddEmployeeAbroadInfo",e)},updateEmployeeAbroad:function(e){return a.post("UpdateEmployeeAbroadInfo",e)},deleteEmployeeAbroad:function(e){return a.post("DeleteEmployeeAbroadInfo",e)},queryContractType:function(){return a.get("QueryContractType")},queryEmployeeContract:function(e){return a.get("QueryEmployeeContract",e)},queryRenewEmployeeContract:function(e){return a.get("QueryRenewEmployeeContract",e)},updateNextTimeRemind:function(e){return a.post("UpdateNextTimeRemind",e)},sendEmailForRenewRemind:function(e){return a.post("SendEmailForRenewRemind",e)},batchUpdateNextTimeRemind:function(e){return a.post("BatchUpdateNextTimeRemind",e)},batchSendEmailForRenewRemind:function(e){return a.post("BatchSendEmailForRenewRemind",e)},addEmployeeContract:function(e){return a.post("AddEmployeeContract",e)},updateEmployeeContract:function(e){return a.post("UpdateEmployeeContract",e)},deleteEmployeeContract:function(e){return a.post("DeleteEmployeeContract",e)},queryTrainLevel:function(){return a.get("QueryTrainLevel")},queryEmployeeTrain:function(e){return a.get("QueryEmployeeTrain",e)},addEmployeeTrain:function(e){return a.post("AddEmployeeTrain",e)},updateEmployeeTrain:function(e){return a.post("UpdateEmployeeTrain",e)},deleteEmployeeTrain:function(e){return a.post("DeleteEmployeeTrain",e)},queryYearList:function(){return a.get("QueryYearList")},queryEvaluateResult:function(){return a.get("QueryEvaluateResult")},queryEmployeeAssessment:function(e){return a.get("QueryEmployeeAssessment",e)},addEmployeeAssessment:function(e){return a.post("AddEmployeeAssessment",e)},updateEmployeeAssessment:function(e){return a.post("UpdateEmployeeAssessment",e)},deleteEmployeeAssessment:function(e){return a.post("DeleteEmployeeAssessment",e)},queryEmployeeDeduct:function(e){return a.get("QueryEmployeeDeduct",e)},addEmployeeDeduct:function(e){return a.post("AddEmployeeDeduct",e)},updateEmployeeDeduct:function(e){return a.get("UpdateEmployeeDeduct",e)},deleteEmployeeDeduct:function(e){return a.post("DeleteEmployeeDeduct",e)},downloadEmployeeDeductTemplate:function(e){return a.getFile("DownlodaEmployeeDeductTemplate",e)},importEmployeeDeduct:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),a.postForm("ImportEmployeeDeduct",n)},queryEmployeeDeductUnCalculate:function(e){return a.get("QueryEmployeeDeductUnCalculate",e)},queryEmployeeDeductCalculate:function(e){return a.get("QueryEmployeeDeductCalculate",e)},getEmployeeDeductCalculate:function(e){return a.get("GetEmployeeDeductCalculate",e)},updateEmployeeDeductCalculate:function(e){return a.post("UpdateEmployeeDeductCalculate",e)},exportEmployeeDeductCalculate:function(e){return a.post("ExportEmployeeDeductCalculate",{data:e,responseType:"arraybuffer"})},queryEmployeeDeductWorkingAge:function(e){return a.get("QueryEmployeeDeductWorkingAge",e)},getEmployeeDeductWorkingAge:function(e){return a.get("GetEmployeeDeductWorkingAge",e)},addEmployeeDeductWorkingAge:function(e){return a.post("AddEmployeeDeductWorkingAge",e)},updateEmployeeDeductWorkingAge:function(e){return a.post("UpdateEmployeeDeductWorkingAge",e)},deleteEmployeeDeductWorkingAge:function(e){return a.post("DeleteEmployeeDeductWorkingAge",e)},queryIncentType:function(){return a.get("QueryIncentType")},queryIncentLevel:function(){return a.get("QueryIncentLevel")},queryEmployeeIncentive:function(e){return a.get("QueryEmployeeIncentive",e)},addEmployeeIncentive:function(e){return a.post("AddEmployeeIncentive",e)},updateEmployeeIncentive:function(e){return a.post("UpdateEmployeeIncentive",e)},deleteEmployeeIncentive:function(e){return a.post("DeleteEmployeeIncentive",e)},getEmployeeHealth:function(e){return a.get("GetEmployeeHealth",e)},updateEmployeeHealth:function(e){return a.post("UpdateEmployeeHealth",e)},queryAccidentType:function(){return a.get("QueryAccidentType")},queryEmployeeAccident:function(e){return a.get("QueryEmployeeAccident",e)},addEmployeeAccident:function(e){return a.post("AddEmployeeAccident",e)},updateEmployeeAccident:function(e){return a.post("UpdateEmployeeAccident",e)},deleteEmployeeAccident:function(e){return a.post("DeleteEmployeeAccident",e)},queryEmployeeTeach:function(e){return a.get("QueryEmployeeTeach",e)},addEmployeeTeach:function(e){return a.post("AddEmployeeTeach",e)},updateEmployeeTeach:function(e){return a.post("UpdateEmployeeTeach",e)},deleteEmployeeTeach:function(e){return a.post("DeleteEmployeeTeach",e)},queryEmployeeRelation:function(e){return a.get("QueryEmployeeRelation",e)},addEmployeeRelation:function(e){return a.post("AddEmployeeRelation",e)},updateEmployeeRelation:function(e){return a.post("UpdateEmployeeRelation",e)},deleteEmployeeRelation:function(e){return a.post("DeleteEmployeeRelation",e)},queryIncomeType:function(){return a.get("QueryIncomeType")},queryEmployeeArticle:function(e){return a.get("QueryEmployeeArticle",e)},addEmployeeArticle:function(e){return a.post("AddEmployeeArticle",e)},updateEmployeeArticle:function(e){return a.post("UpdateEmployeeArticle",e)},deleteEmployeeArticle:function(e){return a.post("DeleteEmployeeArticle",e)},queryClassLevel:function(){return a.get("QueryClassLevel")},queryEmployeeClass:function(e){return a.get("QueryEmployeeClass",e)},addEmployeeClass:function(e){return a.post("AddEmployeeClass",e)},updateEmployeeClass:function(e){return a.post("UpdateEmployeeClass",e)},deleteEmployeeClass:function(e){return a.post("DeleteEmployeeClass",e)},queryEmployeePatent:function(e){return a.get("QueryEmployeePatent",e)},addEmployeePatent:function(e){return a.post("AddEmployeePatent",e)},updateEmployeePatent:function(e){return a.post("UpdateEmployeePatent",e)},deleteEmployeePatent:function(e){return a.post("DeleteEmployeePatent",e)},queryTeacherType:function(){return a.get("QueryTeacherType")},queryEmployeeTeacher:function(e){return a.get("QueryEmployeeTeacher",e)},addEmployeeTeacher:function(e){return a.post("AddEmployeeTeacher",e)},updateEmployeeTeacher:function(e){return a.post("UpdateEmployeeTeacher",e)},deleteEmployeeTeacher:function(e){return a.post("DeleteEmployeeTeacher",e)},queryAwardLevel:function(){return a.get("QueryAwardLevel")},queryDictByParentCode:function(e){return a.get("QueryDictByParentCode",e)},queryHighTalent:function(){return a.get("QueryHighTalent")},queryEmployeeAward:function(e){return a.get("QueryEmployeeAward",e)},addEmployeeAward:function(e){return a.post("AddEmployeeAward",e)},updateEmployeeAward:function(e){return a.post("UpdateEmployeeAward",e)},deleteEmployeeAward:function(e){return a.post("DeleteEmployeeAward",e)},addEmployeeHighTalent:function(e){return a.post("AddEmployeeHighTalent",e)},updateEmployeeHighTalent:function(e){return a.post("UpdateEmployeeHighTalent",e)},queryEmployeeHighTalent:function(e){return a.get("QueryEmployeeHighTalent",e)},deleteEmployeeHighTalent:function(e){return a.post("DeleteEmployeeHighTalent",e)},queryEmployeeDeptHistory:function(e){return a.get("QueryEmployeeDeptHistory",e)},updateEmployeeDept:function(e){return a.post("UpdateEmployeeDept",e)},batchUpdateEmployeeDept:function(e){return a.post("BatchUpdateEmployeeDept",e)},queryEmployeeList:function(e){return a.post("QueryEmployeeList",e)},queryEmployeeListTree:function(e){return a.get("QueryEmployeeListTree",e)},queryDictsSetting:function(e){return a.get("QueryDictsSetting",e)},queryEmployeeListSettingTree:function(e){return a.get("QueryEmployeeListSettingTree",e)},queryAdvancedQueryType:function(){return a.get("QueryAdvancedQueryType")},saveEmployeeList:function(e){return a.post("SaveEmployeeList",e)},deleteEmployeeList:function(e){return a.post("DeleteEmployeeList",e)},getEmployeeList:function(e){return a.get("GetEmployeeList",e)},getEmployeeListExcel:function(e){return a.getFile("GetEmployeeListExcel",e)},queryDeptPrincipal:function(e){return a.get("QueryDeptPrincipal",e)},queryOtherEmployeeInfo:function(e){return a.post("QueryOtherEmployeeInfo",e)},saveOtherEmployeeInfo:function(e){return a.post("SaveOtherEmployeeInfo",e)},deleteOtherEmployeeInfo:function(e){return a.post("DeleteOtherEmployeeInfo",e)},queryOtherEmpTypes:function(e){return a.get("QueryOtherEmpTypes",e)},importExcel:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),a.postForm("ImportExcel",n)},downlodaImportExcelTemplate:function(e){return a.getFile("DownlodaImportExcelTemplate",e)},getSocialSecurityInfo:function(e){return a.get("getSocialSecurityInfo",e)},updateEmployeeSocialSecurity:function(e){return a.post("updateEmployeeSocialSecurity",e)},queryPayRollOrgClass:function(){return a.get("QueryPayRollOrgClass")},queryPayRollCompGroup:function(){return a.get("QueryPayRollCompGroup")},queryPayRollOrgSalary:function(e){return a.get("QueryPayRollOrgSalary",e)},queryPayRollOrgSalaryLevel:function(e){return a.get("QueryPayRollOrgSalaryLevel",e)},queryPayRollOrgPositionSalarys:function(e){return a.get("QueryPayRollOrgPositionSalarys",e)},queryWorkState:function(){return a.get("QueryWorkState")},getWagesInfo:function(e){return a.get("GetWagesInfo",e)},updateEmployeeBenefit:function(e){return a.post("UpdateEmployeeBenefit",e)},queryWagesHistory:function(e){return a.get("QueryWagesHistory",e)},getEmployeePayrollPrint:function(e){return a.get("GetEmployeePayrollPrint",e)},updateEmployeePayrollPrint:function(e){return a.post("UpdateEmployeePayrollPrint",e)},deleteEmployeePayrollPrint:function(e){return a.post("DeleteEmployeePayrollPrint",e)},getPrintDetailsInfo:function(e){return a.get("GetPrintDetailsInfo",e)},getEmployeeWage:function(e){return a.get("GetEmployeeWage",e)},editEmployeeWage:function(e){return a.post("EditEmployeeWage",e)},editEmployeeParty:function(e){return a.post("EditEmployeeParty",e)},queryEmployeeHRAffiliatedBranch:function(e){return a.get("QueryEmployeeHRAffiliatedBranch",e)},getEmployeeHRAffiliatedBranch:function(e){return a.get("GetEmployeeHRAffiliatedBranch",e)},addEmployeeHRAffiliatedBranch:function(e){return a.post("AddEmployeeHRAffiliatedBranch",e)},updateEmployeeHRAffiliatedBranch:function(e){return a.post("UpdateEmployeeHRAffiliatedBranch",e)},deleteEmployeeHRAffiliatedBranch:function(e){return a.post("DeleteEmployeeHRAffiliatedBranch",e)},queryEmployeeHRDict:function(e){return a.get("QueryEmployeeHRDict",e)},getEmployeeHRDict:function(e){return a.get("GetEmployeeHRDict",e)},addEmployeeHRDict:function(e){return a.post("AddEmployeeHRDict",e)},updateEmployeeHRDict:function(e){return a.post("UpdateEmployeeHRDict",e)},deleteEmployeeHRDict:function(e){return a.post("DeleteEmployeeHRDict",e)},queryEmployeeHRPartyMemberHonor:function(e){return a.get("QueryEmployeeHRPartyMemberHonor",e)},getEmployeeHRPartyMemberHonor:function(e){return a.get("GetEmployeeHRPartyMemberHonor",e)},addEmployeeHRPartyMemberHonor:function(e){return a.post("AddEmployeeHRPartyMemberHonor",e)},updateEmployeeHRPartyMemberHonor:function(e){return a.post("UpdateEmployeeHRPartyMemberHonor",e)},deleteEmployeeHRPartyMemberHonor:function(e){return a.post("DeleteEmployeeHRPartyMemberHonor",e)}}},ed82:function(e,t,n){"use strict";var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{visible:e.showEmployee,width:"80%",top:"5vh"},on:{"update:visible":function(t){e.showEmployee=t}}},[n("div",[n("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[n("el-checkbox",{model:{value:e.listQuery.isContainSubDept,callback:function(t){e.$set(e.listQuery,"isContainSubDept",t)},expression:"listQuery.isContainSubDept"}},[e._v("包含下级部门")]),n("c-tree",{attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[n("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[n("el-form-item",{attrs:{label:"唯一码",prop:"uid"}},[n("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.uid,callback:function(t){e.$set(e.listQuery,"uid",t)},expression:"listQuery.uid"}})],1),n("el-form-item",{attrs:{label:"工号",prop:"empCode"}},[n("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),n("el-form-item",{attrs:{label:"中文名",prop:"displayName"}},[n("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),n("el-form-item",{attrs:{label:"薪资状态",prop:"sex"}},[n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.listQuery.salaryStatus,callback:function(t){e.$set(e.listQuery,"salaryStatus",t)},expression:"listQuery.salaryStatus"}},e._l(e.salaryStatuss,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label||e.desc,value:e.value}})})),1)],1),n("el-form-item",{attrs:{label:"在职方式"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.listQuery.hireStyleId,callback:function(t){e.$set(e.listQuery,"hireStyleId",t)},expression:"listQuery.hireStyleId"}},e._l(e.empHireStyleOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),n("el-form-item",{attrs:{label:"离职方式"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.listQuery.leaveStyleId,callback:function(t){e.$set(e.listQuery,"leaveStyleId",t)},expression:"listQuery.leaveStyleId"}},e._l(e.empLeaveStyleOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.search()}}},[e._v("查询")])],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"唯一码",sortable:"custom",prop:"uid"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.uid))])]}}])}),n("el-table-column",{attrs:{label:"工号",sortable:"custom",prop:"empCode"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.empCode))])]}}])}),n("el-table-column",{attrs:{label:"中文名",sortable:"custom",prop:"displayName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.displayName))])]}}])}),n("el-table-column",{attrs:{label:"院区"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.hospitalAreaNameText))])]}}])}),n("el-table-column",{attrs:{label:"部门",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.deptName))])]}}])}),n("el-table-column",{attrs:{label:"薪资状态",sortable:"custom",prop:"employeeSalary.enumEmployeeSalaryStatus"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.enumEmployeeSalaryStatusDesc))])]}}])}),n("el-table-column",{attrs:{fixed:"right",label:"选择",align:"left","header-align":"center",width:"120","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("el-button",{staticStyle:{"padding-left":"5px !important","margin-left":"20%"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.selectRow(o)}}},[e._v(" 选择 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])})],1)])},r=[],a=(n("99af"),n("d3b7"),n("6b75"));function l(e){if(Array.isArray(e))return Object(a["a"])(e)}n("a4d3"),n("e01a"),n("d28b"),n("a630"),n("3ca3"),n("ddb0");function i(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}var u=n("06c5");function s(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e){return l(e)||i(e)||Object(u["a"])(e)||s()}var p=n("d368"),d=n("2efc"),y=n("f9ac"),m=n("e44c"),f={components:{},data:function(){return{showEmployee:!1,listLoading:!1,treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:["1"],pageList:[],listQuery:{total:1,pageIndex:1,pageSize:10,leaveStyleCode:"",salaryStatus:""},genderDropdown:[{value:1,label:"男"},{value:2,label:"女"}],salaryStatuss:[],empHireStyleOptions:[],empLeaveStyleOptions:[]}},created:function(){this.initSalaryStatusList(),this.loadEmployeeHireStyle(),this.loadEmployeeLeaveStyle()},methods:{initSalaryStatusList:function(){var e=this,t={enumType:"EmployeeSalaryStatus"};y["a"].getEnumInfos(t).then((function(t){e.salaryStatuss=[{value:"",label:"全部"}].concat(c(t.data.datas))})).catch((function(e){console.log(e)}))},loadEmployeeHireStyle:function(){var e=this;m["a"].queryHireStyle().then((function(t){e.empHireStyleOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeLeaveStyle:function(){var e=this;m["a"].queryLeaveStyle().then((function(t){e.empLeaveStyleOptions=t.data.datas})).catch((function(e){console.log(e)}))},treeNodeClick:function(e){this.listQuery.deptId=e.id,this.getPageList()},selectRow:function(e){this.showEmployee=!1,this.$emit("selectRow",e)},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.$delete(this.listQuery,"order"),this.listQuery.pageIndex=1,this.getPageList()},loadTree:function(){var e=this;p["a"].queryDeptByUser().then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},getPageList:function(){this.listQuery.deptId?this.queryEmployeesMethods(this.listQuery):alert("请先选择部门!")},queryEmployeesMethods:function(e){var t=this;this.listLoading=!0,console.log(e),d["a"].querySalaryEmployees(e).then((function(e){e.succeed?(t.pageList=e.data.datas,t.listQuery.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e)})).finally((function(){t.listLoading=!1}))}}},g=f,h=n("2877"),E=Object(h["a"])(g,o,r,!1,null,"36d46f7e",null);t["a"]=E.exports},f744:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-row",{staticClass:"filter-container",attrs:{gutter:11,type:"flex"}},[n("el-col",{attrs:{span:5}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"年度"},model:{value:e.listQuery.year,callback:function(t){e.$set(e.listQuery,"year",t)},expression:"listQuery.year"}})],1),n("el-col",{attrs:{span:5}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"状态"},model:{value:e.listQuery.enumDeductType,callback:function(t){e.$set(e.listQuery,"enumDeductType",t)},expression:"listQuery.enumDeductType"}},e._l(e.deductTypeList,(function(e){return n("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),n("el-col",{attrs:{span:5}},[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.showAddDialog()}}},[e._v("添加")])],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"CreateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{sortable:"custom",prop:"Year",label:"年度"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.year))])]}}])}),n("el-table-column",{attrs:{sortable:"custom",prop:"LastEditor",label:"操作人"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.lastEditor))])]}}])}),n("el-table-column",{attrs:{sortable:"custom",prop:"EnumDeductType",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.enumDeductTypeDesc))])]}}])}),n("el-table-column",{attrs:{sortable:"custom",prop:"Remark",label:"备注"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.remark))])]}}])}),n("el-table-column",{attrs:{sortable:"custom",prop:"LastEditTime",label:"操作时间"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.lastEditTime))])]}}])}),n("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center",width:"280","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("el-button",{attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.showDialog(o)}}},[e._v(" 详情 ")]),1==o.enumDeductType?n("el-button",{attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.deductCalculate(o)}}},[e._v(" 处理 ")]):e._e(),n("el-button",{attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.exportData(o)}}},[e._v("导出")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),n("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}}),n("addDialog",{ref:"addDialog",on:{refreshData:e.getPageList}})],1)},r=[],a=n("e44c"),l=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"80%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[n("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[n("el-tabs",{on:{"tab-click":e.handleTabClickMethod},model:{value:e.actionName,callback:function(t){e.actionName=t},expression:"actionName"}},[n("el-tab-pane",{attrs:{label:"不计算人员",name:"unCal"}},[n("el-row",{staticClass:"filter-container",attrs:{gutter:15,type:"flex"}},[n("el-col",{attrs:{span:2}},[n("el-input",{attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQueryUnCal.uid,callback:function(t){e.$set(e.listQueryUnCal,"uid",t)},expression:"listQueryUnCal.uid"}})],1),n("el-col",{attrs:{span:2}},[n("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQueryUnCal.empCode,callback:function(t){e.$set(e.listQueryUnCal,"empCode",t)},expression:"listQueryUnCal.empCode"}})],1),n("el-col",{attrs:{span:2}},[n("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQueryUnCal.displayName,callback:function(t){e.$set(e.listQueryUnCal,"displayName",t)},expression:"listQueryUnCal.displayName"}})],1),n("el-col",{attrs:{span:2}},[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchUnCal}},[e._v("查询")])],1),e.isEdit?n("el-col",{attrs:{span:2}},[n("el-button",{attrs:{type:"primary",title:"选择员工"},on:{click:e.selectEmployeeDialog}},[e._v("选 择")])],1):e._e(),e.isEdit?n("el-col",{attrs:{span:2}},[n("el-button",{attrs:{type:"primary"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),e.isEdit?n("el-col",{attrs:{span:5}},[n("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[n("el-button",{attrs:{type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataListUnCal,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"CreateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"Uid",label:"唯一码"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.uid))])]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"EmpCode",label:"工号"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.empCode))])]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"DisplayName",label:"姓名"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.displayName))])]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"DeptName",label:"部门"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.deptName))])]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"SocietyAge",label:"不计算工龄"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isEdit?n("el-checkbox",{model:{value:t.row.notCalAge,callback:function(n){e.$set(t.row,"notCalAge",n)},expression:"scope.row.notCalAge"}}):e._e(),e.isEdit?e._e():n("el-checkbox",{attrs:{disabled:""},model:{value:t.row.notCalAge,callback:function(n){e.$set(t.row,"notCalAge",n)},expression:"scope.row.notCalAge"}})]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"NewSocietyAge",label:"不计算薪级"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isEdit?n("el-checkbox",{model:{value:t.row.notCalSalaryScale,callback:function(n){e.$set(t.row,"notCalSalaryScale",n)},expression:"scope.row.notCalSalaryScale"}}):e._e(),e.isEdit?e._e():n("el-checkbox",{attrs:{disabled:""},model:{value:t.row.notCalSalaryScale,callback:function(n){e.$set(t.row,"notCalSalaryScale",n)},expression:"scope.row.notCalSalaryScale"}})]}}])}),e.isEdit?n("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.saveDeductCalculate(o)}}},[e._v(" 保存 ")])]}}],null,!1,3543744862)}):e._e()],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.totalUnCal>0,expression:"totalUnCal > 0"}],attrs:{total:e.totalUnCal,"page-sizes":[10,20,50],page:e.listQueryUnCal.pageIndex,limit:e.listQueryUnCal.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryUnCal,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryUnCal,"pageSize",t)},pagination:e.getUnCalPageList}})],1),n("el-tab-pane",{attrs:{label:"计算人员",name:"Cal"}},[n("el-row",{staticClass:"filter-container",attrs:{gutter:15,type:"flex"}},[n("el-col",{attrs:{span:2}},[n("el-input",{attrs:{clearable:"",placeholder:"唯一码"},model:{value:e.listQueryCal.uid,callback:function(t){e.$set(e.listQueryCal,"uid",t)},expression:"listQueryCal.uid"}})],1),n("el-col",{attrs:{span:2}},[n("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQueryCal.empCode,callback:function(t){e.$set(e.listQueryCal,"empCode",t)},expression:"listQueryCal.empCode"}})],1),n("el-col",{attrs:{span:2}},[n("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQueryCal.displayName,callback:function(t){e.$set(e.listQueryCal,"displayName",t)},expression:"listQueryCal.displayName"}})],1),n("el-col",{attrs:{span:2}},[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchCal}},[e._v("查询")])],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataListCal,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"CreateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"Uid",label:"唯一码"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.uid))])]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"EmpCode",label:"工号"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.empCode))])]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"DisplayName",label:"姓名"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.displayName))])]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"DeptName",label:"部门"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.deptName))])]}}])}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"SocietyAge",label:"实际工龄"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.societyAge))])]}}])}),e.isEdit?e._e():n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"NewSocietyAge",label:"新实际工龄"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.newSocietyAge))])]}}],null,!1,2310409861)}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"CompanyAge",label:"本院工龄"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.companyAge))])]}}])}),e.isEdit?e._e():n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"NewCompanyAge",label:"新本院工龄"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.newCompanyAge))])]}}],null,!1,1647486076)}),n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"SalaryScale",label:"薪级"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.salaryScale))])]}}])}),e.isEdit?e._e():n("el-table-column",{attrs:{"nim-width":"120",sortable:"custom",prop:"NewSalaryScale",label:"新薪级"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.newSalaryScale))])]}}],null,!1,*********)})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.totalCal>0,expression:"totalCal > 0"}],attrs:{total:e.totalCal,"page-sizes":[10,20,50],page:e.listQueryCal.pageIndex,limit:e.listQueryCal.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryCal,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryCal,"pageSize",t)},pagination:e.getCalPageList}})],1)],1)],1)],1),n("selectUserComponent",{ref:"selectEmployee",on:{selectRow:e.setEmployee}})],1)},i=[],u=(n("d81d"),n("ed82")),s={components:{selectUserComponent:u["a"]},data:function(){return{showDialog:!1,title:"工龄薪级详情",rules:{},btnSaveLoading:!1,isEdit:!1,dataModel:{},deductId:null,enumDeductType:1,totalUnCal:0,listQueryUnCal:{order:"-CreateTime",pageIndex:1,pageSize:10},dataListUnCal:[],totalCal:0,listQueryCal:{order:"-CreateTime",pageIndex:1,pageSize:10},dataListCal:[],listLoading:!1,actionName:"unCal",editIds:[]}},methods:{initDialog:function(e){1===e.enumDeductType?this.isEdit=!0:this.isEdit=!1,console.log(this.isEdit),this.enumDeductType=e.enumDeductType,this.deductId=e.id,this.listQueryUnCal.deductId=e.id,this.listQueryCal.deductId=e.id,this.loadData(),this.showDialog=!0},getUnCalPageList:function(){var e=this;this.listLoading=!0,a["a"].queryEmployeeDeductUnCalculate(this.listQueryUnCal).then((function(t){e.listLoading=!1,t.succeed?(e.dataListUnCal=t.data.datas,e.totalUnCal=t.data.recordCount,e.listQueryUnCal.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},searchUnCal:function(){this.listQueryUnCal.pageIndex=1,this.getUnCalPageList()},getCalPageList:function(){var e=this;this.listLoading=!0,a["a"].queryEmployeeDeductCalculate(this.listQueryCal).then((function(t){e.listLoading=!1,t.succeed?(e.dataListCal=t.data.datas,e.totalCal=t.data.recordCount,e.listQueryCal.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},handleSelectionChange:function(e){this.editIds=e.map((function(e){return e.id}))},searchCal:function(){this.listQueryCal.pageIndex=1,this.getCalPageList()},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()},sortChange:function(e){e.column,e.prop,e.order},selectEmployeeDialog:function(){this.$refs.selectEmployee.loadTree(),this.$refs.selectEmployee.showEmployee=!0},setEmployee:function(e){this.dataModel={employeeModel:{}},this.setEmployeeUnCal(e)},setEmployeeUnCal:function(e){this.dataModel.employeeId=e.id,this.dataModel.deductId=this.deductId,this.dataModel.notCalAge=!0,this.dataModel.notCalSalaryScale=!0,console.log(this.dataModel),this.saveDeductCalculate(this.dataModel)},saveDeductCalculate:function(e){var t=this;1===this.enumDeductType&&a["a"].updateEmployeeDeductCalculate(e).then((function(e){e.succeed&&t.getUnCalPageList()})).catch()},exportData:function(){var e=this;this.$confirm("确定导出工龄薪级数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a["a"].exportEmployeeDeductCalculate(e.listQueryCal).then((function(t){var o=n("19de"),r="工龄薪级"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?o(t.data,r):o(t,r)})).catch((function(t){e.listLoadingForArticle=!1,console.log(t)}))})).catch((function(t){e.listLoadingForArticle=!1,t.succeed||e.$notice.message("取消导出","info")}))},downloadexceltemplate:function(){a["a"].downloadEmployeeDeductTemplate().then((function(e){var t=n("19de"),o="UpdateCompanyAgeSalaryScaleTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel:function(e){var t=this,n=e.file;a["a"].importEmployeeDeduct(n,{deductId:this.deductId}).then((function(e){if(e.succeed){t.getUnCalPageList();var n=e.data;t.$message.success(n)}})).catch((function(e){}))},handleTabClickMethod:function(e,t){this.loadData()},loadData:function(){switch(this.actionName){case"unCal":this.loadUnCal();break;case"Cal":this.loadCal();break;default:break}},loadUnCal:function(){this.getUnCalPageList()},loadCal:function(){this.getCalPageList()}}},c=s,p=n("2877"),d=Object(p["a"])(c,l,i,!1,null,null,null),y=d.exports,m=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[n("el-form",{ref:"dataForm",attrs:{model:e.dataModel,"label-width":"120px"}},[n("el-row",[n("el-col",{attrs:{span:10}},[n("el-form-item",{attrs:{label:"年度",prop:"Year"}},[[n("el-date-picker",{attrs:{type:"year",placeholder:"选择年"},model:{value:e.year,callback:function(t){e.year=t},expression:"year"}})]],2)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"备注"}},[n("el-input",{attrs:{type:"textarea",rows:3,maxlength:"500",clearable:"",placeholder:"备注"},model:{value:e.dataModel.remark,callback:function(t){e.$set(e.dataModel,"remark",t)},expression:"dataModel.remark"}})],1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},f=[],g=(n("a630"),n("d3b7"),n("25f0"),n("3ca3"),{data:function(){return{showDialog:!1,title:"",btnSaveLoading:!1,isEdit:!1,dataModel:{},year:new Date,selectedMonth:null,months:Array.from({length:13},(function(e,t){return t+1}))}},methods:{initDialog:function(e){if(this.showDialog=!0,!e){this.title="新增薪级工龄计算",this.isEdit=!1,this.getNowTime();var t=this.dataModel.month?new Date(this.dataModel.month).getMonth()+1:0;8===t&&(this.dataModel.yearSocialSecurityBaseCorrection=!0)}},getNowTime:function(){this.selectedMonth=(new Date).getMonth()+1},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){e.dataModel.year=new Date(e.year).getFullYear().toString(),t&&(e.isEdit||a["a"].addEmployeeDeduct(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"添加成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1,e.closeDialog()})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}}),h=g,E=(n("852c"),Object(p["a"])(h,m,f,!1,null,null,null)),S=E.exports,b=n("f9ac"),v={components:{editDialog:y,addDialog:S},data:function(){return{addForm:{},dataList:[],deductTypeList:[],total:0,listQuery:{enumDeductType:"",pageIndex:1,pageSize:10},listLoading:!1}},created:function(){this.initDeductTypeList(),this.getPageList()},methods:{initDeductTypeList:function(){var e=this,t={enumType:"DeductType"};b["a"].getEnumInfos(t).then((function(t){e.deductTypeList=t.data.datas})).catch((function(e){console.log(e)}))},getPageList:function(){var e=this;this.listLoading=!0,a["a"].queryEmployeeDeduct(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e,t,n){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="-"),"ascending"===e.order&&(o="+"),this.listQuery.order=o+e.prop,this.getPageList()},showDialog:function(e){this.$refs.editDialog.initDialog(e)},showAddDialog:function(e){this.$refs.addDialog.initDialog(e)},deductCalculate:function(e){var t=this;this.$confirm("确定更新工龄薪级吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a["a"].updateEmployeeDeduct({deductId:e.id}).then((function(e){e.succeed&&(t.$message({message:"更新工龄薪级成功",type:"success"}),t.getPageList())})).catch((function(e){console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消工龄薪级更新","info")}))},exportData:function(e){var t=this;this.$confirm("确定导出工龄薪级数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a["a"].exportEmployeeDeductCalculate({deductId:e.id}).then((function(e){var o=n("19de"),r="工龄薪级"+t.$moment().format("YYYYMMDDHHmmss")+".xlsx";e.data?o(e.data,r):o(e,r)})).catch((function(e){t.listLoadingForArticle=!1,console.log(e)}))})).catch((function(e){t.listLoadingForArticle=!1,e.succeed||t.$notice.message("取消导出","info")}))}}},C=v,D=Object(p["a"])(C,o,r,!1,null,null,null);t["default"]=D.exports},f9ac:function(e,t,n){"use strict";var o=n("cfe3"),r="SysManage",a=new o["a"](r);t["a"]={queryDict:function(e){return a.get("QueryDict",e)},queryDictType:function(e){return a.post("QueryDictType",e)},addDict:function(e){return a.post("AddDict",e)},deleteDict:function(e){return a.post("DeleteDict",e)},updateDict:function(e){return a.post("UpdateDict",e)},getDict:function(e){return a.get("GetDict",e)},querySysSetting:function(e){return a.get("QuerySysSetting",e)},addSysSetting:function(e){return a.post("AddSysSetting",e)},deleteSysSetting:function(e){return a.post("DeleteSysSetting",e)},updateSysSetting:function(e){return a.post("UpdateSysSetting",e)},getSysSetting:function(e){return a.get("GetSysSetting",e)},queryLanguage:function(e){return a.get("QueryLanguage",e)},getEnumInfos:function(e){return a.get("GetEnumInfos",e)},queryUserGroups:function(e){return a.post("QueryUserGroups",e)},saveUserGroup:function(e){return a.post("SaveUserGroup",e)},deleteUserGroup:function(e){return a.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return a.get("DropdownUserGroups",e)},queryUsers:function(e){return a.post("QueryUsers",e)},saveUser:function(e){return a.post("SaveUser",e)},deleteUser:function(e){return a.post("DeleteUser",e)},initPwd:function(e){return a.post("InitPwd",e)},getUserById:function(e){return a.get("GetUserById",e)},queryEmployees:function(e){return a.post("QueryEmployees",e)},queryModuleInfos:function(e){return a.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return a.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return a.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return a.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return a.post("SaveRightOfDept",e)},queryControlRight:function(e){return a.post("QueryControlRight",e)},saveControlRights:function(e){return a.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return a.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return a.get("QueryStationTree",e)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(e){return a.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return a.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)}}}}]);