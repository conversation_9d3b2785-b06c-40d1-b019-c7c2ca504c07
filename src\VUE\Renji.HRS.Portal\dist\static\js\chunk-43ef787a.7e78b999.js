(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-43ef787a"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},3421:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{staticClass:"input_class",attrs:{type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd",size:"small",clearable:!1},model:{value:t.headModel.recordDate,callback:function(e){t.$set(t.headModel,"recordDate",e)},expression:"headModel.recordDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"假期天数"}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:t.dayChanged},model:{value:t.headModel.addDay,callback:function(e){t.$set(t.headModel,"addDay",e)},expression:"headModel.addDay"}},t._l(t.dayOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"部门"}},[a("c-select-tree2",{ref:"treeSelect",attrs:{clearable:!0,multiple:!0,data:t.treeData,props:t.treeProps,"check-strictly":!0},model:{value:t.headModel.dept,callback:function(e){t.$set(t.headModel,"dept",e)},expression:"headModel.dept"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"唯一码"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empUid,callback:function(e){t.$set(t.headModel,"empUid",e)},expression:"headModel.empUid"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工号"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empCode,callback:function(e){t.$set(t.headModel,"empCode",e)},expression:"headModel.empCode"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"员工姓名"}},[a("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.empName,callback:function(e){t.$set(t.headModel,"empName",e)},expression:"headModel.empName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:""}},[a("el-button",{attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")]),a("el-button",{attrs:{type:"primary"},on:{click:t.exportData}},[t._v("导出")])],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"仅显示加班数据"}},[a("el-checkbox",{model:{value:t.headModel.onlyOTValue,callback:function(e){t.$set(t.headModel,"onlyOTValue",e)},expression:"headModel.onlyOTValue"}})],1)],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[a("el-table-column",{attrs:{prop:"EmpUid",label:"唯一码",align:"center",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.empUid))])]}}])}),a("el-table-column",{attrs:{prop:"EmpCode",label:"工号",align:"center",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"EmpName",label:"姓名",align:"center",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.empName))])]}}])}),a("el-table-column",{attrs:{prop:"EmpDept",label:"部门",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.empDept))])]}}])}),a("el-table-column",{attrs:{prop:"HireStyleName",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.hireStyleName))])]}}])}),a("el-table-column",{attrs:{prop:"EnumStatusDesc",label:"状态",align:"center",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.enumStatusDesc))])]}}])}),t._l(t.dateDatas,(function(e,n){return a("el-table-column",{key:n,attrs:{label:e,align:"center"}},[a("el-table-column",{attrs:{prop:"Hour_4",label:"4小时班",align:"center",width:"65"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.details[n].hour_4))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_8",label:"8小时班",align:"center",width:"65"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.details[n].hour_8))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_12",label:"12小时班",align:"center",width:"75"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.details[n].hour_12))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_16",label:"16小时班",align:"center",width:"75"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.details[n].hour_16))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_24",label:"24小时班",align:"center",width:"75"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.details[n].hour_24))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"Hour_Doctor24",label:"医生24小时班",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.details[n].hour_Doctor24))])]}}],null,!0)})],1)})),a("el-table-column",{attrs:{prop:"SumClass",label:"班次",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.sumClass))])]}}])})],2),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},r=[],o=(a("99af"),a("d3b7"),a("ac1f"),a("25f0"),a("3ca3"),a("4d90"),a("841c"),a("ddb0"),a("2b3d"),a("d368")),l=a("cbd2"),i={components:{},data:function(){return{dateDatas:[],dayOptions:[{label:"1天",value:"1"},{label:"2天",value:"2"},{label:"3天",value:"3"},{label:"4天",value:"4"},{label:"5天",value:"5"},{label:"6天",value:"6"},{label:"7天",value:"7"}],total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,headModel:{addDay:"1",recordDate:this.getNowTime(),onlyOTValue:!0,dept:[]},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:[],currentNode:null,tableData:[],zoomLevel:1}},created:function(){this.loadTree()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth(),n=t.getDate();a+=1,a=a.toString().padStart(2,"0"),n=n.toString().padStart(2,"0");var r="".concat(e,"-").concat(a,"-").concat(n);return r},dayChanged:function(t){this.search()},loadTree:function(){var t=this;o["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})),this.resertCurrentNode()},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},exportData:function(){var t={RecordDate:this.headModel.recordDate,AddDay:this.headModel.addDay,DeptIds:this.headModel.dept,EmpCode:this.headModel.empCode,EmpUid:this.headModel.empUid,EmpName:this.headModel.empName,OnlyOTValue:this.headModel.onlyOTValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};l["a"].getOTReportExcel(t).then((function(t){console.log(t);var e=new Blob([t],{type:t.type}),a="节日加班明细.xlsx";if(window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(e,a);else{var n=document.createElement("a"),r=window.URL.createObjectURL(e);n.href=r,n.download=a,document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(r)}}))},getSearchResult:function(){var t=this,e={RecordDate:this.headModel.recordDate,AddDay:this.headModel.addDay,DeptIds:this.headModel.dept,EmpCode:this.headModel.empCode,EmpUid:this.headModel.empUid,EmpName:this.headModel.empName,OnlyOTValue:this.headModel.onlyOTValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize};l["a"].searchAttHolidayOTRecordDetail(e).then((function(e){t.listLoading=!1,e.succeed?(t.tableData=e.data.datas,t.dateDatas=[],e.data.datas&&e.data.datas.length>0&&(t.dateDatas=e.data.datas[0].recordDates),t.total=e.data.recordCount):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()}}},c=i,u=a("2877"),d=Object(u["a"])(c,n,r,!1,null,null,null);e["default"]=d.exports},"841c":function(t,e,a){"use strict";var n=a("d784"),r=a("825a"),o=a("1d80"),l=a("129f"),i=a("14c3");n("search",1,(function(t,e,a){return[function(e){var a=o(this),n=void 0==e?void 0:e[t];return void 0!==n?n.call(e,a):new RegExp(e)[t](String(a))},function(t){var n=a(e,t,this);if(n.done)return n.value;var o=r(t),c=String(this),u=o.lastIndex;l(u,0)||(o.lastIndex=0);var d=i(o,c);return l(o.lastIndex,u)||(o.lastIndex=u),null===d?-1:d.index}]}))},cbd2:function(t,e,a){"use strict";var n=a("cfe3"),r="AttendanceManage",o=new n["a"](r);e["a"]={getAttMonthShiftRecord:function(t){return o.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return o.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return o.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return o.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return o.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return o.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return o.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return o.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return o.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return o.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return o.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return o.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return o.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return o.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return o.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return o.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return o.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return o.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return o.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return o.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return o.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return o.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return o.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return o.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return o.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return o.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return o.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return o.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return o.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return o.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return o.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return o.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return o.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return o.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return o.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return o.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return o.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return o.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return o.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return o.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return o.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return o.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return o.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return o.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return o.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return o.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return o.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return o.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return o.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return o.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return o.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return o.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return o.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return o.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return o.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return o.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return o.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return o.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return o.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return o.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return o.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},d368:function(t,e,a){"use strict";var n=a("cfe3"),r="Organization",o=new n["a"](r);e["a"]={QueryOrganizationHiddenTop:function(t){return o.get("QueryOrganizationHiddenTop",t)},QueryOrganization:function(t){return o.get("QueryOrganization",t)},QueryDepartment:function(t){return o.get("QueryDepartment",t)},GetDepartment:function(t){return o.get("GetDepartment",t)},AddDepartment:function(t){return o.post("AddDepartment",t)},UpdateDepartment:function(t){return o.post("UpdateDepartment",t)},MoveDepartment:function(t){return o.post("MoveDepartment",t)},MergeDepartment:function(t){return o.post("MergeDepartment",t)},DeleteDepartment:function(t){return o.post("DeleteDepartment",t)},queryPosition:function(t){return o.post("QueryPosition",t)},getPosition:function(t){return o.get("GetPosition",t)},addPosition:function(t){return o.post("AddPosition",t)},updatePosition:function(t){return o.post("UpdatePosition",t)},deletePosition:function(t){return o.post("DeletePosition",t)},GetStation:function(t){return o.get("GetStation",t)},AddStation:function(t){return o.post("AddStation",t)},UpdateStation:function(t){return o.post("UpdateStation",t)},DeleteStation:function(t){return o.post("DeleteStation",t)},QueryPositionStationTree:function(t){return o.get("QueryPositionStationTree",t)},AllocatePosition:function(t){return o.post("AllocatePosition",t)},DeletePositionStation:function(t){return o.post("DeletePositionStation",t)},queryDeptByUser:function(t){return o.get("QueryDeptByUser",t)},queryOneLevelStation:function(){return o.get("QueryOneLevelStation")},queryTwoLevelStation:function(t){return o.get("QueryTwoLevelStation",t)},querySenioritySelect:function(){return o.get("QuerySenioritySelect")},queryStationAllowance:function(t){return o.get("QueryStationAllowance",t)},downloadStationAllowanceTemplate:function(t){return o.post("DownloadStationAllowanceTemplate",{data:t,responseType:"arraybuffer"})},importStationAllowance:function(t,e){return e.append("file",t),o.postForm("ImportStationAllowance",e)},exportStationAllowance:function(t){return o.post("ExportStationAllowance",{data:t,responseType:"arraybuffer"})},getStationAllowance:function(t){return o.get("GetStationAllowance",t)},addStationAllowance:function(t){return o.post("AddStationAllowance",t)},updateStationAllowance:function(t){return o.post("UpdateStationAllowance",t)},querySeniority:function(t){return o.get("QuerySeniority",t)},importSeniority:function(t,e){return e.append("file",t),o.postForm("ImportSeniority",e)},downloadSeniorityTemplate:function(t){return o.post("DownloadSeniorityTemplate",{data:t,responseType:"arraybuffer"})},exportSeniority:function(t){return o.post("ExportSeniority",{data:t,responseType:"arraybuffer"})},getSeniority:function(t){return o.get("GetSeniority",t)},addSeniority:function(t){return o.post("AddSeniority",t)},updateSeniority:function(t){return o.post("UpdateSeniority",t)},querySalaryScale:function(t){return o.get("QuerySalaryScale",t)},getSalaryScale:function(t){return o.get("GetSalaryScale",t)},addSalaryScale:function(t){return o.post("AddSalaryScale",t)},updateSalaryScale:function(t){return o.post("UpdateSalaryScale",t)},deleteSalaryScale:function(t){return o.post("DeleteSalaryScale",t)},importSalaryScale:function(t,e){return e.append("file",t),o.postForm("ImportSalaryScale",e)},exportSalaryScale:function(t){return o.post("ExportSalaryScale",{data:t,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(t){return o.get("QuerySalaryScaleByStationId",t)},queryStationAllowanceByStationId:function(t){return o.get("QueryStationAllowanceByStationId",t)},queryCarSubsidy:function(t){return o.get("QueryCarSubsidy",t)},getCarSubsidy:function(t){return o.get("GetCarSubsidy",t)},addCarSubsidy:function(t){return o.post("AddCarSubsidy",t)},updateCarSubsidy:function(t){return o.post("UpdateCarSubsidy",t)},deleteCarSubsidy:function(t){return o.post("DeleteCarSubsidy",t)},queryTelephoneFee:function(t){return o.get("QueryTelephoneFee",t)},getTelephoneFee:function(t){return o.get("GetTelephoneFee",t)},addTelephoneFee:function(t){return o.post("AddTelephoneFee",t)},updateTelephoneFee:function(t){return o.post("UpdateTelephoneFee",t)},deleteTelephoneFee:function(t){return o.post("DeleteTelephoneFee",t)}}}}]);