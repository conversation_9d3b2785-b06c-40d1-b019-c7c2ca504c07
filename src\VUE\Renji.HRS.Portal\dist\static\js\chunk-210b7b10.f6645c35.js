(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-210b7b10"],{"0bb4":function(e,t,n){"use strict";var r=n("cfe3"),o="Notice",a=new r["a"](o);t["a"]={queryMsgCompany:function(e){return a.post("QueryMsgCompany",e)},saveMsgCompany:function(e){return a.post("SaveMsgCompany",e)},deleteMsgCompany:function(e){return a.post("DeleteMsgCompany",e)},queryMsgPerson:function(e){return a.post("QueryMsgPerson",e)},saveMsgPerson:function(e){return a.post("SaveMsgPerson",e)},deleteMsgPerson:function(e){return a.post("DeleteMsgPerson",e)},getMsgReadInfoByMsgPerson:function(e){return a.get("GetMsgReadInfoByMsgPerson",e)},getEmpByDept:function(e){return a.get("GetEmpByDept",e)},queryMsgCompanyToEffective:function(e){return a.post("QueryMsgCompanyToEffective",e)},getMsgCompanyModelById:function(e){return a.get("GetMsgCompanyModelById",e)},queryMsgPersonToEffective:function(e){return a.post("QueryMsgPersonToEffective",e)},qetMsgPersonModelById:function(e){return a.get("GetMsgPersonModelById",e)}}},"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"841c":function(e,t,n){"use strict";var r=n("d784"),o=n("825a"),a=n("1d80"),i=n("129f"),l=n("14c3");r("search",1,(function(e,t,n){return[function(t){var n=a(this),r=void 0==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var a=o(e),u=String(this),s=a.lastIndex;i(s,0)||(a.lastIndex=0);var c=l(a,u);return i(a.lastIndex,s)||(a.lastIndex=s),null===c?-1:c.index}]}))},b89d:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[n("el-form-item",[n("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),n("el-form-item",[n("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),n("el-form-item",[n("el-input",{attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.search(!1)}}},[e._v("查询")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addDialog}},[e._v("添加")])],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"标题",sortable:"custom",prop:"title"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.title))])]}}])}),n("el-table-column",{attrs:{label:"有效期开始时间",sortable:"custom",prop:"availStartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.startDate))])]}}])}),n("el-table-column",{attrs:{label:"有效期结束时间",sortable:"custom",prop:"availEndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.endDate))])]}}])}),n("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.updateDialog(r)}}},[e._v(" 编辑 ")]),n("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.deleteRecord(r)}}},[e._v(" 删除 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),n("el-dialog",{attrs:{title:"添加",visible:e.addDialogVisible,width:"60%"},on:{"update:visible":function(t){e.addDialogVisible=t},close:e.closeAddDialog}},[n("el-form",{ref:"ref_addForm",attrs:{rules:e.rules,model:e.addForm,"label-width":"120px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"有效期开始时间",prop:"availStartDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.addForm.availStartDate,callback:function(t){e.$set(e.addForm,"availStartDate",t)},expression:"addForm.availStartDate"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"有效期结束时间",prop:"availEndDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.addForm.availEndDate,callback:function(t){e.$set(e.addForm,"availEndDate",t)},expression:"addForm.availEndDate"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"部门",prop:"deptId"}},[n("c-select-tree",{attrs:{options:e.treeDeptData,"tree-props":e.treeProps},model:{value:e.addForm.deptId,callback:function(t){e.$set(e.addForm,"deptId",t)},expression:"addForm.deptId"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"标题"},model:{value:e.addForm.title,callback:function(t){e.$set(e.addForm,"title",t)},expression:"addForm.title"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"内容",prop:"content"}},[n("el-input",{attrs:{placeholder:"",type:"textarea",rows:5},model:{value:e.addForm.content,callback:function(t){e.$set(e.addForm,"content",t)},expression:"addForm.content"}})],1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.addDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitAddForm}},[e._v("保 存")])],1)],1),n("el-dialog",{attrs:{title:"更新",visible:e.updateDialogVisible,width:"60%"},on:{"update:visible":function(t){e.updateDialogVisible=t}}},[n("el-form",{ref:"ref_updateForm",attrs:{rules:e.rules,model:e.updateForm,"label-width":"120px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"有效期开始时间",prop:"availStartDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.updateForm.availStartDate,callback:function(t){e.$set(e.updateForm,"availStartDate",t)},expression:"updateForm.availStartDate"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"有效期结束时间",prop:"availEndDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.updateForm.availEndDate,callback:function(t){e.$set(e.updateForm,"availEndDate",t)},expression:"updateForm.availEndDate"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"部门",prop:"deptId"}},[n("c-select-tree",{attrs:{options:e.treeDeptData,"tree-props":e.treeProps},model:{value:e.updateForm.deptId,callback:function(t){e.$set(e.updateForm,"deptId",t)},expression:"updateForm.deptId"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"标题",prop:"title"}},[n("el-input",{attrs:{placeholder:"标题"},model:{value:e.updateForm.title,callback:function(t){e.$set(e.updateForm,"title",t)},expression:"updateForm.title"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"内容",prop:"content"}},[n("el-input",{attrs:{placeholder:"",type:"textarea",rows:5},model:{value:e.updateForm.content,callback:function(t){e.$set(e.updateForm,"content",t)},expression:"updateForm.content"}})],1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.updateDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitUpdateForm}},[e._v("保 存")])],1)],1)],1)},o=[],a=(n("d3b7"),n("ac1f"),n("841c"),n("f9ac")),i=n("0bb4"),l=n("d368"),u={components:{},data:function(){return{treeDeptData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},addForm:{availStartDate:"",availEndDate:"",deptId:"",title:"",content:""},updateForm:{},rules:{title:[{required:!0,message:"[标题]是必填项！",trigger:"blur"},{required:!0,max:100,message:"[标题]超长！",trigger:"blur"}],deptId:[{required:!0,message:"[部门]是必填项！",trigger:"change"}],availStartDate:[{required:!0,message:"[有效期开始时间]是必填项！",trigger:"change"}],availEndDate:[{required:!0,message:"[有效期结束时间]是必填项！",trigger:"change"}]},addDialogVisible:!1,updateDialogVisible:!1,pageList:[],listQuery:{isCurrentUser:!0,queryCondition:{},total:1,pageIndex:1,pageSize:10},listLoading:!1,languageTypes:[],dataColumns:[{value:"1",label:"标题",type:"System.String",columnName:"Title"},{value:"2",label:"有效期开始时间",type:"System.DateTime",columnName:"AvailStartDate"},{value:"3",label:"有效期结束时间",type:"System.DateTime",columnName:"AvailEndDate"}],selectConditionOptions:[]}},created:function(){this.getPageList(),this.loadConditions(),this.loadTree()},methods:{loadTree:function(){var e=this;l["a"].queryDeptByUser({}).then((function(t){e.treeDeptData=t.data})).catch((function(e){console.log(e)}))},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.search()},search:function(e){void 0!==e&&null!==e&&(this.listQuery.isCurrentUser=e),this.listQuery.pageIndex=1,this.getPageList()},loadConditions:function(){var e=this;a["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getPageList:function(){var e=this;this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),i["a"].queryMsgCompany(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},addDialog:function(){this.addDialogVisible=!0},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()},updateDialog:function(e){this.updateDialogVisible=!0,this.updateForm=e},submitAddForm:function(){var e=this;this.$refs["ref_addForm"].validate((function(t){t&&i["a"].saveMsgCompany(e.addForm).then((function(t){t.succeed?(e.addDialogVisible=!1,e.search(),e.$notice.message("创建成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))}))},submitUpdateForm:function(){var e=this;this.$refs["ref_updateForm"].validate((function(t){t&&i["a"].saveMsgCompany(e.updateForm).then((function(t){t.succeed?(e.updateDialogVisible=!1,e.search(),e.$notice.message("更新成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))}))},deleteRecord:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i["a"].deleteMsgCompany({id:e.id}).then((function(e){e.succeed?(t.search(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){e.succeed||t.$notice.message("取消删除","info")}))}}},s=u,c=n("2877"),d=Object(c["a"])(s,r,o,!1,null,"951e412a",null);t["default"]=d.exports},d368:function(e,t,n){"use strict";var r=n("cfe3"),o="Organization",a=new r["a"](o);t["a"]={QueryOrganizationHiddenTop:function(e){return a.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return a.get("QueryOrganization",e)},QueryDepartment:function(e){return a.get("QueryDepartment",e)},GetDepartment:function(e){return a.get("GetDepartment",e)},AddDepartment:function(e){return a.post("AddDepartment",e)},UpdateDepartment:function(e){return a.post("UpdateDepartment",e)},MoveDepartment:function(e){return a.post("MoveDepartment",e)},MergeDepartment:function(e){return a.post("MergeDepartment",e)},DeleteDepartment:function(e){return a.post("DeleteDepartment",e)},queryPosition:function(e){return a.post("QueryPosition",e)},getPosition:function(e){return a.get("GetPosition",e)},addPosition:function(e){return a.post("AddPosition",e)},updatePosition:function(e){return a.post("UpdatePosition",e)},deletePosition:function(e){return a.post("DeletePosition",e)},GetStation:function(e){return a.get("GetStation",e)},AddStation:function(e){return a.post("AddStation",e)},UpdateStation:function(e){return a.post("UpdateStation",e)},DeleteStation:function(e){return a.post("DeleteStation",e)},QueryPositionStationTree:function(e){return a.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return a.post("AllocatePosition",e)},DeletePositionStation:function(e){return a.post("DeletePositionStation",e)},queryDeptByUser:function(e){return a.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return a.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return a.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),a.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return a.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return a.get("GetStationAllowance",e)},addStationAllowance:function(e){return a.post("AddStationAllowance",e)},updateStationAllowance:function(e){return a.post("UpdateStationAllowance",e)},querySeniority:function(e){return a.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),a.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return a.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return a.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return a.get("GetSeniority",e)},addSeniority:function(e){return a.post("AddSeniority",e)},updateSeniority:function(e){return a.post("UpdateSeniority",e)},querySalaryScale:function(e){return a.get("QuerySalaryScale",e)},getSalaryScale:function(e){return a.get("GetSalaryScale",e)},addSalaryScale:function(e){return a.post("AddSalaryScale",e)},updateSalaryScale:function(e){return a.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return a.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),a.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return a.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return a.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return a.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return a.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return a.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return a.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return a.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return a.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return a.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return a.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return a.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return a.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return a.post("DeleteTelephoneFee",e)}}},f9ac:function(e,t,n){"use strict";var r=n("cfe3"),o="SysManage",a=new r["a"](o);t["a"]={queryDict:function(e){return a.get("QueryDict",e)},queryDictType:function(e){return a.post("QueryDictType",e)},addDict:function(e){return a.post("AddDict",e)},deleteDict:function(e){return a.post("DeleteDict",e)},updateDict:function(e){return a.post("UpdateDict",e)},getDict:function(e){return a.get("GetDict",e)},querySysSetting:function(e){return a.get("QuerySysSetting",e)},addSysSetting:function(e){return a.post("AddSysSetting",e)},deleteSysSetting:function(e){return a.post("DeleteSysSetting",e)},updateSysSetting:function(e){return a.post("UpdateSysSetting",e)},getSysSetting:function(e){return a.get("GetSysSetting",e)},queryLanguage:function(e){return a.get("QueryLanguage",e)},getEnumInfos:function(e){return a.get("GetEnumInfos",e)},queryUserGroups:function(e){return a.post("QueryUserGroups",e)},saveUserGroup:function(e){return a.post("SaveUserGroup",e)},deleteUserGroup:function(e){return a.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return a.get("DropdownUserGroups",e)},queryUsers:function(e){return a.post("QueryUsers",e)},saveUser:function(e){return a.post("SaveUser",e)},deleteUser:function(e){return a.post("DeleteUser",e)},initPwd:function(e){return a.post("InitPwd",e)},getUserById:function(e){return a.get("GetUserById",e)},queryEmployees:function(e){return a.post("QueryEmployees",e)},queryModuleInfos:function(e){return a.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return a.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return a.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return a.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return a.post("SaveRightOfDept",e)},queryControlRight:function(e){return a.post("QueryControlRight",e)},saveControlRights:function(e){return a.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return a.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return a.get("QueryStationTree",e)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(e){return a.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return a.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)}}}}]);