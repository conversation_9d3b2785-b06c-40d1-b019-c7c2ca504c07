(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5384b60f"],{"06c5":function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));a("a630"),a("fb6a"),a("b0c0"),a("d3b7"),a("25f0"),a("3ca3");var o=a("6b75");function n(e,t){if(e){if("string"===typeof e)return Object(o["a"])(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?Object(o["a"])(e,t):void 0}}},"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"19de":function(e,t){e.exports=function(e,t,a,o){var n="undefined"!==typeof o?[o,e]:[e],r=new Blob(n,{type:a||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(r,t);else{var l=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(r):window.webkitURL.createObjectURL(r),i=document.createElement("a");i.style.display="none",i.href=l,i.setAttribute("download",t),"undefined"===typeof i.download&&i.setAttribute("target","_blank"),document.body.appendChild(i),i.click(),setTimeout((function(){document.body.removeChild(i),window.URL.revokeObjectURL(l)}),200)}}},"6b3f":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,width:"80%","close-on-press-escape":!1,visible:!0,"append-to-body":""},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"唯一码",prop:"uid"}},[e._v(" "+e._s(e.dataModel.uid)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工号",prop:"empCode"}},[e._v(" "+e._s(e.dataModel.empCode)+" ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名",prop:"displayName"}},[e._v(" "+e._s(e.dataModel.displayName)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工龄",prop:"societyAge"}},[e._v(" "+e._s(e.dataModel.societyAge)+" ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"薪资类型",prop:"enumSalaryType"}},[a("el-select",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,placeholder:"薪资类型"},on:{change:e.salaryTypeChange},model:{value:e.dataModel.enumSalaryType,callback:function(t){e.$set(e.dataModel,"enumSalaryType",t)},expression:"dataModel.enumSalaryType"}},e._l(e.salaryTypes,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"薪资状态",prop:"enumEmployeeSalaryStatus"}},[a("el-select",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,placeholder:"薪资状态"},model:{value:e.dataModel.enumEmployeeSalaryStatus,callback:function(t){e.$set(e.dataModel,"enumEmployeeSalaryStatus",t)},expression:"dataModel.enumEmployeeSalaryStatus"}},e._l(e.employeeSalaryStatuss,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1)],1)],1),a("el-row",[1==e.dataModel.enumSalaryType||3==e.dataModel.enumSalaryType?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"岗位类型",prop:"stationIds"}},[a("el-cascader",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,options:e.stationTrees,props:{expandTrigger:"hover",value:"id",label:"name"}},on:{change:e.stationTreeChange},model:{value:e.dataModel.stationIds,callback:function(t){e.$set(e.dataModel,"stationIds",t)},expression:"dataModel.stationIds"}})],1)],1):e._e(),1===e.dataModel.enumSalaryType||3===e.dataModel.enumSalaryType?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"薪级",prop:"salaryScaleId"}},[a("el-select",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,placeholder:"薪级"},model:{value:e.dataModel.salaryScaleId,callback:function(t){e.$set(e.dataModel,"salaryScaleId",t)},expression:"dataModel.salaryScaleId"}},e._l(e.salaryScales,(function(e){return a("el-option",{key:e.id,attrs:{label:e.scale.toString(),value:e.id}})})),1)],1)],1):e._e()],1),1==e.dataModel.enumSalaryType||2==e.dataModel.enumSalaryType||3==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"社保基数",prop:"socialSecurityBase"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,placeholder:"请输入社保基数",clearable:"",maxlength:"8"},model:{value:e.dataModel.socialSecurityBase,callback:function(t){e.$set(e.dataModel,"socialSecurityBase",t)},expression:"dataModel.socialSecurityBase"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公积金基数",prop:"housingFundBase"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,placeholder:"请输入公积金基数",clearable:"",maxlength:"8"},model:{value:e.dataModel.housingFundBase,callback:function(t){e.$set(e.dataModel,"housingFundBase",t)},expression:"dataModel.housingFundBase"}})],1)],1)],1):e._e(),2==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"房贴到期时间",prop:"housingAllowanceEndDate"}},[a("el-date-picker",{staticStyle:{width:"80%"},attrs:{type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd",disabled:!e.isEdit,clearable:!1},model:{value:e.dataModel.housingAllowanceEndDate,callback:function(t){e.$set(e.dataModel,"housingAllowanceEndDate",t)},expression:"dataModel.housingAllowanceEndDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"薪资到期时间",prop:"postdoctorEndDate"}},[a("el-date-picker",{staticStyle:{width:"80%"},attrs:{type:"date",placeholder:"请选择薪资到期时间","value-format":"yyyy-MM-dd",disabled:!e.isEdit,clearable:!1},model:{value:e.dataModel.postdoctorEndDate,callback:function(t){e.$set(e.dataModel,"postdoctorEndDate",t)},expression:"dataModel.postdoctorEndDate"}})],1)],1)],1):e._e(),2==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"导师支出单",prop:"tutorNote"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{maxlength:"300",disabled:!e.isEdit,clearable:!1},model:{value:e.dataModel.tutorNote,callback:function(t){e.$set(e.dataModel,"tutorNote",t)},expression:"dataModel.tutorNote"}})],1)],1)],1):e._e(),1==e.dataModel.enumSalaryType||3==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"职业年金"}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.occupationalAnnuity,callback:function(t){e.$set(e.dataModel,"occupationalAnnuity",t)},expression:"dataModel.occupationalAnnuity"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"补充公积金"}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.supplementaryHousing,callback:function(t){e.$set(e.dataModel,"supplementaryHousing",t)},expression:"dataModel.supplementaryHousing"}})],1)],1)],1):e._e(),1==e.dataModel.enumSalaryType||3==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"管理津贴"}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.managementAllowance,callback:function(t){e.$set(e.dataModel,"managementAllowance",t)},expression:"dataModel.managementAllowance"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"卫生津贴"}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.isHealthAllowance,callback:function(t){e.$set(e.dataModel,"isHealthAllowance",t)},expression:"dataModel.isHealthAllowance"}})],1)],1)],1):e._e(),1==e.dataModel.enumSalaryType||3==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"独生子女"}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.onlyChild,callback:function(t){e.$set(e.dataModel,"onlyChild",t)},expression:"dataModel.onlyChild"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计税"}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.taxCalculation,callback:function(t){e.$set(e.dataModel,"taxCalculation",t)},expression:"dataModel.taxCalculation"}})],1)],1)],1):e._e(),1==e.dataModel.enumSalaryType||3==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"护理"}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.nursing,callback:function(t){e.$set(e.dataModel,"nursing",t)},expression:"dataModel.nursing"}})],1)],1),e.dataModel.nursing?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"特殊护龄工资"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.specialNursing,callback:function(t){e.$set(e.dataModel,"specialNursing",t)},expression:"dataModel.specialNursing"}})],1),e.dataModel.specialNursing?a("el-col",{attrs:{span:20}},[a("el-input-number",{staticStyle:{width:"50%","margin-left":"1%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"请输入金额",clearable:""},model:{value:e.dataModel.nursingAllowance,callback:function(t){e.$set(e.dataModel,"nursingAllowance",t)},expression:"dataModel.nursingAllowance"}})],1):e._e()],1)],1)],1):e._e()],1):e._e(),1!=e.dataModel.enumSalaryType&&3!=e.dataModel.enumSalaryType||!e.dataModel.managementAllowance?e._e():a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"电话费",prop:"telephoneFeeId"}},[a("el-select",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,placeholder:"电话费"},model:{value:e.dataModel.telephoneFeeId,callback:function(t){e.$set(e.dataModel,"telephoneFeeId",t)},expression:"dataModel.telephoneFeeId"}},e._l(e.telephoneFees,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公车津贴",prop:"carSubsidyId"}},[a("el-select",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,placeholder:"公车津贴",clearable:""},model:{value:e.dataModel.carSubsidyId,callback:function(t){e.$set(e.dataModel,"carSubsidyId",t)},expression:"dataModel.carSubsidyId"}},e._l(e.carSubsidys,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",[2==e.dataModel.enumSalaryType?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"年薪",prop:"yearlySalary"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.yearlySalary,callback:function(t){e.$set(e.dataModel,"yearlySalary",t)},expression:"dataModel.yearlySalary"}})],1),a("el-col",{staticStyle:{"text-align":"center","line-height":"32px"},attrs:{span:4}},[e._v(" 万元 ")]),e.isEdit?a("el-col",{attrs:{span:4}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.calculateYearlySalary}},[e._v("计算月薪")])],1):e._e()],1)],1)],1):e._e(),2==e.dataModel.enumSalaryType||4==e.dataModel.enumSalaryType?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"月薪",prop:"monthlySalary"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.monthlySalary,callback:function(t){e.$set(e.dataModel,"monthlySalary",t)},expression:"dataModel.monthlySalary"}})],1)],1):e._e()],1),1==e.dataModel.enumSalaryType||3==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"会费",prop:"membershipFee"}},[a("el-row",[a("el-col",{attrs:{span:16}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"请输入会费",clearable:""},model:{value:e.dataModel.membershipFee,callback:function(t){e.$set(e.dataModel,"membershipFee",t)},expression:"dataModel.membershipFee"}})],1),e.isEdit?a("el-col",{attrs:{span:8}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.calculateMembershipFee}},[e._v("计算")])],1):e._e()],1)],1)],1),e.dataModel.isHealthAllowance?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"卫生津贴",prop:"healthAllowance"}},[a("el-row",[a("el-col",{attrs:{span:16}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"请输入卫生津贴",clearable:""},model:{value:e.dataModel.healthAllowance,callback:function(t){e.$set(e.dataModel,"healthAllowance",t)},expression:"dataModel.healthAllowance"}})],1),e.isEdit?a("el-col",{attrs:{span:8}},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.showHealthAllowanceDialog}},[e._v("选择")])],1):e._e()],1)],1)],1):e._e()],1):e._e(),a("el-row",[1==e.dataModel.enumSalaryType||3==e.dataModel.enumSalaryType?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"特殊情况"}},[a("el-switch",{attrs:{disabled:!e.isEdit,"active-color":"#13ce66"},model:{value:e.dataModel.special,callback:function(t){e.$set(e.dataModel,"special",t)},expression:"dataModel.special"}})],1)],1):e._e()],1),e.dataModel.special||3==e.dataModel.enumSalaryType?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"岗位工资",prop:"stationWage"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.stationWage,callback:function(t){e.$set(e.dataModel,"stationWage",t)},expression:"dataModel.stationWage"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"岗位津贴",prop:"stationAllowances"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.stationAllowances,callback:function(t){e.$set(e.dataModel,"stationAllowances",t)},expression:"dataModel.stationAllowances"}})],1)],1)],1):e._e(),e.dataModel.special?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"薪级工资",prop:"scaleWage"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.scaleWage,callback:function(t){e.$set(e.dataModel,"scaleWage",t)},expression:"dataModel.scaleWage"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工作量津贴",prop:"workAllowance"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.workAllowance,callback:function(t){e.$set(e.dataModel,"workAllowance",t)},expression:"dataModel.workAllowance"}})],1)],1)],1):e._e(),e.dataModel.special?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"上下班交通费",prop:"commutingAllowance"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.commutingAllowance,callback:function(t){e.$set(e.dataModel,"commutingAllowance",t)},expression:"dataModel.commutingAllowance"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"停车补贴",prop:"parkingSubsidyAllowance"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.parkingSubsidyAllowance,callback:function(t){e.$set(e.dataModel,"parkingSubsidyAllowance",t)},expression:"dataModel.parkingSubsidyAllowance"}})],1)],1)],1):e._e(),e.dataModel.special?a("el-row",[e.dataModel.managementAllowance&&e.dataModel.telephoneFeeId?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"电话费",prop:"telephoneFeeAllowance"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.telephoneFeeAllowance,callback:function(t){e.$set(e.dataModel,"telephoneFeeAllowance",t)},expression:"dataModel.telephoneFeeAllowance"}})],1)],1):e._e(),a("el-col",{attrs:{span:12}},[e.dataModel.managementAllowance&&e.dataModel.carSubsidyId?a("el-form-item",{attrs:{label:"公车津贴",prop:"carSubsidyAllowance"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.carSubsidyAllowance,callback:function(t){e.$set(e.dataModel,"carSubsidyAllowance",t)},expression:"dataModel.carSubsidyAllowance"}})],1):e._e()],1)],1):e._e(),e.dataModel.special?a("el-row",[e.dataModel.onlyChild?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"独子",prop:"oneChildAllowance"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.oneChildAllowance,callback:function(t){e.$set(e.dataModel,"oneChildAllowance",t)},expression:"dataModel.oneChildAllowance"}})],1)],1):e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"粮油补贴",prop:"foodAllowance"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{disabled:!e.isEdit,min:0,precision:2,controls:!1,placeholder:"",clearable:""},model:{value:e.dataModel.foodAllowance,callback:function(t){e.$set(e.dataModel,"foodAllowance",t)},expression:"dataModel.foodAllowance"}})],1)],1)],1):e._e(),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{staticStyle:{width:"90%"},attrs:{disabled:!e.isEdit,type:"textarea",rows:3,clearable:"",placeholder:"备注",maxlength:"300"},model:{value:e.dataModel.remark,callback:function(t){e.$set(e.dataModel,"remark",t)},expression:"dataModel.remark"}})],1)],1)],1)],1),e.dataModel.enumSalaryType?a("el-row",[a("el-row",{staticStyle:{"margin-left":"5%","margin-bottom":"1%","font-weight":"bold"}},[e._v(" 基础薪资预估: "+e._s(e._f("formatMoney2")(e.estimatedSalary))+" "),e.isDataChanged&&e.isEdit?a("span",{staticStyle:{color:"#E6A23C","margin-left":"20px","font-size":"14px"}},[e._v(" 已修改数据，请重新计算薪资预估 ")]):e._e()]),4!==e.dataModel.enumSalaryType?a("el-table",{staticStyle:{width:"90%",margin:"0 auto"},attrs:{data:e.filteredSalaryData,border:""}},[a("el-table-column",{attrs:{prop:"date",label:"内容","min-width":"370",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.content))])]}}],null,!1,3060787437)}),a("el-table-column",{attrs:{prop:"name",label:"薪资/津贴（元）","min-width":"180","header-align":"right",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[o.allowanceAmount?a("span",[e._v(e._s(e._f("formatMoney2")(o.allowanceAmount)))]):e._e()]}}],null,!1,3217541031)}),a("el-table-column",{attrs:{prop:"address",label:"扣款（元）","min-width":"180","header-align":"right",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[o.deductionAmount?a("span",{staticStyle:{color:"red"}},[e._v(e._s(e._f("formatMoney2")(o.deductionAmount)))]):e._e()]}}],null,!1,3860076938)})],1):e._e()],1):e._e(),e.isEdit?a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",loading:e.btnCalculateLoading},on:{click:e.calculateSalaryEstimate}},[e._v("计算薪资预估")]),a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1):e._e()],1)],1)},n=[],r=(a("99af"),a("4de4"),a("7db0"),a("4160"),a("d81d"),a("b680"),a("159b"),a("b85c")),l=a("53ca"),i=a("2efc"),u=a("f9ac"),d=a("cbd2"),c={props:{employeeSalaryId:{type:String,default:""},isEdit:{type:Boolean,default:!1}},data:function(){var e=this,t=function(e,t,a){/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/.test(t)?a():a(new Error("请输入0以上的数字"))};return{visible:!0,title:"员工薪资查看",salaryTypes:[],employeeSalaryStatuss:[],stationId:[],stationIds:[],stationTypes:[],stations:[],stationTrees:[],salaryScales:[],telephoneFees:[],carSubsidys:[],stationAllowances:[],salaryData:[],stationVariableWage:[],rules:{enumSalaryType:{required:!0,message:"请选择薪资类型",trigger:"change"},enumEmployeeSalaryStatus:{required:!0,message:"请选择薪资状态",trigger:"change"},stationIds:{required:!0,message:"请选择岗位",trigger:"change"},salaryScaleId:[{required:!0,message:"请选择薪级",trigger:"change"}],socialSecurityBase:[{required:!0,message:"社保基数必填",trigger:"blur"},{validator:t,trigger:"blur"}],housingFundBase:[{required:!0,message:"公积金基数必填",trigger:"blur"},{validator:t,trigger:"blur"}],housingAllowanceEndDate:[{required:!0,message:"房贴到期时间必填",trigger:"change",validator:function(t,a,o){2==e.dataModel.enumSalaryType?a?o():o(new Error("房贴到期时间必填")):o()}}],postdoctorEndDate:[{required:!0,message:"薪资到期时间必填",trigger:"change",validator:function(t,a,o){2==e.dataModel.enumSalaryType?a?o():o(new Error("薪资到期时间必填")):o()}}],monthlySalary:[{required:!0,message:"月薪必填",trigger:"blur"},{validator:t,trigger:"blur"}],stationWage:[{required:!0,message:"岗位工资必填",trigger:"blur"},{validator:t,trigger:"blur"}],stationAllowances:[{required:!0,message:"岗位津贴必填",trigger:"blur"},{validator:t,trigger:"blur"}],membershipFee:[{required:!0,message:"会费必填",trigger:"blur"},{validator:t,trigger:"blur"}],healthAllowance:[{required:!0,message:"卫生津贴必填",trigger:"blur"},{validator:t,trigger:"blur"}]},pickerOptionsArrivalDate:{disabledDate:function(t){var a=new Date(e.dataModel.effHireDate),o=a.setYear(a.getFullYear()+2),n=new Date(o);return t.getTime()<n.getTime()}},btnSaveLoading:!1,btnCalculateLoading:!1,dataModel:{enumSalaryType:"",enumEmployeeSalaryStatus:"",special:!1,taxCalculation:!1,isHealthAllowance:!1},onlyChildValBak:0,telephoneFeeValBak:0,carValBak:0,staffingValBak:0,trainingValBak:0,nursingValBak:0,stationSalaryBak:0,scaleSalaryBak:0,estimatedSalary:0,originalDataModel:null,isDataChanged:!1}},computed:{filteredSalaryData:function(){return this.salaryData.filter((function(e){return e.allowanceAmount||e.deductionAmount}))}},watch:{dataModel:{handler:function(){this.isEdit&&this.originalDataModel&&this.checkDataChanges()},deep:!0,immediate:!1}},created:function(){!0===this.isEdit?this.title="员工薪资编辑":this.title="员工薪资显示",this.initSalaryTypeList(),this.initSalaryStatusList(),this.queryStationTypeSelector(),this.queryTelephoneFeeSelector(),this.queryCarSubsidySelector(),this.queryStationTree(),this.queryStationAllowance(),this.getData(this.employeeSalaryId)},methods:{initSalaryTypeList:function(){var e=this,t={enumType:"SalaryType"};u["a"].getEnumInfos(t).then((function(t){e.salaryTypes=t.data.datas})).catch((function(e){console.log(e)}))},initSalaryStatusList:function(){var e=this,t={enumType:"EmployeeSalaryStatus"};u["a"].getEnumInfos(t).then((function(t){e.employeeSalaryStatuss=t.data.datas})).catch((function(e){console.log(e)}))},queryStationTree:function(){var e=this;u["a"].queryStationTree().then((function(t){e.stationTrees=t.data,e.stationVariableWage=t.data.filter((function(e){return!0===e.isVariableWage}))})).catch((function(e){console.log(e)}))},queryStationTypeSelector:function(){var e=this;u["a"].queryStationTypeSelector().then((function(t){e.stationTypes=t.data})).catch((function(e){console.log(e)}))},queryStationAllowance:function(){var e=this;u["a"].queryStationAllowance().then((function(t){e.stationAllowances=t.data})).catch((function(e){console.log(e)}))},querySalaryScaleSelector:function(e){var t=this;u["a"].querySalaryScaleSelector({id:e}).then((function(e){t.salaryScales=e.data})).catch((function(e){console.log(e)}))},queryTelephoneFeeSelector:function(){var e=this;u["a"].queryTelephoneFeeSelector().then((function(t){e.telephoneFees=t.data})).catch((function(e){console.log(e)}))},queryCarSubsidySelector:function(){var e=this;u["a"].queryCarSubsidySelector().then((function(t){e.carSubsidys=t.data})).catch((function(e){console.log(e)}))},getData:function(e){var t=this;i["a"].getEmployeeSalary({id:e}).then((function(e){if(e.succeed)if(!0===e.data.isNewEmpSalary){var a=JSON.parse(JSON.stringify(e.data));t.$confirm("首次维护人员薪资信息，会根据现有人事信息自动初始化部分信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.dataModel=a,0===a.enumEmployeeSalaryStatus&&(t.dataModel.enumEmployeeSalaryStatus=""),t.initDataModel(),t.calculateEstimatedTotal()})).catch((function(){t.dataModel=t.clearBackendInitializedValues(a),0===t.dataModel.enumEmployeeSalaryStatus&&(t.dataModel.enumEmployeeSalaryStatus=""),t.initDataModel(),t.saveOriginalData()}))}else t.dataModel=e.data,0===e.data.enumSalaryType&&(t.dataModel.enumSalaryType=""),0===e.data.enumEmployeeSalaryStatus&&(t.dataModel.enumEmployeeSalaryStatus=""),t.initDataModel(),t.saveOriginalData(),t.calculateEstimatedTotal()})).catch((function(e){console.log(e)}))},clearBackendInitializedValues:function(e){var t=JSON.parse(JSON.stringify(e));return 0!==t.enumSalaryType&&(t.enumSalaryType=""),t.socialSecurityBase=null,t.housingFundBase=null,t.occupationalAnnuity=!1,t.managementAllowance=!1,t.nursing=!1,t.housingAllowanceEndDate=null,t.postdoctorEndDate=null,t.telephoneFeeId=null,t.carSubsidyId=null,t},saveOriginalData:function(){this.originalDataModel=JSON.parse(JSON.stringify(this.dataModel)),this.isDataChanged=!1},checkDataChanges:function(){if(this.originalDataModel){var e=JSON.stringify(this.dataModel),t=JSON.stringify(this.originalDataModel);this.isDataChanged=e!==t}},initDataModel:function(){this.dataModel.stationIds&&(this.dataModel.stationIds=JSON.parse(this.dataModel.stationIds)),this.initSalaryBase(),null!==this.dataModel.parentStationId&&void 0!==this.dataModel.parentStationId&&""!==this.dataModel.parentStationId&&this.querySalaryScaleSelector(this.dataModel.parentStationId)},initSalaryBase:function(){1===this.dataModel.enumSalaryType?this.salaryData=this.dataModel.monthSalaryBaseModels:2===this.dataModel.enumSalaryType?this.salaryData=this.dataModel.yearSalaryBaseModels:3===this.dataModel.enumSalaryType&&(this.salaryData=this.dataModel.monthSalaryBaseModels)},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){var a=!1;if(e.dataModel.special&&(e.dataModel.remark||(a=!0,e.$message.error("特殊情况备注必填"))),t&&!a){e.btnSaveLoading=!0;var o=e.dataModel.stationIds;e.checkStationId(),e.dataModel.monthSalaryBaseModels=[],e.dataModel.yearSalaryBaseModels=[],i["a"].updateEmployeeSalary(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"保存成功",type:"success"}),e.btnSaveLoading=!1,e.closeDialog())})).catch((function(){e.dataModel.stationIds=o,e.btnSaveLoading=!1}))}}))},checkStationId:function(){var e=this.dataModel.stationIds;Array.isArray(e)&&e.length>0?(this.dataModel.stationId=e[e.length-1],this.dataModel.parentStationId=e[0],this.dataModel.stationIds=JSON.stringify(this.dataModel.stationIds)):(this.dataModel.stationId="",this.dataModel.parentStationId="")},closeDialog:function(){this.$emit("refresh")},salaryTypeChange:function(){this.dataModel.special&&(this.dataModel.special=!1),this.$refs.dataForm.clearValidate(),this.initSalaryBase()},stationTreeChange:function(){this.dataModel.salaryScaleId&&(this.dataModel.salaryScaleId=""),"object"===Object(l["a"])(this.dataModel.stationIds)&&this.dataModel.stationIds.length>0&&(1!==this.dataModel.enumSalaryType&&3!==this.dataModel.enumSalaryType||this.querySalaryScaleSelector(this.dataModel.stationIds[0]))},membershipFeeChange:function(e){var t=this,a=0,o=this.dataModel.stationIds;if(Array.isArray(o)&&o.length>0?(this.dataModel.stationId=o[o.length-1],this.dataModel.parentStationId=o[0]):(this.dataModel.stationId="",this.dataModel.parentStationId=""),this.dataModel.stationIds&&this.dataModel.stationIds.length>0){var n=null,l=function e(a){var o,l=Object(r["a"])(a);try{for(l.s();!(o=l.n()).done;){var i=o.value;if(i.id===t.dataModel.stationId)return n=i,!0;if(i.children&&Array.isArray(i.children)){var u=e(i.children);if(u)return!0}}}catch(d){l.e(d)}finally{l.f()}return!1};l(this.stationTrees),n&&n.wage&&(a=parseFloat(n.wage)||0)}var i=0;if(this.dataModel.salaryScaleId){var u=this.salaryScales.find((function(e){return e.id===t.dataModel.salaryScaleId}));u&&u.wage&&(i=parseFloat(u.wage)||0)}var d=a+i,c=(this.dataModel.membershipFeeBase||0)*d/100,s=Math.ceil(10*c)/10;return this.dataModel.membershipFee=s,s},calculateMembershipFee:function(){this.dataModel.stationIds&&0!==this.dataModel.stationIds.length?this.membershipFeeChange():this.$message.warning("请先选择岗位类型")},calculateYearlySalary:function(){if(2===this.dataModel.enumSalaryType){var e=parseFloat(this.dataModel.yearlySalary)||0;if(e<=0)this.$message.warning("请先输入年薪金额");else{var t=0;if(this.salaryData&&this.salaryData.length>0){var a=this.salaryData.find((function(e){return"博士后房贴"===e.content}));a&&a.allowanceAmount&&(t=parseFloat(a.allowanceAmount)||0)}t<=0&&(t=2500);var o=(1e4*e/12-t).toFixed(2);this.dataModel.monthlySalary=parseFloat(o)}}else this.$message.warning("只有博士后薪资类型才能使用计算月薪功能")},calculateSalaryEstimate:function(){var e=this;if(this.dataModel.enumSalaryType){this.btnCalculateLoading=!0;var t=this.dataModel.stationIds;Array.isArray(t)&&t.length>0?(this.dataModel.stationId=t[t.length-1],this.dataModel.parentStationId=t[0],this.dataModel.stationIds=JSON.stringify(this.dataModel.stationIds)):(this.dataModel.stationId="",this.dataModel.parentStationId=""),i["a"].calculateEmployeeSalaryEstimate(this.dataModel).then((function(a){e.dataModel.stationIds=t,a.succeed?(e.salaryData=a.data||[],e.calculateEstimatedTotal(),e.saveOriginalData(),e.$message.success("薪资预估计算完成")):e.$message.error(a.message||"计算薪资预估失败"),e.btnCalculateLoading=!1})).catch((function(a){console.error("计算薪资预估失败:",a),e.dataModel.stationIds=t,e.btnCalculateLoading=!1,e.$message.error("计算薪资预估失败，请稍后重试")}))}else this.$message.warning("请先选择薪资类型")},calculateEstimatedTotal:function(){var e=this;4===this.dataModel.enumSalaryType?this.estimatedSalary=this.dataModel.monthlySalary||0:(this.estimatedSalary=0,this.salaryData&&this.salaryData.length>0&&this.salaryData.forEach((function(t){t.allowanceAmount&&(e.estimatedSalary+=parseFloat(t.allowanceAmount)),t.deductionAmount&&(e.estimatedSalary-=parseFloat(t.deductionAmount))})))},showHealthAllowanceDialog:function(){var e=this;this.$confirm("显示同部门员工卫生津贴","选择卫生津贴",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((function(){var t={deptId:e.dataModel.deptId};d["a"].getSameDeptEmployeeWithHealthAllowance(t).then((function(t){if(t.data){var a=t.data.filter((function(e){return e.h1>0})).map((function(e){return{displayName:e.empName,empCode:e.empCode,empUid:e.empUid,h1Value:e.h1}}));a.length>0?e.showHealthAllowanceList(a):e.$message.warning("没有找到同部门员工卫生津贴数据")}else e.$message.warning("没有找到同部门员工卫生津贴数据")})).catch((function(t){console.error(t),e.$message.error("获取数据失败")}))})).catch((function(){}))},showHealthAllowanceList:function(e){var t=this,a=this.$createElement,o=[{prop:"displayName",label:"姓名"},{prop:"empCode",label:"工号"},{prop:"h1Value",label:"卫生津贴",formatter:function(e){return e.h1Value+" 元"}},{prop:"operation",label:"操作",render:function(e,a){var o=a.row;return e("el-button",{props:{type:"primary",size:"mini"},on:{click:function(){t.selectHealthAllowance(o)}}},"选择")}}],n=a("div",{style:{maxHeight:"400px",overflow:"auto"}},[a("el-table",{props:{data:e,border:!0,stripe:!0},style:{width:"100%"}},o.map((function(e){return a("el-table-column",{props:{prop:e.prop,label:e.label},scopedSlots:e.render?{default:function(t){return e.render(a,t)}}:null})})))]);this.$msgbox({title:"选择同部门员工卫生津贴",message:n,showCancelButton:!0,confirmButtonText:"关闭",cancelButtonText:"取消",beforeClose:function(e,t,a){a()}})},selectHealthAllowance:function(e){e&&e.h1Value&&(this.dataModel.healthAllowance=e.h1Value,this.$message.success("已选择 ".concat(e.displayName," 的卫生津贴：").concat(e.h1Value," 元")),this.$msgbox.close())}}},s=c,y=(a("c205"),a("2877")),p=Object(y["a"])(s,o,n,!1,null,null,null);t["a"]=p.exports},"6b75":function(e,t,a){"use strict";function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,o=new Array(t);a<t;a++)o[a]=e[a];return o}a.d(t,"a",(function(){return o}))},"841c":function(e,t,a){"use strict";var o=a("d784"),n=a("825a"),r=a("1d80"),l=a("129f"),i=a("14c3");o("search",1,(function(e,t,a){return[function(t){var a=r(this),o=void 0==t?void 0:t[e];return void 0!==o?o.call(t,a):new RegExp(t)[e](String(a))},function(e){var o=a(t,e,this);if(o.done)return o.value;var r=n(e),u=String(this),d=r.lastIndex;l(d,0)||(r.lastIndex=0);var c=i(r,u);return l(r.lastIndex,d)||(r.lastIndex=d),null===c?-1:c.index}]}))},b85c:function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));a("a4d3"),a("e01a"),a("d28b"),a("d3b7"),a("3ca3"),a("ddb0");var o=a("06c5");function n(e,t){var a;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(a=Object(o["a"])(e))||t&&e&&"number"===typeof e.length){a&&(e=a);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,i=!0,u=!1;return{s:function(){a=e[Symbol.iterator]()},n:function(){var e=a.next();return i=e.done,e},e:function(e){u=!0,l=e},f:function(){try{i||null==a["return"]||a["return"]()}finally{if(u)throw l}}}}},c205:function(e,t,a){"use strict";var o=a("fd3f"),n=a.n(o);n.a},cbd2:function(e,t,a){"use strict";var o=a("cfe3"),n="AttendanceManage",r=new o["a"](n);t["a"]={getAttMonthShiftRecord:function(e){return r.get("GetAttMonthShiftRecord",e)},queryAttMonthShiftRecordDetail:function(e){return r.get("QueryAttMonthShiftRecordDetail",e)},batchConfirmAttMonthShiftRecord:function(e){return r.post("BatchConfirmAttMonthShiftRecord",e)},saveAttMonthShiftRecord:function(e){return r.post("SaveAttMonthShiftRecord",e)},submitAttMonthShiftRecord:function(e){return r.post("SubmitAttMonthShiftRecord",e)},ConfirmAttMonthShiftRecord:function(e){return r.post("ConfirmAttMonthShiftRecord",e)},rejectAttMonthShiftRecord:function(e){return r.post("RejectAttMonthShiftRecord",e)},searchAttMonthShiftRecordDetail:function(e){return r.get("SearchAttMonthShiftRecordDetail",e)},searchAttMonthShiftRecordDetail_Update:function(e){return r.get("SearchAttMonthShiftRecordDetail_Update",e)},updateAttMonthShiftRecordDetail:function(e){return r.post("UpdateAttMonthShiftRecordDetail",e)},getColorDeptTree_MiddleNightShift:function(e){return r.get("GetColorDeptTree_MiddleNightShift",e)},get_MiddleNightShiftReportExcel:function(e){return r.getFile("Get_MiddleNightShiftReportExcel",e)},getAttHolidayOTRecord:function(e){return r.get("GetAttHolidayOTRecord",e)},queryAttHolidayOTRecordDetail:function(e){return r.get("QueryAttHolidayOTRecordDetail",e)},saveAttHolidayOTRecord:function(e){return r.post("SaveAttHolidayOTRecord",e)},batchConfirmAttHolidayOTRecord:function(e){return r.post("BatchConfirmAttHolidayOTRecord",e)},submitAttHolidayOTRecord:function(e){return r.post("SubmitAttHolidayOTRecord",e)},ConfirmAttHolidayOTRecord:function(e){return r.post("ConfirmAttHolidayOTRecord",e)},rejectAttHolidayOTRecord:function(e){return r.post("RejectAttHolidayOTRecord",e)},searchAttHolidayOTRecordDetail:function(e){return r.get("SearchAttHolidayOT",e)},searchAttHolidayOTRecordDetail_Update:function(e){return r.get("SearchAttHolidayOTRecordDetail_Update",e)},updateAttHolidayOTRecordDetail:function(e){return r.post("UpdateAttHolidayOTRecordDetail",e)},getColorDeptTree_HolidayOT:function(e){return r.get("GetColorDeptTree_HolidayOT",e)},getOTReportExcel:function(e){return r.getFile("GetOTReportExcel",e)},getAttDayOffRecord:function(e){return r.get("GetAttDayOffRecord",e)},queryAttDayOffRecordDetail:function(e){return r.get("QueryAttDayOffRecordDetail",e)},saveAttDayOffRecord:function(e){return r.post("SaveAttDayOffRecord",e)},submitAttDayOffRecord:function(e){return r.post("SubmitAttDayOffRecord",e)},updateApproveAttDayOffRecord:function(e){return r.post("UpdateApproveAttDayOffRecord",e)},rejectAttDayOffRecord:function(e){return r.post("RejectAttDayOffRecord",e)},searchAttDayOffRecordDetail:function(e){return r.get("SearchAttDayOffRecordDetail",e)},searchAttDayOffRecordDetail_Update:function(e){return r.get("SearchAttDayOffRecordDetail_Update",e)},updateAttDayOffRecordDetail:function(e){return r.post("UpdateAttDayOffRecordDetail",e)},getColorDeptTree_DayOff:function(e){return r.get("GetColorDeptTree_DayOff",e)},getDayOffReportExcel:function(e){return r.getFile("GetDayOffReportExcel",e)},searchAttDayOffRecordDetail1:function(e){return r.get("SearchAttDayOffRecordDetail1",e)},searchAttMonthWatchRecord:function(e){return r.get("SearchAttMonthWatchRecord",e)},updateAttMonthWatchRecord:function(e){return r.post("UpdateAttMonthWatchRecord",e)},getMonthWatchTReportExcel:function(e){return r.getFile("GetMonthWatchTReportExcel",e)},getAttDayOffRecordDetail1Excel:function(e){return r.getFile("GetAttDayOffRecordDetail1Excel",e)},queryEmployeeList:function(e){return r.get("QueryEmployeeList",e)},searchAttDayOffRecordDetail2:function(e){return r.get("SearchAttDayOffRecordDetail2",e)},getAttDayOffRecordDetail2Excel:function(e){return r.getFile("GetAttDayOffRecordDetail2Excel",e)},searchAttDayOffRecordDetail3:function(e){return r.get("SearchAttDayOffRecordDetail3",e)},getAttDayOffRecordDetail3Excel:function(e){return r.getFile("GetAttDayOffRecordDetail3Excel",e)},queryAttDayOffRecordProphylacticDetail:function(e){return r.get("QueryAttDayOffRecordProphylacticDetail",e)},exportAttDayOffRecordProphylacticDetail:function(e){return r.getFile("ExportAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylacticCase:function(e){return r.get("GetAttDayOffRecordProphylacticCase",e)},getAttDayOffRecordProphylacticDetail:function(e){return r.get("GetAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylactic:function(e){return r.get("GetAttDayOffRecordProphylactic",e)},addAttDayOffRecordProphylactic:function(e){return r.post("AddAttDayOffRecordProphylactic",e)},updateAttDayOffRecordProphylactic:function(e){return r.post("UpdateAttDayOffRecordProphylactic",e)},deleteAttDayOffRecordProphylacticDetail:function(e){return r.post("DeleteAttDayOffRecordProphylacticDetail",e)},subjectAttDayOffRecordProphylactic:function(e){return r.post("SubjectAttDayOffRecordProphylactic",e)},queryCheckRecordFilling:function(e){return r.get("QueryCheckRecordFilling",e)},queryPersonnelAttendanceData:function(e){return r.get("QueryPersonnelAttendanceData",e)},queryProphylacticChange:function(e){return r.get("QueryProphylacticChange",e)},exportProphylacticChange:function(e){return r.getFile("ExportProphylacticChange",e)},queryPersonnelPendingApproval:function(e){return r.get("QueryPersonnelPendingApproval",e)},approveAttDayOffRecord:function(e){return r.post("ApproveAttDayOffRecord",e)},getSameDeptEmployeeWithHealthAllowance:function(e){return r.get("GetSameDeptEmployeeWithHealthAllowance",e)}}},e44c:function(e,t,a){"use strict";a("4160"),a("b64b"),a("159b");var o=a("cfe3"),n="HR",r=new o["a"](n);t["a"]={queryEmployee:function(e){return r.get("QueryEmployee",e)},queryEmployeeByCommonCondition:function(e){return r.post("QueryEmployeeByCommonCondition",e)},queryEmployeeStatus:function(){return r.get("QueryEmployeeStatus")},queryRank:function(){return r.get("QueryRank")},queryAdministrativePosition:function(){return r.get("queryAdministrativePosition")},queryMajorTechnical:function(){return r.get("queryMajorTechnical")},queryOfficialRank:function(){return r.get("QueryOfficialRank")},queryHireStyle:function(){return r.get("QueryHireStyle")},queryLeaveStyle:function(){return r.get("QueryLeaveStyle")},queryMarryList:function(){return r.get("QueryMarryList")},queryNationality:function(){return r.get("QueryNationality")},queryRegisterType:function(){return r.get("QueryRegisterType")},deleteEmployee:function(e){return r.post("DeleteEmployee",e)},queryDocumentType:function(){return r.get("QueryDocumentType")},addEmployee:function(e){return r.post("AddEmployee",e)},isEmpCodeExists:function(e){var t={id:e.id,empCode:e.empCode};return r.get("IsEmpCodeExists",t)},checkIdentityNumber:function(e){var t={id:e.id,identityNumber:e.identityNumber};return r.get("checkIdentityNumber",t)},getEmployee:function(e){return r.get("GetEmployee",e)},updateEmployee:function(e){return r.post("UpdateEmployee",e)},getPersonnelInformation:function(e){return r.get("GetPersonnelInformation",e)},postAddDocumentInformation:function(e){return r.get("PostAddDocumentInformation",e)},postUpdateDocumentInformation:function(e){return r.get("PostUpdateDocumentInformation",e)},postDeleteDocumentInformation:function(e){return r.get("PostDeleteDocumentInformation",e)},queryEmployeeByConditions:function(e){return r.post("QueryEmployeeByConditions",e)},queryEmployeeByCommonConditions:function(e){return r.post("QueryEmployeeByCommonConditions",e)},queryEmployeeQuerySetting:function(e){return r.get("QueryEmployeeQuerySetting",e)},querySettingColumns:function(){return r.get("QueryEmployeeInfoQuerySettingColumns")},queryOperations:function(e){return r.get("QueryOperationByColumnType",e)},addEmployeeQuerySetting:function(e){return r.post("AddEmployeeQuerySetting",e)},deleteEmployeeQuerySetting:function(e){return r.post("DeleteEmployeeQuerySetting",e)},queryDegrees:function(){return r.get("QueryDegrees")},queryEducation:function(){return r.get("QueryEducation")},QuerySocialSecurity:function(){return r.get("QuerySocialSecurity")},queryParty:function(){return r.get("QueryParty")},queryRecruitmentCategory:function(){return r.get("QueryRecruitmentCategory")},queryRecruitmentCompany:function(){return r.get("QueryRecruitmentCompany")},getEmployeeHR:function(e){return r.get("GetEmployeeHR",e)},updateEmployeeHR:function(e){return r.post("UpdateEmployeeHR",e)},updateCompanyAge:function(){return r.post("UpdateCompanyAge")},calculateGeneralHoliday:function(){return r.get("CalculateGeneralHoliday")},queryStation:function(e){return r.get("QueryStation",e)},queryPositionStation:function(e){return r.get("QueryPositionStation",e)},queryEmployeeStation:function(e){return r.get("QueryEmployeeStation",e)},addEmployeeStation:function(e){return r.post("AddEmployeeStation",e)},updateEmployeeStation:function(e){return r.post("UpdateEmployeeStation",e)},deleteEmployeeStation:function(e){return r.post("DeleteEmployeeStation",e)},queryLevel:function(){return r.get("QueryLevel")},queryEmployeeCertify:function(e){return r.get("QueryEmployeeCertify",e)},addEmployeeCertify:function(e){return r.post("AddEmployeeCertify",e)},updateEmployeeCertify:function(e){return r.post("UpdateEmployeeCertify",e)},deleteEmployeeCertify:function(e){return r.post("DeleteEmployeeCertify",e)},queryGraduation:function(){return r.get("QueryGraduation")},queryLearnWay:function(){return r.get("QueryLearnWay")},queryEmployeeEducation:function(e){return r.get("QueryEmployeeEducation",e)},addEmployeeEducation:function(e){return r.post("AddEmployeeEducation",e)},updateEmployeeEducation:function(e){return r.post("UpdateEmployeeEducation",e)},deleteEmployeeEducation:function(e){return r.post("DeleteEmployeeEducation",e)},queryEmployeeWork:function(e){return r.get("QueryEmployeeWork",e)},addEmployeeWork:function(e){return r.post("AddEmployeeWork",e)},updateEmployeeWork:function(e){return r.post("UpdateEmployeeWork",e)},deleteEmployeeWork:function(e){return r.post("DeleteEmployeeWork",e)},queryAbroadType:function(){return r.get("QueryAbroadType")},queryEmployeeAbroad:function(e){return r.get("QueryEmployeeAbroadInfo",e)},addEmployeeAbroad:function(e){return r.post("AddEmployeeAbroadInfo",e)},updateEmployeeAbroad:function(e){return r.post("UpdateEmployeeAbroadInfo",e)},deleteEmployeeAbroad:function(e){return r.post("DeleteEmployeeAbroadInfo",e)},queryContractType:function(){return r.get("QueryContractType")},queryEmployeeContract:function(e){return r.get("QueryEmployeeContract",e)},queryRenewEmployeeContract:function(e){return r.get("QueryRenewEmployeeContract",e)},updateNextTimeRemind:function(e){return r.post("UpdateNextTimeRemind",e)},sendEmailForRenewRemind:function(e){return r.post("SendEmailForRenewRemind",e)},batchUpdateNextTimeRemind:function(e){return r.post("BatchUpdateNextTimeRemind",e)},batchSendEmailForRenewRemind:function(e){return r.post("BatchSendEmailForRenewRemind",e)},addEmployeeContract:function(e){return r.post("AddEmployeeContract",e)},updateEmployeeContract:function(e){return r.post("UpdateEmployeeContract",e)},deleteEmployeeContract:function(e){return r.post("DeleteEmployeeContract",e)},queryTrainLevel:function(){return r.get("QueryTrainLevel")},queryEmployeeTrain:function(e){return r.get("QueryEmployeeTrain",e)},addEmployeeTrain:function(e){return r.post("AddEmployeeTrain",e)},updateEmployeeTrain:function(e){return r.post("UpdateEmployeeTrain",e)},deleteEmployeeTrain:function(e){return r.post("DeleteEmployeeTrain",e)},queryYearList:function(){return r.get("QueryYearList")},queryEvaluateResult:function(){return r.get("QueryEvaluateResult")},queryEmployeeAssessment:function(e){return r.get("QueryEmployeeAssessment",e)},addEmployeeAssessment:function(e){return r.post("AddEmployeeAssessment",e)},updateEmployeeAssessment:function(e){return r.post("UpdateEmployeeAssessment",e)},deleteEmployeeAssessment:function(e){return r.post("DeleteEmployeeAssessment",e)},queryEmployeeDeduct:function(e){return r.get("QueryEmployeeDeduct",e)},addEmployeeDeduct:function(e){return r.post("AddEmployeeDeduct",e)},updateEmployeeDeduct:function(e){return r.get("UpdateEmployeeDeduct",e)},deleteEmployeeDeduct:function(e){return r.post("DeleteEmployeeDeduct",e)},downloadEmployeeDeductTemplate:function(e){return r.getFile("DownlodaEmployeeDeductTemplate",e)},importEmployeeDeduct:function(e,t){var a=new FormData;return t&&Object.keys(t).forEach((function(e){return a.append(e,t[e])})),a.append("file",e),r.postForm("ImportEmployeeDeduct",a)},queryEmployeeDeductUnCalculate:function(e){return r.get("QueryEmployeeDeductUnCalculate",e)},queryEmployeeDeductCalculate:function(e){return r.get("QueryEmployeeDeductCalculate",e)},getEmployeeDeductCalculate:function(e){return r.get("GetEmployeeDeductCalculate",e)},updateEmployeeDeductCalculate:function(e){return r.post("UpdateEmployeeDeductCalculate",e)},exportEmployeeDeductCalculate:function(e){return r.post("ExportEmployeeDeductCalculate",{data:e,responseType:"arraybuffer"})},queryEmployeeDeductWorkingAge:function(e){return r.get("QueryEmployeeDeductWorkingAge",e)},getEmployeeDeductWorkingAge:function(e){return r.get("GetEmployeeDeductWorkingAge",e)},addEmployeeDeductWorkingAge:function(e){return r.post("AddEmployeeDeductWorkingAge",e)},updateEmployeeDeductWorkingAge:function(e){return r.post("UpdateEmployeeDeductWorkingAge",e)},deleteEmployeeDeductWorkingAge:function(e){return r.post("DeleteEmployeeDeductWorkingAge",e)},queryIncentType:function(){return r.get("QueryIncentType")},queryIncentLevel:function(){return r.get("QueryIncentLevel")},queryEmployeeIncentive:function(e){return r.get("QueryEmployeeIncentive",e)},addEmployeeIncentive:function(e){return r.post("AddEmployeeIncentive",e)},updateEmployeeIncentive:function(e){return r.post("UpdateEmployeeIncentive",e)},deleteEmployeeIncentive:function(e){return r.post("DeleteEmployeeIncentive",e)},getEmployeeHealth:function(e){return r.get("GetEmployeeHealth",e)},updateEmployeeHealth:function(e){return r.post("UpdateEmployeeHealth",e)},queryAccidentType:function(){return r.get("QueryAccidentType")},queryEmployeeAccident:function(e){return r.get("QueryEmployeeAccident",e)},addEmployeeAccident:function(e){return r.post("AddEmployeeAccident",e)},updateEmployeeAccident:function(e){return r.post("UpdateEmployeeAccident",e)},deleteEmployeeAccident:function(e){return r.post("DeleteEmployeeAccident",e)},queryEmployeeTeach:function(e){return r.get("QueryEmployeeTeach",e)},addEmployeeTeach:function(e){return r.post("AddEmployeeTeach",e)},updateEmployeeTeach:function(e){return r.post("UpdateEmployeeTeach",e)},deleteEmployeeTeach:function(e){return r.post("DeleteEmployeeTeach",e)},queryEmployeeRelation:function(e){return r.get("QueryEmployeeRelation",e)},addEmployeeRelation:function(e){return r.post("AddEmployeeRelation",e)},updateEmployeeRelation:function(e){return r.post("UpdateEmployeeRelation",e)},deleteEmployeeRelation:function(e){return r.post("DeleteEmployeeRelation",e)},queryIncomeType:function(){return r.get("QueryIncomeType")},queryEmployeeArticle:function(e){return r.get("QueryEmployeeArticle",e)},addEmployeeArticle:function(e){return r.post("AddEmployeeArticle",e)},updateEmployeeArticle:function(e){return r.post("UpdateEmployeeArticle",e)},deleteEmployeeArticle:function(e){return r.post("DeleteEmployeeArticle",e)},queryClassLevel:function(){return r.get("QueryClassLevel")},queryEmployeeClass:function(e){return r.get("QueryEmployeeClass",e)},addEmployeeClass:function(e){return r.post("AddEmployeeClass",e)},updateEmployeeClass:function(e){return r.post("UpdateEmployeeClass",e)},deleteEmployeeClass:function(e){return r.post("DeleteEmployeeClass",e)},queryEmployeePatent:function(e){return r.get("QueryEmployeePatent",e)},addEmployeePatent:function(e){return r.post("AddEmployeePatent",e)},updateEmployeePatent:function(e){return r.post("UpdateEmployeePatent",e)},deleteEmployeePatent:function(e){return r.post("DeleteEmployeePatent",e)},queryTeacherType:function(){return r.get("QueryTeacherType")},queryEmployeeTeacher:function(e){return r.get("QueryEmployeeTeacher",e)},addEmployeeTeacher:function(e){return r.post("AddEmployeeTeacher",e)},updateEmployeeTeacher:function(e){return r.post("UpdateEmployeeTeacher",e)},deleteEmployeeTeacher:function(e){return r.post("DeleteEmployeeTeacher",e)},queryAwardLevel:function(){return r.get("QueryAwardLevel")},queryDictByParentCode:function(e){return r.get("QueryDictByParentCode",e)},queryHighTalent:function(){return r.get("QueryHighTalent")},queryEmployeeAward:function(e){return r.get("QueryEmployeeAward",e)},addEmployeeAward:function(e){return r.post("AddEmployeeAward",e)},updateEmployeeAward:function(e){return r.post("UpdateEmployeeAward",e)},deleteEmployeeAward:function(e){return r.post("DeleteEmployeeAward",e)},addEmployeeHighTalent:function(e){return r.post("AddEmployeeHighTalent",e)},updateEmployeeHighTalent:function(e){return r.post("UpdateEmployeeHighTalent",e)},queryEmployeeHighTalent:function(e){return r.get("QueryEmployeeHighTalent",e)},deleteEmployeeHighTalent:function(e){return r.post("DeleteEmployeeHighTalent",e)},queryEmployeeDeptHistory:function(e){return r.get("QueryEmployeeDeptHistory",e)},updateEmployeeDept:function(e){return r.post("UpdateEmployeeDept",e)},batchUpdateEmployeeDept:function(e){return r.post("BatchUpdateEmployeeDept",e)},queryEmployeeList:function(e){return r.post("QueryEmployeeList",e)},queryEmployeeListTree:function(e){return r.get("QueryEmployeeListTree",e)},queryDictsSetting:function(e){return r.get("QueryDictsSetting",e)},queryEmployeeListSettingTree:function(e){return r.get("QueryEmployeeListSettingTree",e)},queryAdvancedQueryType:function(){return r.get("QueryAdvancedQueryType")},saveEmployeeList:function(e){return r.post("SaveEmployeeList",e)},deleteEmployeeList:function(e){return r.post("DeleteEmployeeList",e)},getEmployeeList:function(e){return r.get("GetEmployeeList",e)},getEmployeeListExcel:function(e){return r.getFile("GetEmployeeListExcel",e)},queryDeptPrincipal:function(e){return r.get("QueryDeptPrincipal",e)},queryOtherEmployeeInfo:function(e){return r.post("QueryOtherEmployeeInfo",e)},saveOtherEmployeeInfo:function(e){return r.post("SaveOtherEmployeeInfo",e)},deleteOtherEmployeeInfo:function(e){return r.post("DeleteOtherEmployeeInfo",e)},queryOtherEmpTypes:function(e){return r.get("QueryOtherEmpTypes",e)},importExcel:function(e,t){var a=new FormData;return t&&Object.keys(t).forEach((function(e){return a.append(e,t[e])})),a.append("file",e),r.postForm("ImportExcel",a)},downlodaImportExcelTemplate:function(e){return r.getFile("DownlodaImportExcelTemplate",e)},getSocialSecurityInfo:function(e){return r.get("getSocialSecurityInfo",e)},updateEmployeeSocialSecurity:function(e){return r.post("updateEmployeeSocialSecurity",e)},queryPayRollOrgClass:function(){return r.get("QueryPayRollOrgClass")},queryPayRollCompGroup:function(){return r.get("QueryPayRollCompGroup")},queryPayRollOrgSalary:function(e){return r.get("QueryPayRollOrgSalary",e)},queryPayRollOrgSalaryLevel:function(e){return r.get("QueryPayRollOrgSalaryLevel",e)},queryPayRollOrgPositionSalarys:function(e){return r.get("QueryPayRollOrgPositionSalarys",e)},queryWorkState:function(){return r.get("QueryWorkState")},getWagesInfo:function(e){return r.get("GetWagesInfo",e)},updateEmployeeBenefit:function(e){return r.post("UpdateEmployeeBenefit",e)},queryWagesHistory:function(e){return r.get("QueryWagesHistory",e)},getEmployeePayrollPrint:function(e){return r.get("GetEmployeePayrollPrint",e)},updateEmployeePayrollPrint:function(e){return r.post("UpdateEmployeePayrollPrint",e)},deleteEmployeePayrollPrint:function(e){return r.post("DeleteEmployeePayrollPrint",e)},getPrintDetailsInfo:function(e){return r.get("GetPrintDetailsInfo",e)},getEmployeeWage:function(e){return r.get("GetEmployeeWage",e)},editEmployeeWage:function(e){return r.post("EditEmployeeWage",e)},editEmployeeParty:function(e){return r.post("EditEmployeeParty",e)},queryEmployeeHRAffiliatedBranch:function(e){return r.get("QueryEmployeeHRAffiliatedBranch",e)},getEmployeeHRAffiliatedBranch:function(e){return r.get("GetEmployeeHRAffiliatedBranch",e)},addEmployeeHRAffiliatedBranch:function(e){return r.post("AddEmployeeHRAffiliatedBranch",e)},updateEmployeeHRAffiliatedBranch:function(e){return r.post("UpdateEmployeeHRAffiliatedBranch",e)},deleteEmployeeHRAffiliatedBranch:function(e){return r.post("DeleteEmployeeHRAffiliatedBranch",e)},queryEmployeeHRDict:function(e){return r.get("QueryEmployeeHRDict",e)},getEmployeeHRDict:function(e){return r.get("GetEmployeeHRDict",e)},addEmployeeHRDict:function(e){return r.post("AddEmployeeHRDict",e)},updateEmployeeHRDict:function(e){return r.post("UpdateEmployeeHRDict",e)},deleteEmployeeHRDict:function(e){return r.post("DeleteEmployeeHRDict",e)},queryEmployeeHRPartyMemberHonor:function(e){return r.get("QueryEmployeeHRPartyMemberHonor",e)},getEmployeeHRPartyMemberHonor:function(e){return r.get("GetEmployeeHRPartyMemberHonor",e)},addEmployeeHRPartyMemberHonor:function(e){return r.post("AddEmployeeHRPartyMemberHonor",e)},updateEmployeeHRPartyMemberHonor:function(e){return r.post("UpdateEmployeeHRPartyMemberHonor",e)},deleteEmployeeHRPartyMemberHonor:function(e){return r.post("DeleteEmployeeHRPartyMemberHonor",e)}}},f9ac:function(e,t,a){"use strict";var o=a("cfe3"),n="SysManage",r=new o["a"](n);t["a"]={queryDict:function(e){return r.get("QueryDict",e)},queryDictType:function(e){return r.post("QueryDictType",e)},addDict:function(e){return r.post("AddDict",e)},deleteDict:function(e){return r.post("DeleteDict",e)},updateDict:function(e){return r.post("UpdateDict",e)},getDict:function(e){return r.get("GetDict",e)},querySysSetting:function(e){return r.get("QuerySysSetting",e)},addSysSetting:function(e){return r.post("AddSysSetting",e)},deleteSysSetting:function(e){return r.post("DeleteSysSetting",e)},updateSysSetting:function(e){return r.post("UpdateSysSetting",e)},getSysSetting:function(e){return r.get("GetSysSetting",e)},queryLanguage:function(e){return r.get("QueryLanguage",e)},getEnumInfos:function(e){return r.get("GetEnumInfos",e)},queryUserGroups:function(e){return r.post("QueryUserGroups",e)},saveUserGroup:function(e){return r.post("SaveUserGroup",e)},deleteUserGroup:function(e){return r.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return r.get("DropdownUserGroups",e)},queryUsers:function(e){return r.post("QueryUsers",e)},saveUser:function(e){return r.post("SaveUser",e)},deleteUser:function(e){return r.post("DeleteUser",e)},initPwd:function(e){return r.post("InitPwd",e)},getUserById:function(e){return r.get("GetUserById",e)},queryEmployees:function(e){return r.post("QueryEmployees",e)},queryModuleInfos:function(e){return r.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return r.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return r.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return r.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return r.post("SaveRightOfDept",e)},queryControlRight:function(e){return r.post("QueryControlRight",e)},saveControlRights:function(e){return r.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return r.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return r.get("QueryStationTree",e)},queryStationTypeSelector:function(){return r.get("QueryStationTypeSelector")},queryStationSelector:function(e){return r.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return r.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return r.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return r.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return r.get("QueryStationAllowance",e)}}},fd3f:function(e,t,a){}}]);