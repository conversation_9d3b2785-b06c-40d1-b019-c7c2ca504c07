﻿namespace Renji.JHR.Api.Models
{
    public partial class PostdoctoralSalaryCorrectionYearModel
    {
        [MapFromProperty(typeof(PostdoctoralSalaryCorrectionYear), PostdoctoralSalaryCorrectionYear.Foreigns.Employee)]
        public EmployeeModel? Employee { get; set; }

        /// <summary>
        /// 薪资类型
        /// </summary>
        [MapFromProperty(typeof(PostdoctoralSalaryCorrectionYear), PostdoctoralSalaryCorrectionYear.Foreigns.Employee,Entities.Employee.Inverses.EmployeeSalary, EmployeeSalary.Columns.EnumSalaryType)]
        public int? EnumSalaryType { get; set; }

        // 保险比例
        public decimal? PensionInsurancePercentage { get; set; }
        public decimal? MedicalInsurancePercentage { get; set; }
        public decimal? UnemploymentInsurancePercentage { get; set; }
        public decimal? HousingFundPercentage { get; set; }
        public decimal? SupplementaryHousingFundPercentage { get; set; }
        public decimal? PostdoctoralMembershipFeePercentage { get; set; }

        /// <summary>
        /// 月份范围
        /// </summary>
        public List<DateTime>? MonthRange
        {
            get
            {
                if (StartTime.HasValue && EndTime.HasValue && StartTime != DateTime.MinValue && EndTime != DateTime.MinValue)
                {
                    return new List<DateTime> { StartTime.Value, EndTime.Value };
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 原会费金额
        /// </summary>
        public decimal? OldMembershipFee { get; set; }

        /// <summary>
        /// 新会费金额
        /// </summary>
        public decimal? NewMembershipFee { get; set; }

        /// <summary>
        /// 计算会费金额
        /// </summary>
        public decimal? CalculatedMembershipFee { get; set; }

        /// <summary>
        /// 实际会费金额
        /// </summary>
        public decimal? ActualMembershipFee { get; set; }

    }
}
