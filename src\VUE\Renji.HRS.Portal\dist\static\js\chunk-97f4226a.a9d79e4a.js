(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-97f4226a"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"841c":function(e,t,n){"use strict";var r=n("d784"),o=n("825a"),i=n("1d80"),a=n("129f"),s=n("14c3");r("search",1,(function(e,t,n){return[function(t){var n=i(this),r=void 0==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var i=o(e),l=String(this),u=i.lastIndex;a(u,0)||(i.lastIndex=0);var d=s(i,l);return a(i.lastIndex,u)||(i.lastIndex=u),null===d?-1:d.index}]}))},b113:function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{visible:e.showEmp,width:"80%",top:"5vh"},on:{"update:visible":function(t){e.showEmp=t}}},[n("div",[n("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[n("c-tree",{attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[n("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[n("el-form-item",{attrs:{label:"员工编号",prop:"empCode"}},[n("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),n("el-form-item",{attrs:{label:"中文名",prop:"displayName"}},[n("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),n("el-form-item",{attrs:{label:"性别",prop:"sex"}},[n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.listQuery.sex,callback:function(t){e.$set(e.listQuery,"sex",t)},expression:"listQuery.sex"}},e._l(e.genderDropdown,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.search()}}},[e._v("查询")])],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"员工编号",sortable:"custom",prop:"empCode"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empCode))])]}}])}),n("el-table-column",{attrs:{label:"中文名",sortable:"custom",prop:"displayName"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.displayName))])]}}])}),n("el-table-column",{attrs:{label:"院区"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.hospitalAreaNameText))])]}}])}),n("el-table-column",{attrs:{label:"部门",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.deptName))])]}}])}),n("el-table-column",{attrs:{fixed:"right",label:"选择",align:"left","header-align":"center",width:"320","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.selectRow(r)}}},[e._v(" 选择 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])})],1)])},o=[],i=(n("d3b7"),n("d368")),a=n("f9ac"),s={components:{},data:function(){return{showEmp:!1,listLoading:!1,treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:["1"],pageList:[],listQuery:{total:1,pageIndex:1,pageSize:10},genderDropdown:[{value:1,label:"男"},{value:2,label:"女"}]}},created:function(){this.loadTree()},methods:{treeNodeClick:function(e){this.listQuery.deptId=e.id,this.getPageList()},selectRow:function(e){this.showEmp=!1,this.$emit("selectRow",e)},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.listQuery.pageIndex=1,this.getPageList()},loadTree:function(){var e=this;i["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.listQuery.deptId=null,this.getPageList()},getPageList:function(){this.queryEmployeesMethods(this.listQuery)},queryEmployeesMethods:function(e){var t=this;this.listLoading=!0,a["a"].queryEmployees(e).then((function(e){e.succeed?(t.pageList=e.data.datas,t.listQuery.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e)})).finally((function(){t.listLoading=!1}))}}},l=s,u=n("2877"),d=Object(u["a"])(l,r,o,!1,null,"407e330e",null);t["a"]=d.exports},d267:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[n("el-form-item",[n("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),n("el-form-item",[n("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),n("el-form-item",[n("el-input",{attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.search()}}},[e._v("查询")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addDialog}},[e._v("添加")])],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"登录名",sortable:"custom",prop:"username"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.username))])]}}])}),n("el-table-column",{attrs:{label:"员工姓名",sortable:"custom",prop:"displayName"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.displayName))])]}}])}),n("el-table-column",{attrs:{label:"用户组名称",sortable:"custom",prop:"groupName"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.groupName))])]}}])}),n("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"320","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.initPwd(r)}}},[e._v(" 初始化密码 ")]),n("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.updateDialog(r)}}},[e._v(" 编辑 ")]),n("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.deleteRecord(r)}}},[e._v(" 删除 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),n("el-dialog",{attrs:{title:"用户管理",visible:e.addDialogVisible,width:"50%"},on:{"update:visible":function(t){e.addDialogVisible=t},close:e.closeAddDialog}},[n("el-form",{ref:"ref_addForm",attrs:{rules:e.rules,model:e.addForm,"label-width":"120px"}},[n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"登录名",prop:"username"}},[n("el-input",{attrs:{placeholder:"",disabled:e.isedit},model:{value:e.addForm.username,callback:function(t){e.$set(e.addForm,"username",t)},expression:"addForm.username"}})],1)],1)],1),e.isedit?e._e():n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"密码",prop:"pwdText"}},[n("el-input",{attrs:{placeholder:"","show-password":""},model:{value:e.addForm.pwdText,callback:function(t){e.$set(e.addForm,"pwdText",t)},expression:"addForm.pwdText"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:20}},[n("el-form-item",{attrs:{label:"员工",prop:"displayName"}},[n("el-input",{attrs:{placeholder:"",readonly:""},model:{value:e.addForm.displayName,callback:function(t){e.$set(e.addForm,"displayName",t)},expression:"addForm.displayName"}})],1)],1),n("el-col",{attrs:{span:3,offset:1}},[n("el-button",{on:{click:e.selectEmployeeDialog}},[e._v("选 择")])],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"用户组",prop:"usergroupids"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:""},model:{value:e.addForm.usergroupids,callback:function(t){e.$set(e.addForm,"usergroupids",t)},expression:"addForm.usergroupids"}},e._l(e.usergroupoptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.addDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitAddForm}},[e._v("保 存")])],1)],1),n("selectusercomponent",{ref:"selectempc",on:{selectRow:e.setEmp}})],1)},o=[],i=(n("d81d"),n("d3b7"),n("ac1f"),n("841c"),n("f9ac")),a=n("b113"),s=n("b27e"),l={components:{selectusercomponent:a["a"]},data:function(){return{isedit:!1,addForm:{usergroupids:[],username:"",pwdText:"",displayName:"",empCode:"",uid:""},rules:{username:[{required:!0,message:"[登录名] 是必填项！",trigger:"blur"},{required:!0,max:50,message:"[登录名] 超长！",trigger:"blur"}],pwdText:[{required:!0,message:"[密码] 是必填项！",trigger:"blur"},{required:!0,max:50,message:"[密码] 超长！",trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/,message:"[密码] 必须是大写字母，小写字母，数字，字符组成，且长度不少于八位！"}],displayName:[{required:!0,message:"[用户] 是必填项！",trigger:"blur"}]},addDialogVisible:!1,pageList:[],listQuery:{queryCondition:{},total:1,pageIndex:1,pageSize:10},listLoading:!1,dataColumns:[{value:"1",label:"登录名",type:"System.String",columnName:"Username"},{value:"2",label:"员工姓名",type:"System.String",columnName:"DisplayName"},{value:"3",label:"用户组名称",type:"System.String",columnName:"GroupName"}],selectConditionOptions:[],usergroupoptions:[]}},created:function(){this.loadUserGroupsMothod(),this.loadConditions(),this.getPageList()},methods:{selectEmployeeDialog:function(){this.$refs.selectempc.showEmp=!0},loadUserGroupsMothod:function(){var e=this;i["a"].dropdownUserGroups().then((function(t){e.usergroupoptions=t.data})).catch((function(e){return console.log(e)}))},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.search()},search:function(){this.listQuery.pageIndex=1,this.getPageList()},loadConditions:function(){var e=this;i["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getPageList:function(){var e=this;this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),i["a"].queryUsers(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},addDialog:function(){this.addDialogVisible=!0,this.isedit=!1},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()},updateDialog:function(e){var t=this;this.addDialogVisible=!0,this.isedit=!0,i["a"].getUserById({id:e.id}).then((function(e){t.addForm.id=e.data.id,t.addForm.username=e.data.username,t.addForm.displayName=e.data.displayName,t.addForm.empCode=e.data.empCode,t.addForm.uid=e.data.uid,e.data.roleMembers&&(t.addForm.usergroupids=e.data.roleMembers.map((function(e){return e.roleId})))})).catch((function(e){return console.log(e)}))},submitAddForm:function(){var e=this;this.addForm.usergroupids&&(this.addForm.roleMembers=this.addForm.usergroupids.map((function(e){return{roleId:e}}))),this.$refs["ref_addForm"].validate((function(t){if(t){var n={id:e.addForm.id,username:Object(s["a"])(e.addForm.username),newPwdText:Object(s["a"])(e.addForm.pwdText),displayName:e.addForm.displayName,empCode:e.addForm.empCode,uid:e.addForm.uid,usergroupids:e.addForm.usergroupids,roleMembers:e.addForm.roleMembers};i["a"].saveUser(n).then((function(t){t.succeed?(e.addDialogVisible=!1,e.search(),e.$notice.message("保存成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})),e.addForm.id=""}}))},deleteRecord:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i["a"].deleteUser({id:e.id}).then((function(e){e.succeed?(t.search(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){e.succeed||t.$notice.message("取消删除","info")}))},initPwd:function(e){var t=this;this.$confirm("确定要初始化密码?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i["a"].initPwd({id:e.id}).then((function(e){e.succeed?t.$alert("密码:"+e.data,"提示"):e.succeed||t.$notice.message("初始化密码失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){e.succeed||t.$notice.message("取消初始化密码","info")}))},setEmp:function(e){this.addForm.displayName=e.displayName,this.addForm.empCode=e.empCode,this.addForm.uid=e.uid}}},u=l,d=n("2877"),c=Object(d["a"])(u,r,o,!1,null,"8ebc0cc8",null);t["default"]=c.exports},d368:function(e,t,n){"use strict";var r=n("cfe3"),o="Organization",i=new r["a"](o);t["a"]={QueryOrganizationHiddenTop:function(e){return i.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return i.get("QueryOrganization",e)},QueryDepartment:function(e){return i.get("QueryDepartment",e)},GetDepartment:function(e){return i.get("GetDepartment",e)},AddDepartment:function(e){return i.post("AddDepartment",e)},UpdateDepartment:function(e){return i.post("UpdateDepartment",e)},MoveDepartment:function(e){return i.post("MoveDepartment",e)},MergeDepartment:function(e){return i.post("MergeDepartment",e)},DeleteDepartment:function(e){return i.post("DeleteDepartment",e)},queryPosition:function(e){return i.post("QueryPosition",e)},getPosition:function(e){return i.get("GetPosition",e)},addPosition:function(e){return i.post("AddPosition",e)},updatePosition:function(e){return i.post("UpdatePosition",e)},deletePosition:function(e){return i.post("DeletePosition",e)},GetStation:function(e){return i.get("GetStation",e)},AddStation:function(e){return i.post("AddStation",e)},UpdateStation:function(e){return i.post("UpdateStation",e)},DeleteStation:function(e){return i.post("DeleteStation",e)},QueryPositionStationTree:function(e){return i.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return i.post("AllocatePosition",e)},DeletePositionStation:function(e){return i.post("DeletePositionStation",e)},queryDeptByUser:function(e){return i.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return i.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return i.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return i.get("QuerySenioritySelect")},queryStationAllowance:function(e){return i.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return i.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),i.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return i.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return i.get("GetStationAllowance",e)},addStationAllowance:function(e){return i.post("AddStationAllowance",e)},updateStationAllowance:function(e){return i.post("UpdateStationAllowance",e)},querySeniority:function(e){return i.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),i.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return i.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return i.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return i.get("GetSeniority",e)},addSeniority:function(e){return i.post("AddSeniority",e)},updateSeniority:function(e){return i.post("UpdateSeniority",e)},querySalaryScale:function(e){return i.get("QuerySalaryScale",e)},getSalaryScale:function(e){return i.get("GetSalaryScale",e)},addSalaryScale:function(e){return i.post("AddSalaryScale",e)},updateSalaryScale:function(e){return i.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return i.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),i.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return i.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return i.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return i.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return i.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return i.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return i.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return i.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return i.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return i.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return i.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return i.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return i.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return i.post("DeleteTelephoneFee",e)}}},f9ac:function(e,t,n){"use strict";var r=n("cfe3"),o="SysManage",i=new r["a"](o);t["a"]={queryDict:function(e){return i.get("QueryDict",e)},queryDictType:function(e){return i.post("QueryDictType",e)},addDict:function(e){return i.post("AddDict",e)},deleteDict:function(e){return i.post("DeleteDict",e)},updateDict:function(e){return i.post("UpdateDict",e)},getDict:function(e){return i.get("GetDict",e)},querySysSetting:function(e){return i.get("QuerySysSetting",e)},addSysSetting:function(e){return i.post("AddSysSetting",e)},deleteSysSetting:function(e){return i.post("DeleteSysSetting",e)},updateSysSetting:function(e){return i.post("UpdateSysSetting",e)},getSysSetting:function(e){return i.get("GetSysSetting",e)},queryLanguage:function(e){return i.get("QueryLanguage",e)},getEnumInfos:function(e){return i.get("GetEnumInfos",e)},queryUserGroups:function(e){return i.post("QueryUserGroups",e)},saveUserGroup:function(e){return i.post("SaveUserGroup",e)},deleteUserGroup:function(e){return i.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return i.get("DropdownUserGroups",e)},queryUsers:function(e){return i.post("QueryUsers",e)},saveUser:function(e){return i.post("SaveUser",e)},deleteUser:function(e){return i.post("DeleteUser",e)},initPwd:function(e){return i.post("InitPwd",e)},getUserById:function(e){return i.get("GetUserById",e)},queryEmployees:function(e){return i.post("QueryEmployees",e)},queryModuleInfos:function(e){return i.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return i.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return i.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return i.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return i.post("SaveRightOfDept",e)},queryControlRight:function(e){return i.post("QueryControlRight",e)},saveControlRights:function(e){return i.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return i.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return i.get("QueryStationTree",e)},queryStationTypeSelector:function(){return i.get("QueryStationTypeSelector")},queryStationSelector:function(e){return i.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return i.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return i.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return i.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return i.get("QueryStationAllowance",e)}}}}]);