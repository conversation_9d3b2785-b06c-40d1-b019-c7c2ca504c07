(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67c77f90"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"4a63":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.headModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{staticClass:"input_class",attrs:{type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd",size:"small",clearable:!1},model:{value:e.headModel.recordDate,callback:function(t){e.$set(e.headModel,"recordDate",t)},expression:"headModel.recordDate"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"部门"}},[n("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},model:{value:e.headModel.dept,callback:function(t){e.$set(e.headModel,"dept",t)},expression:"headModel.dept"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工号"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empCode,callback:function(t){e.$set(e.headModel,"empCode",t)},expression:"headModel.empCode"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"姓名"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.headModel.empName,callback:function(t){e.$set(e.headModel,"empName",t)},expression:"headModel.empName"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:""}},[n("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"仅显示加班数据"}},[n("el-checkbox",{model:{value:e.headModel.onlyOTValue,callback:function(t){e.$set(e.headModel,"onlyOTValue",t)},expression:"headModel.onlyOTValue"}})],1),n("el-form-item",{attrs:{label:"按月份查询"}},[n("el-checkbox",{model:{value:e.headModel.withMonth,callback:function(t){e.$set(e.headModel,"withMonth",t)},expression:"headModel.withMonth"}})],1)],1)],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[n("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empUid))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"120",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empName))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"部门",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empDept))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"加班类型",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.edit?n("el-select",{attrs:{placeholder:"请选择"},model:{value:r.enumOverTimeType,callback:function(t){e.$set(r,"enumOverTimeType",t)},expression:"row.enumOverTimeType"}},e._l(e.overTimeType,(function(e){return n("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1):n("span",[e._v(e._s(r.enumOverTimeTypeDesc))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"日期",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.recordDateStr))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.edit?n("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(t){return e.confirmEdit(r)}}},[e._v(" 更新 ")]):e._e(),r.edit?n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.cancelEdit(r)}}},[e._v(" 取消 ")]):e._e(),r.edit?e._e():n("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.Edit(r)}}},[e._v(" 编辑 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[20,50,100],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.paginationChanged}})]},proxy:!0}])})],1)},o=[],a=(n("99af"),n("d81d"),n("d3b7"),n("ac1f"),n("25f0"),n("4d90"),n("841c"),n("d368")),i=n("cbd2"),u=n("f9ac"),c={components:{},data:function(){return{headModel:{onlyOTValue:!0},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,treeExpandedKeys:[],currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],editOringinData:{},overTimeType:[]}},created:function(){this.$set(this.headModel,"recordDate",this.getNowTime()),this.search(),this.loadTree(),this.loadOverTimeType()},methods:{getNowTime:function(){var e=new Date,t=e.getFullYear(),n=e.getMonth(),r=e.getDate();n+=1,n=n.toString().padStart(2,"0");var o="".concat(t,"-").concat(n,"-").concat(r," 00:00:00");return o},loadTree:function(){var e=this;a["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)})),this.resertCurrentNode()},loadOverTimeType:function(){var e=this;u["a"].getEnumInfos({enumType:"OverTimeType"}).then((function(t){e.overTimeType=t.data.datas})).catch((function(e){console.log(e)}))},resertCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){},Edit:function(e){e.edit=!e.edit},cancelEdit:function(e){e.edit=!1,e.enumOverTimeType=e.originalEnumOverTimeType,e.enumOverTimeTypeDesc=e.originalEnumOverTimeTypeDesc},confirmEdit:function(e){var t=this;e.edit=!1,e.oTDate=this.headModel.recordDate,i["a"].updateAttHolidayOTRecordDetail(e).then((function(n){if(n.succeed){var r=n.data;e.enumOverTimeTypeDesc=r.enumOverTimeTypeDesc,e.updator=r.updator,e.originalEnumOverTimeType=r.enumOverTimeType,e.originalEnumOverTimeTypeDesc=r.enumOverTimeTypeDesc}else t.$notice.resultTip(n)})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.getSearchResult()},getSearchResult:function(){var e=this,t={RecordDate:this.headModel.recordDate,DeptId:this.headModel.dept,EmpCode:this.headModel.empCode,EmpName:this.headModel.empName,OnlyOTValue:this.headModel.onlyOTValue,PageIndex:this.listQuery.pageIndex,PageSize:this.listQuery.pageSize,WithMonth:this.headModel.withMonth};i["a"].searchAttHolidayOTRecordDetail_Update(t).then((function(t){e.listLoading=!1,t.succeed?(e.total=t.data.recordCount,e.tableData=t.data.datas.map((function(t){return e.$set(t,"edit",!1),t.originalEnumOverTimeType=t.enumOverTimeType,t.originalEnumOverTimeTypeDesc=t.enumOverTimeTypeDesc,t}))):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getSearchResult()}}},l=c,d=n("2877"),s=Object(d["a"])(l,r,o,!1,null,null,null);t["default"]=s.exports},"841c":function(e,t,n){"use strict";var r=n("d784"),o=n("825a"),a=n("1d80"),i=n("129f"),u=n("14c3");r("search",1,(function(e,t,n){return[function(t){var n=a(this),r=void 0==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var a=o(e),c=String(this),l=a.lastIndex;i(l,0)||(a.lastIndex=0);var d=u(a,c);return i(a.lastIndex,l)||(a.lastIndex=l),null===d?-1:d.index}]}))},cbd2:function(e,t,n){"use strict";var r=n("cfe3"),o="AttendanceManage",a=new r["a"](o);t["a"]={getAttMonthShiftRecord:function(e){return a.get("GetAttMonthShiftRecord",e)},queryAttMonthShiftRecordDetail:function(e){return a.get("QueryAttMonthShiftRecordDetail",e)},batchConfirmAttMonthShiftRecord:function(e){return a.post("BatchConfirmAttMonthShiftRecord",e)},saveAttMonthShiftRecord:function(e){return a.post("SaveAttMonthShiftRecord",e)},submitAttMonthShiftRecord:function(e){return a.post("SubmitAttMonthShiftRecord",e)},ConfirmAttMonthShiftRecord:function(e){return a.post("ConfirmAttMonthShiftRecord",e)},rejectAttMonthShiftRecord:function(e){return a.post("RejectAttMonthShiftRecord",e)},searchAttMonthShiftRecordDetail:function(e){return a.get("SearchAttMonthShiftRecordDetail",e)},searchAttMonthShiftRecordDetail_Update:function(e){return a.get("SearchAttMonthShiftRecordDetail_Update",e)},updateAttMonthShiftRecordDetail:function(e){return a.post("UpdateAttMonthShiftRecordDetail",e)},getColorDeptTree_MiddleNightShift:function(e){return a.get("GetColorDeptTree_MiddleNightShift",e)},get_MiddleNightShiftReportExcel:function(e){return a.getFile("Get_MiddleNightShiftReportExcel",e)},getAttHolidayOTRecord:function(e){return a.get("GetAttHolidayOTRecord",e)},queryAttHolidayOTRecordDetail:function(e){return a.get("QueryAttHolidayOTRecordDetail",e)},saveAttHolidayOTRecord:function(e){return a.post("SaveAttHolidayOTRecord",e)},batchConfirmAttHolidayOTRecord:function(e){return a.post("BatchConfirmAttHolidayOTRecord",e)},submitAttHolidayOTRecord:function(e){return a.post("SubmitAttHolidayOTRecord",e)},ConfirmAttHolidayOTRecord:function(e){return a.post("ConfirmAttHolidayOTRecord",e)},rejectAttHolidayOTRecord:function(e){return a.post("RejectAttHolidayOTRecord",e)},searchAttHolidayOTRecordDetail:function(e){return a.get("SearchAttHolidayOT",e)},searchAttHolidayOTRecordDetail_Update:function(e){return a.get("SearchAttHolidayOTRecordDetail_Update",e)},updateAttHolidayOTRecordDetail:function(e){return a.post("UpdateAttHolidayOTRecordDetail",e)},getColorDeptTree_HolidayOT:function(e){return a.get("GetColorDeptTree_HolidayOT",e)},getOTReportExcel:function(e){return a.getFile("GetOTReportExcel",e)},getAttDayOffRecord:function(e){return a.get("GetAttDayOffRecord",e)},queryAttDayOffRecordDetail:function(e){return a.get("QueryAttDayOffRecordDetail",e)},saveAttDayOffRecord:function(e){return a.post("SaveAttDayOffRecord",e)},submitAttDayOffRecord:function(e){return a.post("SubmitAttDayOffRecord",e)},updateApproveAttDayOffRecord:function(e){return a.post("UpdateApproveAttDayOffRecord",e)},rejectAttDayOffRecord:function(e){return a.post("RejectAttDayOffRecord",e)},searchAttDayOffRecordDetail:function(e){return a.get("SearchAttDayOffRecordDetail",e)},searchAttDayOffRecordDetail_Update:function(e){return a.get("SearchAttDayOffRecordDetail_Update",e)},updateAttDayOffRecordDetail:function(e){return a.post("UpdateAttDayOffRecordDetail",e)},getColorDeptTree_DayOff:function(e){return a.get("GetColorDeptTree_DayOff",e)},getDayOffReportExcel:function(e){return a.getFile("GetDayOffReportExcel",e)},searchAttDayOffRecordDetail1:function(e){return a.get("SearchAttDayOffRecordDetail1",e)},searchAttMonthWatchRecord:function(e){return a.get("SearchAttMonthWatchRecord",e)},updateAttMonthWatchRecord:function(e){return a.post("UpdateAttMonthWatchRecord",e)},getMonthWatchTReportExcel:function(e){return a.getFile("GetMonthWatchTReportExcel",e)},getAttDayOffRecordDetail1Excel:function(e){return a.getFile("GetAttDayOffRecordDetail1Excel",e)},queryEmployeeList:function(e){return a.get("QueryEmployeeList",e)},searchAttDayOffRecordDetail2:function(e){return a.get("SearchAttDayOffRecordDetail2",e)},getAttDayOffRecordDetail2Excel:function(e){return a.getFile("GetAttDayOffRecordDetail2Excel",e)},searchAttDayOffRecordDetail3:function(e){return a.get("SearchAttDayOffRecordDetail3",e)},getAttDayOffRecordDetail3Excel:function(e){return a.getFile("GetAttDayOffRecordDetail3Excel",e)},queryAttDayOffRecordProphylacticDetail:function(e){return a.get("QueryAttDayOffRecordProphylacticDetail",e)},exportAttDayOffRecordProphylacticDetail:function(e){return a.getFile("ExportAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylacticCase:function(e){return a.get("GetAttDayOffRecordProphylacticCase",e)},getAttDayOffRecordProphylacticDetail:function(e){return a.get("GetAttDayOffRecordProphylacticDetail",e)},getAttDayOffRecordProphylactic:function(e){return a.get("GetAttDayOffRecordProphylactic",e)},addAttDayOffRecordProphylactic:function(e){return a.post("AddAttDayOffRecordProphylactic",e)},updateAttDayOffRecordProphylactic:function(e){return a.post("UpdateAttDayOffRecordProphylactic",e)},deleteAttDayOffRecordProphylacticDetail:function(e){return a.post("DeleteAttDayOffRecordProphylacticDetail",e)},subjectAttDayOffRecordProphylactic:function(e){return a.post("SubjectAttDayOffRecordProphylactic",e)},queryCheckRecordFilling:function(e){return a.get("QueryCheckRecordFilling",e)},queryPersonnelAttendanceData:function(e){return a.get("QueryPersonnelAttendanceData",e)},queryProphylacticChange:function(e){return a.get("QueryProphylacticChange",e)},exportProphylacticChange:function(e){return a.getFile("ExportProphylacticChange",e)},queryPersonnelPendingApproval:function(e){return a.get("QueryPersonnelPendingApproval",e)},approveAttDayOffRecord:function(e){return a.post("ApproveAttDayOffRecord",e)},getSameDeptEmployeeWithHealthAllowance:function(e){return a.get("GetSameDeptEmployeeWithHealthAllowance",e)}}},d368:function(e,t,n){"use strict";var r=n("cfe3"),o="Organization",a=new r["a"](o);t["a"]={QueryOrganizationHiddenTop:function(e){return a.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return a.get("QueryOrganization",e)},QueryDepartment:function(e){return a.get("QueryDepartment",e)},GetDepartment:function(e){return a.get("GetDepartment",e)},AddDepartment:function(e){return a.post("AddDepartment",e)},UpdateDepartment:function(e){return a.post("UpdateDepartment",e)},MoveDepartment:function(e){return a.post("MoveDepartment",e)},MergeDepartment:function(e){return a.post("MergeDepartment",e)},DeleteDepartment:function(e){return a.post("DeleteDepartment",e)},queryPosition:function(e){return a.post("QueryPosition",e)},getPosition:function(e){return a.get("GetPosition",e)},addPosition:function(e){return a.post("AddPosition",e)},updatePosition:function(e){return a.post("UpdatePosition",e)},deletePosition:function(e){return a.post("DeletePosition",e)},GetStation:function(e){return a.get("GetStation",e)},AddStation:function(e){return a.post("AddStation",e)},UpdateStation:function(e){return a.post("UpdateStation",e)},DeleteStation:function(e){return a.post("DeleteStation",e)},QueryPositionStationTree:function(e){return a.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return a.post("AllocatePosition",e)},DeletePositionStation:function(e){return a.post("DeletePositionStation",e)},queryDeptByUser:function(e){return a.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return a.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return a.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),a.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return a.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return a.get("GetStationAllowance",e)},addStationAllowance:function(e){return a.post("AddStationAllowance",e)},updateStationAllowance:function(e){return a.post("UpdateStationAllowance",e)},querySeniority:function(e){return a.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),a.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return a.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return a.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return a.get("GetSeniority",e)},addSeniority:function(e){return a.post("AddSeniority",e)},updateSeniority:function(e){return a.post("UpdateSeniority",e)},querySalaryScale:function(e){return a.get("QuerySalaryScale",e)},getSalaryScale:function(e){return a.get("GetSalaryScale",e)},addSalaryScale:function(e){return a.post("AddSalaryScale",e)},updateSalaryScale:function(e){return a.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return a.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),a.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return a.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return a.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return a.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return a.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return a.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return a.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return a.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return a.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return a.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return a.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return a.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return a.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return a.post("DeleteTelephoneFee",e)}}},f9ac:function(e,t,n){"use strict";var r=n("cfe3"),o="SysManage",a=new r["a"](o);t["a"]={queryDict:function(e){return a.get("QueryDict",e)},queryDictType:function(e){return a.post("QueryDictType",e)},addDict:function(e){return a.post("AddDict",e)},deleteDict:function(e){return a.post("DeleteDict",e)},updateDict:function(e){return a.post("UpdateDict",e)},getDict:function(e){return a.get("GetDict",e)},querySysSetting:function(e){return a.get("QuerySysSetting",e)},addSysSetting:function(e){return a.post("AddSysSetting",e)},deleteSysSetting:function(e){return a.post("DeleteSysSetting",e)},updateSysSetting:function(e){return a.post("UpdateSysSetting",e)},getSysSetting:function(e){return a.get("GetSysSetting",e)},queryLanguage:function(e){return a.get("QueryLanguage",e)},getEnumInfos:function(e){return a.get("GetEnumInfos",e)},queryUserGroups:function(e){return a.post("QueryUserGroups",e)},saveUserGroup:function(e){return a.post("SaveUserGroup",e)},deleteUserGroup:function(e){return a.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return a.get("DropdownUserGroups",e)},queryUsers:function(e){return a.post("QueryUsers",e)},saveUser:function(e){return a.post("SaveUser",e)},deleteUser:function(e){return a.post("DeleteUser",e)},initPwd:function(e){return a.post("InitPwd",e)},getUserById:function(e){return a.get("GetUserById",e)},queryEmployees:function(e){return a.post("QueryEmployees",e)},queryModuleInfos:function(e){return a.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return a.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return a.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return a.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return a.post("SaveRightOfDept",e)},queryControlRight:function(e){return a.post("QueryControlRight",e)},saveControlRights:function(e){return a.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return a.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return a.get("QueryStationTree",e)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(e){return a.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return a.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)}}}}]);