(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-246be99c"],{"19de":function(e,t){e.exports=function(e,t,n,o){var r="undefined"!==typeof o?[o,e]:[e],i=new Blob(r,{type:n||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(i,t);else{var a=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(i):window.webkitURL.createObjectURL(i),l=document.createElement("a");l.style.display="none",l.href=a,l.setAttribute("download",t),"undefined"===typeof l.download&&l.setAttribute("target","_blank"),document.body.appendChild(l),l.click(),setTimeout((function(){document.body.removeChild(l),window.URL.revokeObjectURL(a)}),200)}}},"28b9":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container "},[n("layout1",{scopedSlots:e._u([{key:"header",fn:function(){return[n("el-button",{attrs:{type:"primary",icon:"el-icon-setting"},on:{click:e.querySetting}},[e._v("查询设置")])]},proxy:!0},{key:"aside",fn:function(){return[n("el-checkbox",{on:{change:e.selectedChange},model:{value:e.listQuery.IsContainSubDept,callback:function(t){e.$set(e.listQuery,"IsContainSubDept",t)},expression:"listQuery.IsContainSubDept"}},[e._v("包含下级部门")]),n("c-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.treeLoading,expression:"treeLoading"}],attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[n("el-card",[n("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pageEmpInfoList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChangeForEmpInfo}},[n("el-table-column",{attrs:{fixed:"",label:"唯一码",sortable:"custom",prop:"Uid","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",{staticClass:"link-type",on:{click:function(t){return e.handleWatchEmpInfo(o)}}},[e._v(e._s(o.uid))])]}}])}),n("el-table-column",{attrs:{label:"工号","min-width":"80px",sortable:"custom",prop:"EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.empCode))])]}}])}),n("el-table-column",{attrs:{label:"姓名","min-width":"80px",sortable:"custom",prop:"DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.displayName))])]}}])}),n("el-table-column",{attrs:{label:"部门","min-width":"80px",sortable:"custom",prop:"DepartmentName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.departmentName))])]}}])}),n("el-table-column",{attrs:{label:"岗位","min-width":"80px",sortable:"custom",prop:"PositionName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.positionName))])]}}])}),n("el-table-column",{attrs:{label:"在职方式","min-width":"120px",sortable:"custom",prop:"HireStyleName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.hireStyleName))])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.queryEmployeeByConditions}})],1)]},proxy:!0}])}),e.dialogAppInfoVisible?n("el-dialog",{staticClass:"empManager",attrs:{title:e.empDialogTitle,visible:e.dialogAppInfoVisible,width:"90%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogAppInfoVisible=t}}},[n("hRInfo",{ref:"hRInfo",attrs:{"emp-model":e.empModel}})],1):e._e(),e.dialogQuerySettingVisible?n("el-dialog",{staticClass:"empManager",attrs:{title:"查询设置",visible:e.dialogQuerySettingVisible,width:"90%","before-close":e.hideQuerySettingDialog},on:{"update:visible":function(t){e.dialogQuerySettingVisible=t}}},[n("querySetting",{ref:"querySetting",on:{search:e.quickSearch,CloseDialog:e.hideQuerySettingDialog}})],1):e._e()],1)},r=[],i=(n("d3b7"),n("d368")),a=n("e44c"),l=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.empModel,"label-position":"right","label-width":"100px"}},[n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("人事信息")])]),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"姓名",prop:"DisplayName"}},[n("span",[e._v(e._s(e.empModel.displayName))])])],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"工号 ",prop:"EmpCode"}},[n("span",[e._v(e._s(e.empModel.empCode))])])],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"唯一码 ",prop:"Uid"}},[n("span",[e._v(e._s(e.empModel.uid))])])],1)],1),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"进院日期",prop:"hireDate"}},[n("span",[e._v(e._s(e.empModel.hireDateFormat))])])],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"身份证号码",prop:"IdentityNumber"}},[n("span",[e._v(e._s(e.empModel.identityNumber))])])],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"在职方式",prop:"hireStyleId"}},[n("span",[e._v(e._s(e.empModel.hireStyleName))])])],1)],1),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:16}},[n("el-form-item",{attrs:{label:"家庭地址",prop:"ResidentAddress"}},[n("span",[e._v(e._s(e.empModel.address))])])],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"家庭地址邮编 ",prop:"ResidentZip"}},[n("span",[e._v(e._s(e.empModel.zipCode))])])],1)],1)],1)],1),n("el-form",{ref:"financialForm",attrs:{rules:e.financialRules,model:e.financialInformation,"label-position":"right","label-width":"100px"}},[n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("财务信息")])]),n("el-row",{attrs:{type:"flex",justify:"end"}},[n("el-col",{attrs:{span:2}},[n("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1),n("el-col",{attrs:{span:2}},[n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate1}},[e._v("模板下载")])],1),n("el-col",{attrs:{span:2}},[n("el-upload",{attrs:{action:"","http-request":e.importExcel1,accept:".xlsx","show-file-list":!1}},[n("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1)],1),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"银行名称一",prop:"bankName1"}},[n("el-input",{model:{value:e.financialInformation.bankName1,callback:function(t){e.$set(e.financialInformation,"bankName1",t)},expression:"financialInformation.bankName1"}})],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"银行名称二",prop:"bankName2"}},[n("el-input",{model:{value:e.financialInformation.bankName2,callback:function(t){e.$set(e.financialInformation,"bankName2",t)},expression:"financialInformation.bankName2"}})],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"银行名称三",prop:"bankName3"}},[n("el-input",{model:{value:e.financialInformation.bankName3,callback:function(t){e.$set(e.financialInformation,"bankName3",t)},expression:"financialInformation.bankName3"}})],1)],1)],1),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"银行账号一",prop:"bankAccount1"}},[n("el-input",{model:{value:e.financialInformation.bankAccount1,callback:function(t){e.$set(e.financialInformation,"bankAccount1",t)},expression:"financialInformation.bankAccount1"}})],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"银行账号二",prop:"bankAccount2"}},[n("el-input",{model:{value:e.financialInformation.bankAccount2,callback:function(t){e.$set(e.financialInformation,"bankAccount2",t)},expression:"financialInformation.bankAccount2"}})],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"银行账号三",prop:"bankAccount3"}},[n("el-input",{model:{value:e.financialInformation.bankAccount3,callback:function(t){e.$set(e.financialInformation,"bankAccount3",t)},expression:"financialInformation.bankAccount3"}})],1)],1)],1),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"社保账号",prop:"annuityAccount"}},[n("el-input",{model:{value:e.financialInformation.annuityAccount,callback:function(t){e.$set(e.financialInformation,"annuityAccount",t)},expression:"financialInformation.annuityAccount"}})],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"公积金账号",prop:"accumFundAccount"}},[n("el-input",{model:{value:e.financialInformation.accumFundAccount,callback:function(t){e.$set(e.financialInformation,"accumFundAccount",t)},expression:"financialInformation.accumFundAccount"}})],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"工资单编号",prop:"paySlipNumber"}},[n("el-input",{model:{value:e.financialInformation.paySlipNumber,callback:function(t){e.$set(e.financialInformation,"paySlipNumber",t)},expression:"financialInformation.paySlipNumber"}})],1)],1)],1),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"免个人所得税",prop:"dispenseTax"}},[n("el-checkbox",{model:{value:e.financialInformation.dispenseTax,callback:function(t){e.$set(e.financialInformation,"dispenseTax",t)},expression:"financialInformation.dispenseTax"}})],1)],1)],1)],1)],1)],1)},u=[],s={name:"",components:{},props:{empModel:{type:Object,default:function(){console.log("propE default invoked.")}}},data:function(){return{rules:{},financialRules:{},financialInformation:{}}},created:function(){this.getFinancialInformation()},methods:{getFinancialInformation:function(){var e=this;a["a"].getSocialSecurityInfo({id:this.empModel.id}).then((function(t){t.data&&(e.financialInformation=t.data)}))},save:function(){var e=this;this.financialInformation.ID=this.empModel.id,a["a"].updateEmployeeSocialSecurity(this.financialInformation).then((function(t){t.succeed?e.$notice.message("保存成功","success"):-3!==t.type&&e.$notice.resultTip(t)}))},downloadexceltemplate1:function(){a["a"].downlodaImportExcelTemplate({type:"importempsoc"}).then((function(e){var t=n("19de"),o="EmployeeSocialInsuranceTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel1:function(e){var t=this,n=e.file;a["a"].importExcel(n,{type:"importempsoc"}).then((function(e){if(e.succeed){var n=e.data;t.$message.success(n)}})).catch((function(e){}))}}},c=s,p=(n("84de"),n("2877")),d=Object(p["a"])(c,l,u,!1,null,"8a11500c",null),y=d.exports,m=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.tabClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"查询设置",name:"querySetting"}},[n("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[n("el-row",{staticStyle:{"padding-top":"10px"},attrs:{gutter:10}},[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"逻辑关系",prop:"enumLogicRelationships"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{"value-key":"value",clearable:"",placeholder:"请选择逻辑关系"},on:{change:e.relationChange},model:{value:e.tempData.tempFormModel.enumLogicRelationships,callback:function(t){e.$set(e.tempData.tempFormModel,"enumLogicRelationships",t)},expression:"tempData.tempFormModel.enumLogicRelationships"}},e._l(e.logicRelationshipOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.desc,value:e}})})),1)],1)],1),n("el-col",{attrs:{span:2}}),n("el-col",{attrs:{span:10}},[n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.quickSearch}},[e._v("快捷查询")])],1)],1)],1),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"字段",prop:"selectedColumn"}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{"value-key":"id",clearable:"",placeholder:"请选择字段"},on:{change:e.columnChange},model:{value:e.tempData.tempFormModel.selectedColumn,callback:function(t){e.$set(e.tempData.tempFormModel,"selectedColumn",t)},expression:"tempData.tempFormModel.selectedColumn"}},e._l(e.empColumnOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.displayName,value:e}})})),1)],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"运算符",prop:"selectedCondition"}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{"value-key":"value",clearable:"",placeholder:"请选择运算符"},on:{change:e.conditionChange},model:{value:e.tempData.tempFormModel.selectedCondition,callback:function(t){e.$set(e.tempData.tempFormModel,"selectedCondition",t)},expression:"tempData.tempFormModel.selectedCondition"}},e._l(e.selectConditionOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.desc,value:e}})})),1)],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"关键字",prop:"keywords"}},[n("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.tempData.tempFormModel.keywords,callback:function(t){e.$set(e.tempData.tempFormModel,"keywords",t)},expression:"tempData.tempFormModel.keywords"}})],1)],1)],1),n("el-row",{attrs:{gutter:10}},[n("el-col",{attrs:{span:24}},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"dataListTable",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),n("el-table-column",{attrs:{label:"关系","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.displayRelationship))])]}}])}),n("el-table-column",{attrs:{label:"运算符","min-width":"300px"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[o.children?e._e():n("span",[e._v(e._s(o.displayName)+e._s(o.displayOperation)+e._s(o.keywords))]),o.children?n("span",[e._v(e._s(o.description))]):e._e()]}}])}),n("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteData(o)}}},[e._v(" 删除 ")])]}}])})],1)],1)],1),n("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[n("el-col",{attrs:{span:24,align:"center"}},[n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.addData(e.tempData.tempFormModel)}}},[e._v("添加")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-connection"},on:{click:e.showCombineData}},[e._v("组合")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:e.search}},[e._v("确定")]),n("el-button",{attrs:{type:"warning",icon:"el-icon-refresh-left"},on:{click:e.cancle}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-document"},on:{click:e.showSaveDialog}},[e._v("保存")])],1)],1)],1),e.dialogCombineVisible?n("el-dialog",{attrs:{title:"组合",visible:e.dialogCombineVisible,"append-to-body":""},on:{"update:visible":function(t){e.dialogCombineVisible=t}}},[n("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[n("el-col",{attrs:{span:24,align:"center"}},[n("el-radio",{attrs:{label:10},model:{value:e.relationshipForCombine,callback:function(t){e.relationshipForCombine=t},expression:"relationshipForCombine"}},[e._v("AND组合")]),n("el-radio",{attrs:{label:20},model:{value:e.relationshipForCombine,callback:function(t){e.relationshipForCombine=t},expression:"relationshipForCombine"}},[e._v("OR组合")])],1)],1),n("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[n("el-col",{attrs:{span:24,align:"center"}},[n("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:e.combineData}},[e._v("保存")]),n("el-button",{attrs:{type:"warning",icon:"el-icon-circle-close"},on:{click:function(t){return e.colseCombine()}}},[e._v("关闭")])],1)],1)],1):e._e(),e.dialogSaveVisible?n("el-dialog",{attrs:{title:"保存查询",visible:e.dialogSaveVisible,"append-to-body":""},on:{"update:visible":function(t){e.dialogSaveVisible=t}}},[n("el-form",{ref:"dataFormForSave",attrs:{rules:e.rulesForSave,model:e.tempData.tempFormModelForSave,"label-position":"right","label-width":"100px"}},[n("el-row",{staticStyle:{"padding-top":"10px"},attrs:{gutter:10}},[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"名称",prop:"name"}},[n("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入名称"},model:{value:e.tempData.tempFormModelForSave.name,callback:function(t){e.$set(e.tempData.tempFormModelForSave,"name",t)},expression:"tempData.tempFormModelForSave.name"}})],1)],1)],1),n("el-row",{staticStyle:{padding:"10px"},attrs:{gutter:10}},[n("el-col",{attrs:{span:24,align:"center"}},[n("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:e.save}},[e._v("保存")]),n("el-button",{attrs:{type:"warning",icon:"el-icon-circle-close"},on:{click:e.cancleSave}},[e._v("取消")])],1)],1)],1)],1):e._e()],1),n("el-tab-pane",{attrs:{label:"已存查询",name:"savedQuery"}},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageSettingList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[n("el-table-column",{attrs:{label:"名称","min-width":"200px"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",{staticClass:"link-type",on:{click:function(t){return e.handleQuickSearch(o)}}},[e._v(e._s(o.name))])]}}])}),n("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteSetting(o)}}},[e._v(" 删除 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForSetting.total>0,expression:"listQueryForSetting.total > 0"}],attrs:{total:e.listQueryForSetting.total,"page-sizes":[10,20,50],page:e.listQueryForSetting.pageIndex,limit:e.listQueryForSetting.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForSetting,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForSetting,"pageSize",t)},pagination:e.getSettingList}})],1)],1)],1)},f=[],g=(n("a4d3"),n("e01a"),n("4de4"),n("c975"),n("a434"),n("b0c0"),n("f9ac")),h={name:"",components:{},data:function(){return{activeName:"querySetting",dataList:[],listQuery:{conditionList:[]},listLoading:!1,rules:{keywords:[{max:100,message:"关键字不允许超过100个字符",trigger:"blur"}]},tempData:{tempFormModel:{keywords:""},tempFormModelForSave:{}},logicRelationshipOptions:[],empColumnOptions:[],selectConditionOptions:[],dialogCombineVisible:!1,relationshipForCombine:10,rulesForSave:{name:[{required:!0,message:"请输入名称",trigger:"blur"},{max:100,message:"名称不允许超过100个字符",trigger:"blur"}]},dialogSaveVisible:!1,pageSettingList:[],listQueryForSetting:{total:1,pageIndex:1,pageSize:10}}},created:function(){this.loadRelationships(),this.loadColumns(),this.loadConditions()},methods:{tabClick:function(e){"savedQuery"===e.name&&(this.listQueryForSetting.pageIndex=1,this.reloadSettingList())},loadRelationships:function(){var e=this;g["a"].getEnumInfos({enumType:"LogicRelationships"}).then((function(t){e.logicRelationshipOptions=t.data.datas,e.logicRelationshipOptions.length>0&&(e.tempData.tempFormModel.enumLogicRelationships=e.logicRelationshipOptions[0])})).catch((function(e){console.log(e)}))},loadColumns:function(){var e=this;a["a"].querySettingColumns().then((function(t){e.empColumnOptions=t.data.datas,e.empColumnOptions.length>0&&(e.tempData.tempFormModel.selectedColumn=e.empColumnOptions[0])})).catch((function(e){console.log(e)}))},loadConditions:function(){var e=this;g["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas,e.selectConditionOptions.length>0&&(e.tempData.tempFormModel.selectedCondition=e.selectConditionOptions[e.selectConditionOptions.length-1])})).catch((function(e){console.log(e)}))},columnChange:function(e){this.$forceUpdate()},conditionChange:function(e){this.$forceUpdate()},relationChange:function(e){this.$forceUpdate()},quickSearch:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var n=e.tempData.tempFormModel,o={enumLogicRelationships:n.enumLogicRelationships.value,displayRelationship:n.enumLogicRelationships.desc,entityColumnName:n.selectedColumn.entityColumnName,displayName:n.selectedColumn.displayName,entityColumnType:n.selectedColumn.entityColumnType,enumOperations:n.selectedCondition.value,displayOperation:n.selectedCondition.desc,keywords:e.tempData.tempFormModel.keywords};e.listQuery.conditionList=e.dataList,e.listQuery.conditionList.push(o),e.listQuery.employeeQuerySettingId&&delete e.listQuery.employeeQuerySettingId,e.$emit("search",e.listQuery)}}))},search:function(){this.listQuery.conditionList=this.dataList,this.listQuery.conditionList.length>0&&(this.listQuery.employeeQuerySettingId&&delete this.listQuery.employeeQuerySettingId,this.$emit("search",this.listQuery))},handleQuickSearch:function(e){this.listQuery.conditionList&&delete this.listQuery.conditionList,this.listQuery.employeeQuerySettingId=e.id,this.$emit("search",this.listQuery)},clear:function(){this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={},this.dataList=[]},addData:function(e){var t=this;this.$refs["dataForm"].validate((function(n){if(n){var o={enumLogicRelationships:e.enumLogicRelationships.value,displayRelationship:e.enumLogicRelationships.desc,entityColumnName:e.selectedColumn.entityColumnName,displayName:e.selectedColumn.displayName,entityColumnType:e.selectedColumn.entityColumnType,enumOperations:e.selectedCondition.value,displayOperation:e.selectedCondition.desc,keywords:e.keywords};o.description=o.displayName+" "+o.displayOperation+" "+o.keywords,t.dataList.push(o)}}))},deleteData:function(e){this.dataList.splice(this.dataList.indexOf(e),1)},showCombineData:function(){this.dialogCombineVisible=!0},colseCombine:function(){this.dialogCombineVisible=!1,this.relationshipForCombine=10},combineData:function(){var e=this,t=this.$refs.dataListTable.selection;if(!t||t.length<2)this.$notice.message("所选记录数量不足，无需组合。","info");else{for(var n={enumLogicRelationships:t[0].enumLogicRelationships,displayRelationship:t[0].displayRelationship,description:""},o=[],r=0;r<t.length;r++){var i=JSON.parse(JSON.stringify(t[r]));i.enumLogicRelationships=this.relationshipForCombine,i.displayRelationship=this.logicRelationshipOptions.filter((function(t){return t.value===e.relationshipForCombine}))[0].desc,o.push(i),n.description=0===r?i.description:n.description+" "+i.displayRelationship+" "+i.description,this.dataList.splice(this.dataList.indexOf(t[r]),1)}n.children=o,this.dataList.push(n),this.colseCombine()}},showSaveDialog:function(){this.dialogSaveVisible=!0},save:function(){var e=this;if(this.addData(this.tempData.tempFormModel),!this.dataList||this.dataList&&0===this.dataList)this.$notice.message("列表中无数据，无法保存。","info");else{var t={name:this.tempData.tempFormModelForSave.name,employeeQuerySettingItem:this.dataList};this.$refs["dataFormForSave"].validate((function(n){n&&a["a"].addEmployeeQuerySetting(t).then((function(t){t.succeed?(e.$notice.message("保存成功","success"),e.activeName="savedQuery",e.reloadSettingList(),e.clearSaveForm(),e.colseCombine(),e.dialogSaveVisible=!1):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("保存失败。","error")}))}))}},cancle:function(){this.$emit("CloseDialog")},cancleSave:function(){this.dialogSaveVisible=!1,this.clearSaveForm()},clearSaveForm:function(){this.$refs["dataFormForSave"].resetFields(),this.tempData.tempFormModelForSave={}},getSettingList:function(){var e=this;a["a"].queryEmployeeQuerySetting(this.listQueryForSetting).then((function(t){t.succeed?(e.pageSettingList=t.data.datas,e.listQueryForSetting.total=t.data.recordCount,e.listQueryForSetting.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},reloadSettingList:function(){this.listQueryForSetting.pageIndex=1,this.getSettingList()},deleteSetting:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a["a"].deleteEmployeeQuerySetting(e).then((function(e){e.succeed?(t.getSettingList(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},E=h,S=(n("8381"),Object(p["a"])(E,m,f,!1,null,"667c56dd",null)),b=S.exports,v={components:{hRInfo:y,querySetting:b},data:function(){return{treeData:[],treeExpandedKeys:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},empDialogTitle:"人事信息",selected:"",currentNode:{},includeDownDept:!1,listQuery:{total:1,pageIndex:1,pageSize:10},listLoading:!1,treeLoading:!1,dialogAppInfoVisible:!1,activeName:"tab1",empId:"",empModel:{},dialogQuerySettingVisible:!1,pageEmpInfoList:[]}},created:function(){this.loadTree()},methods:{loadTree:function(){var e=this;this.treeLoading=!0,i["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeData&&e.treeData.length>0&&e.treeExpandedKeys.push(e.treeData[0].id)})).catch((function(e){console.log(e)})).finally((function(){e.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){this.currentNode=e,this.listQuery.deptId=e.id,this.listQuery.pageIndex=1,this.queryEmployeeByConditions()},handleWatchEmpInfo:function(e){this.empDialogTitle="人事信息——"+e.displayName,this.empId=e.id,this.empModel=e,this.dialogAppInfoVisible=!0},handleClose:function(){this.dialogAppInfoVisible=!1},updateEmpId:function(e){this.empId=e},selectedChange:function(){this.listQuery.pageIndex=1,this.queryEmployeeByConditions()},querySetting:function(){this.dialogQuerySettingVisible=!0},quickSearch:function(e){this.hideQuerySettingDialog(),e.conditionList&&(this.listQuery.conditionList=e.conditionList,this.listQuery.employeeQuerySettingId=null,this.listQuery.deptId=null),e.employeeQuerySettingId&&(this.listQuery.employeeQuerySettingId=e.employeeQuerySettingId,this.listQuery.deptId=null,this.listQuery.conditionList=null),this.listQuery.pageIndex=1,this.queryEmployeeByConditions()},queryEmployeeByConditions:function(){var e=this;this.listQuery.deptId&&(this.listQuery.employeeQuerySettingId=null,this.listQuery.conditionList=null),a["a"].queryEmployeeByConditions(this.listQuery).then((function(t){t.succeed?(e.pageEmpInfoList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},sortChangeForEmpInfo:function(e,t,n){console.log(e),console.log(t),console.log(n),this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.queryEmployeeByConditions()},hideQuerySettingDialog:function(){this.dialogQuerySettingVisible=!1,this.$refs["querySetting"].clear()}}},D=v,Q=(n("d427"),n("3981"),Object(p["a"])(D,o,r,!1,null,"60745f3b",null));t["default"]=Q.exports},"2cf46":function(e,t,n){},3981:function(e,t,n){"use strict";var o=n("a9ac"),r=n.n(o);r.a},8381:function(e,t,n){"use strict";var o=n("cf90"),r=n.n(o);r.a},"84de":function(e,t,n){"use strict";var o=n("c091"),r=n.n(o);r.a},a434:function(e,t,n){"use strict";var o=n("23e7"),r=n("23cb"),i=n("a691"),a=n("50c4"),l=n("7b0b"),u=n("65f0"),s=n("8418"),c=n("1dde"),p=n("ae40"),d=c("splice"),y=p("splice",{ACCESSORS:!0,0:0,1:2}),m=Math.max,f=Math.min,g=9007199254740991,h="Maximum allowed length exceeded";o({target:"Array",proto:!0,forced:!d||!y},{splice:function(e,t){var n,o,c,p,d,y,E=l(this),S=a(E.length),b=r(e,S),v=arguments.length;if(0===v?n=o=0:1===v?(n=0,o=S-b):(n=v-2,o=f(m(i(t),0),S-b)),S+n-o>g)throw TypeError(h);for(c=u(E,o),p=0;p<o;p++)d=b+p,d in E&&s(c,p,E[d]);if(c.length=o,n<o){for(p=b;p<S-o;p++)d=p+o,y=p+n,d in E?E[y]=E[d]:delete E[y];for(p=S;p>S-o+n;p--)delete E[p-1]}else if(n>o)for(p=S-o;p>b;p--)d=p+o-1,y=p+n-1,d in E?E[y]=E[d]:delete E[y];for(p=0;p<n;p++)E[p+b]=arguments[p+2];return E.length=S-o+n,c}})},a9ac:function(e,t,n){},c091:function(e,t,n){},cf90:function(e,t,n){},d368:function(e,t,n){"use strict";var o=n("cfe3"),r="Organization",i=new o["a"](r);t["a"]={QueryOrganizationHiddenTop:function(e){return i.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return i.get("QueryOrganization",e)},QueryDepartment:function(e){return i.get("QueryDepartment",e)},GetDepartment:function(e){return i.get("GetDepartment",e)},AddDepartment:function(e){return i.post("AddDepartment",e)},UpdateDepartment:function(e){return i.post("UpdateDepartment",e)},MoveDepartment:function(e){return i.post("MoveDepartment",e)},MergeDepartment:function(e){return i.post("MergeDepartment",e)},DeleteDepartment:function(e){return i.post("DeleteDepartment",e)},queryPosition:function(e){return i.post("QueryPosition",e)},getPosition:function(e){return i.get("GetPosition",e)},addPosition:function(e){return i.post("AddPosition",e)},updatePosition:function(e){return i.post("UpdatePosition",e)},deletePosition:function(e){return i.post("DeletePosition",e)},GetStation:function(e){return i.get("GetStation",e)},AddStation:function(e){return i.post("AddStation",e)},UpdateStation:function(e){return i.post("UpdateStation",e)},DeleteStation:function(e){return i.post("DeleteStation",e)},QueryPositionStationTree:function(e){return i.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return i.post("AllocatePosition",e)},DeletePositionStation:function(e){return i.post("DeletePositionStation",e)},queryDeptByUser:function(e){return i.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return i.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return i.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return i.get("QuerySenioritySelect")},queryStationAllowance:function(e){return i.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return i.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),i.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return i.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return i.get("GetStationAllowance",e)},addStationAllowance:function(e){return i.post("AddStationAllowance",e)},updateStationAllowance:function(e){return i.post("UpdateStationAllowance",e)},querySeniority:function(e){return i.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),i.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return i.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return i.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return i.get("GetSeniority",e)},addSeniority:function(e){return i.post("AddSeniority",e)},updateSeniority:function(e){return i.post("UpdateSeniority",e)},querySalaryScale:function(e){return i.get("QuerySalaryScale",e)},getSalaryScale:function(e){return i.get("GetSalaryScale",e)},addSalaryScale:function(e){return i.post("AddSalaryScale",e)},updateSalaryScale:function(e){return i.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return i.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),i.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return i.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return i.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return i.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return i.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return i.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return i.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return i.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return i.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return i.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return i.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return i.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return i.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return i.post("DeleteTelephoneFee",e)}}},d427:function(e,t,n){"use strict";var o=n("2cf46"),r=n.n(o);r.a},e44c:function(e,t,n){"use strict";n("4160"),n("b64b"),n("159b");var o=n("cfe3"),r="HR",i=new o["a"](r);t["a"]={queryEmployee:function(e){return i.get("QueryEmployee",e)},queryEmployeeByCommonCondition:function(e){return i.post("QueryEmployeeByCommonCondition",e)},queryEmployeeStatus:function(){return i.get("QueryEmployeeStatus")},queryRank:function(){return i.get("QueryRank")},queryAdministrativePosition:function(){return i.get("queryAdministrativePosition")},queryMajorTechnical:function(){return i.get("queryMajorTechnical")},queryOfficialRank:function(){return i.get("QueryOfficialRank")},queryHireStyle:function(){return i.get("QueryHireStyle")},queryLeaveStyle:function(){return i.get("QueryLeaveStyle")},queryMarryList:function(){return i.get("QueryMarryList")},queryNationality:function(){return i.get("QueryNationality")},queryRegisterType:function(){return i.get("QueryRegisterType")},deleteEmployee:function(e){return i.post("DeleteEmployee",e)},queryDocumentType:function(){return i.get("QueryDocumentType")},addEmployee:function(e){return i.post("AddEmployee",e)},isEmpCodeExists:function(e){var t={id:e.id,empCode:e.empCode};return i.get("IsEmpCodeExists",t)},checkIdentityNumber:function(e){var t={id:e.id,identityNumber:e.identityNumber};return i.get("checkIdentityNumber",t)},getEmployee:function(e){return i.get("GetEmployee",e)},updateEmployee:function(e){return i.post("UpdateEmployee",e)},getPersonnelInformation:function(e){return i.get("GetPersonnelInformation",e)},postAddDocumentInformation:function(e){return i.get("PostAddDocumentInformation",e)},postUpdateDocumentInformation:function(e){return i.get("PostUpdateDocumentInformation",e)},postDeleteDocumentInformation:function(e){return i.get("PostDeleteDocumentInformation",e)},queryEmployeeByConditions:function(e){return i.post("QueryEmployeeByConditions",e)},queryEmployeeByCommonConditions:function(e){return i.post("QueryEmployeeByCommonConditions",e)},queryEmployeeQuerySetting:function(e){return i.get("QueryEmployeeQuerySetting",e)},querySettingColumns:function(){return i.get("QueryEmployeeInfoQuerySettingColumns")},queryOperations:function(e){return i.get("QueryOperationByColumnType",e)},addEmployeeQuerySetting:function(e){return i.post("AddEmployeeQuerySetting",e)},deleteEmployeeQuerySetting:function(e){return i.post("DeleteEmployeeQuerySetting",e)},queryDegrees:function(){return i.get("QueryDegrees")},queryEducation:function(){return i.get("QueryEducation")},QuerySocialSecurity:function(){return i.get("QuerySocialSecurity")},queryParty:function(){return i.get("QueryParty")},queryRecruitmentCategory:function(){return i.get("QueryRecruitmentCategory")},queryRecruitmentCompany:function(){return i.get("QueryRecruitmentCompany")},getEmployeeHR:function(e){return i.get("GetEmployeeHR",e)},updateEmployeeHR:function(e){return i.post("UpdateEmployeeHR",e)},updateCompanyAge:function(){return i.post("UpdateCompanyAge")},calculateGeneralHoliday:function(){return i.get("CalculateGeneralHoliday")},queryStation:function(e){return i.get("QueryStation",e)},queryPositionStation:function(e){return i.get("QueryPositionStation",e)},queryEmployeeStation:function(e){return i.get("QueryEmployeeStation",e)},addEmployeeStation:function(e){return i.post("AddEmployeeStation",e)},updateEmployeeStation:function(e){return i.post("UpdateEmployeeStation",e)},deleteEmployeeStation:function(e){return i.post("DeleteEmployeeStation",e)},queryLevel:function(){return i.get("QueryLevel")},queryEmployeeCertify:function(e){return i.get("QueryEmployeeCertify",e)},addEmployeeCertify:function(e){return i.post("AddEmployeeCertify",e)},updateEmployeeCertify:function(e){return i.post("UpdateEmployeeCertify",e)},deleteEmployeeCertify:function(e){return i.post("DeleteEmployeeCertify",e)},queryGraduation:function(){return i.get("QueryGraduation")},queryLearnWay:function(){return i.get("QueryLearnWay")},queryEmployeeEducation:function(e){return i.get("QueryEmployeeEducation",e)},addEmployeeEducation:function(e){return i.post("AddEmployeeEducation",e)},updateEmployeeEducation:function(e){return i.post("UpdateEmployeeEducation",e)},deleteEmployeeEducation:function(e){return i.post("DeleteEmployeeEducation",e)},queryEmployeeWork:function(e){return i.get("QueryEmployeeWork",e)},addEmployeeWork:function(e){return i.post("AddEmployeeWork",e)},updateEmployeeWork:function(e){return i.post("UpdateEmployeeWork",e)},deleteEmployeeWork:function(e){return i.post("DeleteEmployeeWork",e)},queryAbroadType:function(){return i.get("QueryAbroadType")},queryEmployeeAbroad:function(e){return i.get("QueryEmployeeAbroadInfo",e)},addEmployeeAbroad:function(e){return i.post("AddEmployeeAbroadInfo",e)},updateEmployeeAbroad:function(e){return i.post("UpdateEmployeeAbroadInfo",e)},deleteEmployeeAbroad:function(e){return i.post("DeleteEmployeeAbroadInfo",e)},queryContractType:function(){return i.get("QueryContractType")},queryEmployeeContract:function(e){return i.get("QueryEmployeeContract",e)},queryRenewEmployeeContract:function(e){return i.get("QueryRenewEmployeeContract",e)},updateNextTimeRemind:function(e){return i.post("UpdateNextTimeRemind",e)},sendEmailForRenewRemind:function(e){return i.post("SendEmailForRenewRemind",e)},batchUpdateNextTimeRemind:function(e){return i.post("BatchUpdateNextTimeRemind",e)},batchSendEmailForRenewRemind:function(e){return i.post("BatchSendEmailForRenewRemind",e)},addEmployeeContract:function(e){return i.post("AddEmployeeContract",e)},updateEmployeeContract:function(e){return i.post("UpdateEmployeeContract",e)},deleteEmployeeContract:function(e){return i.post("DeleteEmployeeContract",e)},queryTrainLevel:function(){return i.get("QueryTrainLevel")},queryEmployeeTrain:function(e){return i.get("QueryEmployeeTrain",e)},addEmployeeTrain:function(e){return i.post("AddEmployeeTrain",e)},updateEmployeeTrain:function(e){return i.post("UpdateEmployeeTrain",e)},deleteEmployeeTrain:function(e){return i.post("DeleteEmployeeTrain",e)},queryYearList:function(){return i.get("QueryYearList")},queryEvaluateResult:function(){return i.get("QueryEvaluateResult")},queryEmployeeAssessment:function(e){return i.get("QueryEmployeeAssessment",e)},addEmployeeAssessment:function(e){return i.post("AddEmployeeAssessment",e)},updateEmployeeAssessment:function(e){return i.post("UpdateEmployeeAssessment",e)},deleteEmployeeAssessment:function(e){return i.post("DeleteEmployeeAssessment",e)},queryEmployeeDeduct:function(e){return i.get("QueryEmployeeDeduct",e)},addEmployeeDeduct:function(e){return i.post("AddEmployeeDeduct",e)},updateEmployeeDeduct:function(e){return i.get("UpdateEmployeeDeduct",e)},deleteEmployeeDeduct:function(e){return i.post("DeleteEmployeeDeduct",e)},downloadEmployeeDeductTemplate:function(e){return i.getFile("DownlodaEmployeeDeductTemplate",e)},importEmployeeDeduct:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),i.postForm("ImportEmployeeDeduct",n)},queryEmployeeDeductUnCalculate:function(e){return i.get("QueryEmployeeDeductUnCalculate",e)},queryEmployeeDeductCalculate:function(e){return i.get("QueryEmployeeDeductCalculate",e)},getEmployeeDeductCalculate:function(e){return i.get("GetEmployeeDeductCalculate",e)},updateEmployeeDeductCalculate:function(e){return i.post("UpdateEmployeeDeductCalculate",e)},exportEmployeeDeductCalculate:function(e){return i.post("ExportEmployeeDeductCalculate",{data:e,responseType:"arraybuffer"})},queryEmployeeDeductWorkingAge:function(e){return i.get("QueryEmployeeDeductWorkingAge",e)},getEmployeeDeductWorkingAge:function(e){return i.get("GetEmployeeDeductWorkingAge",e)},addEmployeeDeductWorkingAge:function(e){return i.post("AddEmployeeDeductWorkingAge",e)},updateEmployeeDeductWorkingAge:function(e){return i.post("UpdateEmployeeDeductWorkingAge",e)},deleteEmployeeDeductWorkingAge:function(e){return i.post("DeleteEmployeeDeductWorkingAge",e)},queryIncentType:function(){return i.get("QueryIncentType")},queryIncentLevel:function(){return i.get("QueryIncentLevel")},queryEmployeeIncentive:function(e){return i.get("QueryEmployeeIncentive",e)},addEmployeeIncentive:function(e){return i.post("AddEmployeeIncentive",e)},updateEmployeeIncentive:function(e){return i.post("UpdateEmployeeIncentive",e)},deleteEmployeeIncentive:function(e){return i.post("DeleteEmployeeIncentive",e)},getEmployeeHealth:function(e){return i.get("GetEmployeeHealth",e)},updateEmployeeHealth:function(e){return i.post("UpdateEmployeeHealth",e)},queryAccidentType:function(){return i.get("QueryAccidentType")},queryEmployeeAccident:function(e){return i.get("QueryEmployeeAccident",e)},addEmployeeAccident:function(e){return i.post("AddEmployeeAccident",e)},updateEmployeeAccident:function(e){return i.post("UpdateEmployeeAccident",e)},deleteEmployeeAccident:function(e){return i.post("DeleteEmployeeAccident",e)},queryEmployeeTeach:function(e){return i.get("QueryEmployeeTeach",e)},addEmployeeTeach:function(e){return i.post("AddEmployeeTeach",e)},updateEmployeeTeach:function(e){return i.post("UpdateEmployeeTeach",e)},deleteEmployeeTeach:function(e){return i.post("DeleteEmployeeTeach",e)},queryEmployeeRelation:function(e){return i.get("QueryEmployeeRelation",e)},addEmployeeRelation:function(e){return i.post("AddEmployeeRelation",e)},updateEmployeeRelation:function(e){return i.post("UpdateEmployeeRelation",e)},deleteEmployeeRelation:function(e){return i.post("DeleteEmployeeRelation",e)},queryIncomeType:function(){return i.get("QueryIncomeType")},queryEmployeeArticle:function(e){return i.get("QueryEmployeeArticle",e)},addEmployeeArticle:function(e){return i.post("AddEmployeeArticle",e)},updateEmployeeArticle:function(e){return i.post("UpdateEmployeeArticle",e)},deleteEmployeeArticle:function(e){return i.post("DeleteEmployeeArticle",e)},queryClassLevel:function(){return i.get("QueryClassLevel")},queryEmployeeClass:function(e){return i.get("QueryEmployeeClass",e)},addEmployeeClass:function(e){return i.post("AddEmployeeClass",e)},updateEmployeeClass:function(e){return i.post("UpdateEmployeeClass",e)},deleteEmployeeClass:function(e){return i.post("DeleteEmployeeClass",e)},queryEmployeePatent:function(e){return i.get("QueryEmployeePatent",e)},addEmployeePatent:function(e){return i.post("AddEmployeePatent",e)},updateEmployeePatent:function(e){return i.post("UpdateEmployeePatent",e)},deleteEmployeePatent:function(e){return i.post("DeleteEmployeePatent",e)},queryTeacherType:function(){return i.get("QueryTeacherType")},queryEmployeeTeacher:function(e){return i.get("QueryEmployeeTeacher",e)},addEmployeeTeacher:function(e){return i.post("AddEmployeeTeacher",e)},updateEmployeeTeacher:function(e){return i.post("UpdateEmployeeTeacher",e)},deleteEmployeeTeacher:function(e){return i.post("DeleteEmployeeTeacher",e)},queryAwardLevel:function(){return i.get("QueryAwardLevel")},queryDictByParentCode:function(e){return i.get("QueryDictByParentCode",e)},queryHighTalent:function(){return i.get("QueryHighTalent")},queryEmployeeAward:function(e){return i.get("QueryEmployeeAward",e)},addEmployeeAward:function(e){return i.post("AddEmployeeAward",e)},updateEmployeeAward:function(e){return i.post("UpdateEmployeeAward",e)},deleteEmployeeAward:function(e){return i.post("DeleteEmployeeAward",e)},addEmployeeHighTalent:function(e){return i.post("AddEmployeeHighTalent",e)},updateEmployeeHighTalent:function(e){return i.post("UpdateEmployeeHighTalent",e)},queryEmployeeHighTalent:function(e){return i.get("QueryEmployeeHighTalent",e)},deleteEmployeeHighTalent:function(e){return i.post("DeleteEmployeeHighTalent",e)},queryEmployeeDeptHistory:function(e){return i.get("QueryEmployeeDeptHistory",e)},updateEmployeeDept:function(e){return i.post("UpdateEmployeeDept",e)},batchUpdateEmployeeDept:function(e){return i.post("BatchUpdateEmployeeDept",e)},queryEmployeeList:function(e){return i.post("QueryEmployeeList",e)},queryEmployeeListTree:function(e){return i.get("QueryEmployeeListTree",e)},queryDictsSetting:function(e){return i.get("QueryDictsSetting",e)},queryEmployeeListSettingTree:function(e){return i.get("QueryEmployeeListSettingTree",e)},queryAdvancedQueryType:function(){return i.get("QueryAdvancedQueryType")},saveEmployeeList:function(e){return i.post("SaveEmployeeList",e)},deleteEmployeeList:function(e){return i.post("DeleteEmployeeList",e)},getEmployeeList:function(e){return i.get("GetEmployeeList",e)},getEmployeeListExcel:function(e){return i.getFile("GetEmployeeListExcel",e)},queryDeptPrincipal:function(e){return i.get("QueryDeptPrincipal",e)},queryOtherEmployeeInfo:function(e){return i.post("QueryOtherEmployeeInfo",e)},saveOtherEmployeeInfo:function(e){return i.post("SaveOtherEmployeeInfo",e)},deleteOtherEmployeeInfo:function(e){return i.post("DeleteOtherEmployeeInfo",e)},queryOtherEmpTypes:function(e){return i.get("QueryOtherEmpTypes",e)},importExcel:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),i.postForm("ImportExcel",n)},downlodaImportExcelTemplate:function(e){return i.getFile("DownlodaImportExcelTemplate",e)},getSocialSecurityInfo:function(e){return i.get("getSocialSecurityInfo",e)},updateEmployeeSocialSecurity:function(e){return i.post("updateEmployeeSocialSecurity",e)},queryPayRollOrgClass:function(){return i.get("QueryPayRollOrgClass")},queryPayRollCompGroup:function(){return i.get("QueryPayRollCompGroup")},queryPayRollOrgSalary:function(e){return i.get("QueryPayRollOrgSalary",e)},queryPayRollOrgSalaryLevel:function(e){return i.get("QueryPayRollOrgSalaryLevel",e)},queryPayRollOrgPositionSalarys:function(e){return i.get("QueryPayRollOrgPositionSalarys",e)},queryWorkState:function(){return i.get("QueryWorkState")},getWagesInfo:function(e){return i.get("GetWagesInfo",e)},updateEmployeeBenefit:function(e){return i.post("UpdateEmployeeBenefit",e)},queryWagesHistory:function(e){return i.get("QueryWagesHistory",e)},getEmployeePayrollPrint:function(e){return i.get("GetEmployeePayrollPrint",e)},updateEmployeePayrollPrint:function(e){return i.post("UpdateEmployeePayrollPrint",e)},deleteEmployeePayrollPrint:function(e){return i.post("DeleteEmployeePayrollPrint",e)},getPrintDetailsInfo:function(e){return i.get("GetPrintDetailsInfo",e)},getEmployeeWage:function(e){return i.get("GetEmployeeWage",e)},editEmployeeWage:function(e){return i.post("EditEmployeeWage",e)},editEmployeeParty:function(e){return i.post("EditEmployeeParty",e)},queryEmployeeHRAffiliatedBranch:function(e){return i.get("QueryEmployeeHRAffiliatedBranch",e)},getEmployeeHRAffiliatedBranch:function(e){return i.get("GetEmployeeHRAffiliatedBranch",e)},addEmployeeHRAffiliatedBranch:function(e){return i.post("AddEmployeeHRAffiliatedBranch",e)},updateEmployeeHRAffiliatedBranch:function(e){return i.post("UpdateEmployeeHRAffiliatedBranch",e)},deleteEmployeeHRAffiliatedBranch:function(e){return i.post("DeleteEmployeeHRAffiliatedBranch",e)},queryEmployeeHRDict:function(e){return i.get("QueryEmployeeHRDict",e)},getEmployeeHRDict:function(e){return i.get("GetEmployeeHRDict",e)},addEmployeeHRDict:function(e){return i.post("AddEmployeeHRDict",e)},updateEmployeeHRDict:function(e){return i.post("UpdateEmployeeHRDict",e)},deleteEmployeeHRDict:function(e){return i.post("DeleteEmployeeHRDict",e)},queryEmployeeHRPartyMemberHonor:function(e){return i.get("QueryEmployeeHRPartyMemberHonor",e)},getEmployeeHRPartyMemberHonor:function(e){return i.get("GetEmployeeHRPartyMemberHonor",e)},addEmployeeHRPartyMemberHonor:function(e){return i.post("AddEmployeeHRPartyMemberHonor",e)},updateEmployeeHRPartyMemberHonor:function(e){return i.post("UpdateEmployeeHRPartyMemberHonor",e)},deleteEmployeeHRPartyMemberHonor:function(e){return i.post("DeleteEmployeeHRPartyMemberHonor",e)}}},f9ac:function(e,t,n){"use strict";var o=n("cfe3"),r="SysManage",i=new o["a"](r);t["a"]={queryDict:function(e){return i.get("QueryDict",e)},queryDictType:function(e){return i.post("QueryDictType",e)},addDict:function(e){return i.post("AddDict",e)},deleteDict:function(e){return i.post("DeleteDict",e)},updateDict:function(e){return i.post("UpdateDict",e)},getDict:function(e){return i.get("GetDict",e)},querySysSetting:function(e){return i.get("QuerySysSetting",e)},addSysSetting:function(e){return i.post("AddSysSetting",e)},deleteSysSetting:function(e){return i.post("DeleteSysSetting",e)},updateSysSetting:function(e){return i.post("UpdateSysSetting",e)},getSysSetting:function(e){return i.get("GetSysSetting",e)},queryLanguage:function(e){return i.get("QueryLanguage",e)},getEnumInfos:function(e){return i.get("GetEnumInfos",e)},queryUserGroups:function(e){return i.post("QueryUserGroups",e)},saveUserGroup:function(e){return i.post("SaveUserGroup",e)},deleteUserGroup:function(e){return i.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return i.get("DropdownUserGroups",e)},queryUsers:function(e){return i.post("QueryUsers",e)},saveUser:function(e){return i.post("SaveUser",e)},deleteUser:function(e){return i.post("DeleteUser",e)},initPwd:function(e){return i.post("InitPwd",e)},getUserById:function(e){return i.get("GetUserById",e)},queryEmployees:function(e){return i.post("QueryEmployees",e)},queryModuleInfos:function(e){return i.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return i.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return i.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return i.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return i.post("SaveRightOfDept",e)},queryControlRight:function(e){return i.post("QueryControlRight",e)},saveControlRights:function(e){return i.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return i.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return i.get("QueryStationTree",e)},queryStationTypeSelector:function(){return i.get("QueryStationTypeSelector")},queryStationSelector:function(e){return i.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return i.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return i.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return i.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return i.get("QueryStationAllowance",e)}}}}]);