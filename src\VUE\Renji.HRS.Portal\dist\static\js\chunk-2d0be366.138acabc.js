(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0be366"],{"2efc":function(e,t,o){"use strict";var n=o("cfe3"),r="Salary",a=new n["a"](r);t["a"]={querySalary:function(e){return a.get("QuerySalary",e)},queryEmployeeSalaryStatus:function(e){return a.get("QueryEmployeeSalaryStatus",e)},getSalary:function(e){return a.get("GetSalary",e)},addSalary:function(e){return a.post("AddSalary",e)},updateSalary:function(e){return a.post("UpdateSalary",e)},deleteSalary:function(e){return a.post("DeleteSalary",e)},approvalSalary:function(e){return a.post("ApprovalSalary",e)},querySalaryDetail:function(e){return a.post("QuerySalaryDetail",e)},queryLastMonthEmployeeSalary:function(e){return a.get("QueryLastMonthEmployeeSalary",e)},getSalaryDetail:function(e){return a.get("GetSalaryDetail",e)},queryHROnAccountView:function(e){return a.get("QueryHROnAccountView",e)},queryHROnAccount:function(e){return a.get("QueryHROnAccount",e)},updateHROnAccountWriteOff:function(e){return a.post("UpdateHROnAccountWriteOff",e)},querySocialSecurityWithhold:function(e){return a.get("QuerySocialSecurityWithhold",e)},getfourGoldDeficiencySign:function(e){return a.get("GetfourGoldDeficiencySign",e)},updateSalaryDetail:function(e){return a.post("UpdateSalaryDetail",e)},calculateSalaryDetail:function(e){return a.post("CalculateSalaryDetail",e)},importSalaryDetail:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportSalaryDetail",t)},exportSalaryDetail:function(e){return a.post("ExportSalaryDetail",{data:e,responseType:"arraybuffer"})},exportHRData:function(e){return a.post("ExportHRData",{data:e,responseType:"arraybuffer"})},queryEmployeeSalary:function(e){return a.get("QueryEmployeeSalary",e)},exportEmployeeSalaryRecord:function(e){return a.post("ExportEmployeeSalaryRecord",{data:e,responseType:"arraybuffer"})},getEmployeeSalary:function(e){return a.get("GetEmployeeSalary",e)},updateEmployeeSalary:function(e){return a.post("UpdateEmployeeSalary",e)},calculateEmployeeSalaryEstimate:function(e){return a.post("CalculateEmployeeSalaryEstimate",e)},syncEmployeeSalary:function(){return a.post("SyncEmployeeSalary")},querySalaryData:function(e){return a.get("QuerySalaryData",e)},getSalaryData:function(e){return a.get("GetSalaryData",e)},getSalaryDataCodes:function(e){return a.get("GetSalaryDataCodes",e)},updateSalaryData:function(e){return a.post("UpdateSalaryData",e)},queryCalendar:function(e){return a.get("QueryCalendar",e)},getCalendar:function(e){return a.get("GetCalendar",e)},updateCalendar:function(e){return a.post("UpdateCalendar",e)},deleteCalendar:function(e){return a.post("DeleteCalendar",e)},addCalendar:function(e){return a.post("AddCalendar",e)},queryEmployeeDeceased:function(e){return a.get("QueryEmployeeDeceased",e)},getEmployeeDeceased:function(e){return a.get("GetEmployeeDeceased",e)},updateEmployeeDeceased:function(e){return a.post("UpdateEmployeeDeceased",e)},addEmployeeDeceased:function(e){return a.post("AddEmployeeDeceased",e)},deleteEmployeeDeceased:function(e){return a.post("DeleteEmployeeDeceased",e)},autoImportEmployeeDeceased:function(e){return a.get("AutoImportEmployeeDeceased",e)},queryEmployeeRetire:function(e){return a.get("QueryEmployeeRetire",e)},getEmployeeRetire:function(e){return a.get("GetEmployeeRetire",e)},updateEmployeeRetire:function(e){return a.post("UpdateEmployeeRetire",e)},addEmployeeRetire:function(e){return a.post("AddEmployeeRetire",e)},deleteEmployeeRetire:function(e){return a.post("DeleteEmployeeRetire",e)},autoImportEmployeeRetire:function(e){return a.post("AutoImportEmployeeRetire",e)},queryEmployeeResign:function(e){return a.get("QueryEmployeeResign",e)},getEmployeeResign:function(e){return a.get("GetEmployeeResign",e)},updateEmployeeResign:function(e){return a.post("UpdateEmployeeResign",e)},addEmployeeResign:function(e){return a.post("AddEmployeeResign",e)},deleteEmployeeResign:function(e){return a.post("DeleteEmployeeResign",e)},autoImportEmployeeResign:function(e){return a.post("AutoImportEmployeeResign",e)},querySalaryEmployees:function(e){return a.get("QuerySalaryEmployees",e)},queryEmployeePostdoctorTwoYears:function(e){return a.get("QueryEmployeePostdoctorTwoYears",e)},getEmployeePostdoctorTwoYears:function(e){return a.get("GetEmployeePostdoctorTwoYears",e)},updateEmployeePostdoctorTwoYears:function(e){return a.post("UpdateEmployeePostdoctorTwoYears",e)},addEmployeePostdoctorTwoYears:function(e){return a.post("AddEmployeePostdoctorTwoYears",e)},deleteEmployeePostdoctorTwoYears:function(e){return a.post("DeleteEmployeePostdoctorTwoYears",e)},autoImportEmployeePostdoctorTwoYears:function(e){return a.post("AutoImportEmployeePostdoctorTwoYears",e)},queryEmployeePostdoctorOutbound:function(e){return a.get("QueryEmployeePostdoctorOutbound",e)},getEmployeePostdoctorOutbound:function(e){return a.get("GetEmployeePostdoctorOutbound",e)},updateEmployeePostdoctorOutbound:function(e){return a.post("UpdateEmployeePostdoctorOutbound",e)},addEmployeePostdoctorOutbound:function(e){return a.post("AddEmployeePostdoctorOutbound",e)},deleteEmployeePostdoctorOutbound:function(e){return a.post("DeleteEmployeePostdoctorOutbound",e)},autoImportEmployeePostdoctorOutbound:function(e){return a.post("autoImportEmployeePostdoctorOutbound",e)},queryEmployeeOtherConditionsStopSalary:function(e){return a.get("QueryEmployeeOtherConditionsStopSalary",e)},getEmployeeOtherConditionsStopSalary:function(e){return a.get("GetEmployeeOtherConditionsStopSalary",e)},updateEmployeeOtherConditionsStopSalary:function(e){return a.post("UpdateEmployeeOtherConditionsStopSalary",e)},addEmployeeOtherConditionsStopSalary:function(e){return a.post("AddEmployeeOtherConditionsStopSalary",e)},deleteEmployeeOtherConditionsStopSalary:function(e){return a.post("DeleteEmployeeOtherConditionsStopSalary",e)},autoImportEmployeeOtherConditionsStopSalary:function(e){return a.post("AutoImportEmployeeOtherConditionsStopSalary",e)},queryEmployeeOverseasReturnReissue:function(e){return a.get("QueryEmployeeOverseasReturnReissue",e)},getEmployeeOverseasReturnReissue:function(e){return a.get("GetEmployeeOverseasReturnReissue",e)},queryEmployeeOverseasReissueDetail:function(e){return a.get("QueryEmployeeOverseasReissueDetail",e)},updateEmployeeOverseasReturnReissue:function(e){return a.post("UpdateEmployeeOverseasReturnReissue",e)},addEmployeeOverseasReturnReissue:function(e){return a.post("AddEmployeeOverseasReturnReissue",e)},deleteEmployeeOverseasReturnReissue:function(e){return a.post("DeleteEmployeeOverseasReturnReissue",e)},autoImportEmployeeOverseasReturnReissue:function(e){return a.post("AutoImportEmployeeOverseasReturnReissue",e)},importEmployeeOverseasReissueDetail:function(e,t,o,n){return t.append("file",e),t.append("salaryId",o),t.append("employeeId",n),a.postForm("ImportEmployeeOverseasReissueDetail",t)},queryEmployeeOverseasMonthlyDeduction:function(e){return a.get("QueryEmployeeOverseasMonthlyDeduction",e)},getEmployeeOverseasMonthlyDeduction:function(e){return a.get("GetEmployeeOverseasMonthlyDeduction",e)},updateEmployeeOverseasMonthlyDeduction:function(e){return a.post("UpdateEmployeeOverseasMonthlyDeduction",e)},addEmployeeOverseasMonthlyDeduction:function(e){return a.post("AddEmployeeOverseasMonthlyDeduction",e)},deleteEmployeeOverseasMonthlyDeduction:function(e){return a.post("DeleteEmployeeOverseasMonthlyDeduction",e)},autoImportEmployeeOverseasMonthlyDeduction:function(e){return a.post("AutoImportEmployeeOverseasMonthlyDeduction",e)},queryEmployeeOverseasExcessDeduction:function(e){return a.get("QueryEmployeeOverseasExcessDeduction",e)},getEmployeeOverseasExcessDeduction:function(e){return a.get("GetEmployeeOverseasExcessDeduction",e)},updateEmployeeOverseasExcessDeduction:function(e){return a.post("UpdateEmployeeOverseasExcessDeduction",e)},addEmployeeOverseasExcessDeduction:function(e){return a.post("AddEmployeeOverseasExcessDeduction",e)},deleteEmployeeOverseasExcessDeduction:function(e){return a.post("DeleteEmployeeOverseasExcessDeduction",e)},queryEmployeeOverseasSuspensionSalary:function(e){return a.get("QueryEmployeeOverseasSuspensionSalary",e)},getEmployeeOverseasSuspensionSalary:function(e){return a.get("GetEmployeeOverseasSuspensionSalary",e)},updateEmployeeOverseasSuspensionSalary:function(e){return a.post("UpdateEmployeeOverseasSuspensionSalary",e)},addEmployeeOverseasSuspensionSalary:function(e){return a.post("AddEmployeeOverseasSuspensionSalary",e)},deleteEmployeeOverseasSuspensionSalary:function(e){return a.post("DeleteEmployeeOverseasSuspensionSalary",e)},autoImportEmployeeOverseasSuspensionSalary:function(e){return a.post("AutoImportEmployeeOverseasSuspensionSalary",e)},queryNewEmployee:function(e){return a.get("QueryNewEmployee",e)},getNewEmployee:function(e){return a.get("GetNewEmployee",e)},calculateNewEmployee:function(e){return a.post("CalculateNewEmployee",e)},importEmployeeSalaryBase:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportEmployeeSalaryBase",t)},exportEmployeeSalary:function(e){return a.post("ExportEmployeeSalary",{data:e,responseType:"arraybuffer"})},exportEmployeeSocialSecurityBaseCorrection:function(e){return a.post("ExportEmployeeSocialSecurityBaseCorrection",{data:e,responseType:"arraybuffer"})},updateNewEmployee:function(e){return a.post("UpdateNewEmployee",e)},addNewEmployee:function(e){return a.post("AddNewEmployee",e)},deleteNewEmployee:function(e){return a.post("DeleteNewEmployee",e)},autoImportNewEmployee:function(e){return a.get("AutoImportNewEmployee",e)},queryEmployeeOnlyChildFeeAdjustment:function(e){return a.get("QueryEmployeeOnlyChildFeeAdjustment",e)},getEmployeeOnlyChildFeeAdjustment:function(e){return a.get("GetEmployeeOnlyChildFeeAdjustment",e)},updateEmployeeOnlyChildFeeAdjustment:function(e){return a.post("UpdateEmployeeOnlyChildFeeAdjustment",e)},addEmployeeOnlyChildFeeAdjustment:function(e){return a.post("AddEmployeeOnlyChildFeeAdjustment",e)},deleteEmployeeOnlyChildFeeAdjustment:function(e){return a.post("DeleteEmployeeOnlyChildFeeAdjustment",e)},queryEmployeeOccupationalAnnuityBackDeduction:function(e){return a.get("QueryEmployeeOccupationalAnnuityBackDeduction",e)},getEmployeeOccupationalAnnuityBackDeduction:function(e){return a.get("GetEmployeeOccupationalAnnuityBackDeduction",e)},calculateEmployeeOccupationalAnnuityBackDeduction:function(e){return a.post("CalculateEmployeeOccupationalAnnuityBackDeduction",e)},updateEmployeeOccupationalAnnuityBackDeduction:function(e){return a.post("UpdateEmployeeOccupationalAnnuityBackDeduction",e)},addEmployeeOccupationalAnnuityBackDeduction:function(e){return a.post("AddEmployeeOccupationalAnnuityBackDeduction",e)},deleteEmployeeOccupationalAnnuityBackDeduction:function(e){return a.post("DeleteEmployeeOccupationalAnnuityBackDeduction",e)},queryEmployeePaymentBackPayDeduction:function(e){return a.get("QueryEmployeePaymentBackPayDeduction",e)},getEmployeePaymentBackPayDeduction:function(e){return a.get("GetEmployeePaymentBackPayDeduction",e)},calculateEmployeePaymentBackPayDeduction:function(e){return a.post("CalculateEmployeePaymentBackPayDeduction",e)},updateEmployeePaymentBackPayDeduction:function(e){return a.post("UpdateEmployeePaymentBackPayDeduction",e)},addEmployeePaymentBackPayDeduction:function(e){return a.post("AddEmployeePaymentBackPayDeduction",e)},deleteEmployeePaymentBackPayDeduction:function(e){return a.post("DeleteEmployeePaymentBackPayDeduction",e)},queryEmployeeSocialSecurityBaseCorrection:function(e){return a.get("QueryEmployeeSocialSecurityBaseCorrection",e)},getEmployeeSocialSecurityBaseCorrection:function(e){return a.get("GetEmployeeSocialSecurityBaseCorrection",e)},calculateEmployeeSocialSecurityBaseCorrection:function(e){return a.post("CalculateEmployeeSocialSecurityBaseCorrection",e)},updateEmployeeSocialSecurityBaseCorrection:function(e){return a.post("UpdateEmployeeSocialSecurityBaseCorrection",e)},addEmployeeSocialSecurityBaseCorrection:function(e){return a.post("AddEmployeeSocialSecurityBaseCorrection",e)},deleteEmployeeSocialSecurityBaseCorrection:function(e){return a.post("DeleteEmployeeSocialSecurityBaseCorrection",e)},queryAdministrativeAppointment:function(e){return a.get("QueryAdministrativeAppointment",e)},getAdministrativeAppointment:function(e){return a.get("GetAdministrativeAppointment",e)},calculateAdministrativeAppointment:function(e){return a.post("CalculateAdministrativeAppointment",e)},updateAdministrativeAppointment:function(e){return a.post("UpdateAdministrativeAppointment",e)},addAdministrativeAppointment:function(e){return a.post("AddAdministrativeAppointment",e)},deleteAdministrativeAppointment:function(e){return a.post("DeleteAdministrativeAppointment",e)},queryEmployeeSalaryCorrectionMonth:function(e){return a.get("QueryEmployeeSalaryCorrectionMonth",e)},getEmployeeSalaryCorrectionMonth:function(e){return a.get("GetEmployeeSalaryCorrectionMonth",e)},calculateEmployeeSalaryCorrectionMonth:function(e){return a.post("CalculateEmployeeSalaryCorrectionMonth",e)},updateEmployeeSalaryCorrectionMonth:function(e){return a.post("UpdateEmployeeSalaryCorrectionMonth",e)},addEmployeeSalaryCorrectionMonth:function(e){return a.post("AddEmployeeSalaryCorrectionMonth",e)},deleteEmployeeSalaryCorrectionMonth:function(e){return a.post("DeleteEmployeeSalaryCorrectionMonth",e)},importEmployeeSalaryCorrectionMonth:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportEmployeeSalaryCorrectionMonth",t)},queryPostdoctoralSalaryCorrectionYear:function(e){return a.get("QueryPostdoctoralSalaryCorrectionYear",e)},getPostdoctoralSalaryCorrectionYear:function(e){return a.get("GetPostdoctoralSalaryCorrectionYear",e)},calculatePostdoctoralSalaryCorrectionYear:function(e){return a.post("CalculatePostdoctoralSalaryCorrectionYear",e)},updatePostdoctoralSalaryCorrectionYear:function(e){return a.post("UpdatePostdoctoralSalaryCorrectionYear",e)},addPostdoctoralSalaryCorrectionYear:function(e){return a.post("AddPostdoctoralSalaryCorrectionYear",e)},deletePostdoctoralSalaryCorrectionYear:function(e){return a.post("DeletePostdoctoralSalaryCorrectionYear",e)},queryShiftAllowance:function(e){return a.get("QueryShiftAllowance",e)},getShiftAllowance:function(e){return a.get("GetShiftAllowance",e)},calculateShiftAllowance:function(e){return a.post("CalculateShiftAllowance",e)},updateShiftAllowance:function(e){return a.post("UpdateShiftAllowance",e)},addShiftAllowance:function(e){return a.post("AddShiftAllowance",e)},deleteShiftAllowance:function(e){return a.post("DeleteShiftAllowance",e)},autoImportShiftAllowance:function(e){return a.post("AutoImportShiftAllowance",e)},getGeneralHospitalAdminDutyAllowance:function(e){return a.get("GetGeneralHospitalAdminDutyAllowance",e)},queryGeneralHospitalAdminDutyAllowance:function(e){return a.get("QueryGeneralHospitalAdminDutyAllowance",e)},updateGeneralHospitalAdminDutyAllowance:function(e){return a.post("UpdateGeneralHospitalAdminDutyAllowance",e)},addGeneralHospitalAdminDutyAllowance:function(e){return a.post("AddGeneralHospitalAdminDutyAllowance",e)},deleteGeneralHospitalAdminDutyAllowance:function(e){return a.post("DeleteGeneralHospitalAdminDutyAllowance",e)},importGeneralHospitalAdminDutyAllowance:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportGeneralHospitalAdminDutyAllowance",t)},importShiftAllowance:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportShiftAllowance",t)},autoImportAdminDutyAllowance:function(e){return a.post("AutoImportAdminDutyAllowance",e)},queryMiddleNightShiftAllowance:function(e){return a.get("QueryMiddleNightShiftAllowance",e)},getMiddleNightShiftAllowance:function(e){return a.get("GetMiddleNightShiftAllowance",e)},getMiddleNightShiftAllowanceParam:function(){return a.get("GetMiddleNightShiftAllowanceParam")},updateMiddleNightShiftAllowance:function(e){return a.post("UpdateMiddleNightShiftAllowance",e)},addMiddleNightShiftAllowance:function(e){return a.post("AddMiddleNightShiftAllowance",e)},deleteMiddleNightShiftAllowance:function(e){return a.post("DeleteMiddleNightShiftAllowance",e)},importMiddleNightShiftAllowance:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportMiddleNightShiftAllowance",t)},autoMiddleNightShiftAllowance:function(e){return a.post("AutoMiddleNightShiftAllowance",e)},queryAttendanceHealthAllowance:function(e){return a.get("QueryAttendanceHealthAllowance",e)},getAttendanceHealthAllowance:function(e){return a.get("GetAttendanceHealthAllowance",e)},updateAttendanceHealthAllowance:function(e){return a.post("UpdateAttendanceHealthAllowance",e)},addAttendanceHealthAllowance:function(e){return a.post("AddAttendanceHealthAllowance",e)},deleteAttendanceHealthAllowance:function(e){return a.post("DeleteAttendanceHealthAllowance",e)},exportAttendanceHealthAllowance:function(e){return a.post("ExportAttendanceHealthAllowance",{data:e,responseType:"arraybuffer"})},importAttendanceHealthAllowance:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportAttendanceHealthAllowance",t)},autoImportAttendanceHealthAllowance:function(e){return a.post("AutoImportAttendanceHealthAllowance",e)},autoImportYearSocialSecurityBaseCorrection:function(e){return a.post("AutoImportYearSocialSecurityBaseCorrection",e)},queryOvertimeAllowance:function(e){return a.get("QueryOvertimeAllowance",e)},getOvertimeAllowance:function(e){return a.get("GetOvertimeAllowance",e)},updateOvertimeAllowance:function(e){return a.post("UpdateOvertimeAllowance",e)},addOvertimeAllowance:function(e){return a.post("AddOvertimeAllowance",e)},deleteOvertimeAllowance:function(e){return a.post("DeleteOvertimeAllowance",e)},importOvertimeAllowance:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportOvertimeAllowance",t)},autoImportOvertimeAllowance:function(e){return a.post("AutoImportOvertimeAllowance",e)},calculateOvertimeAllowance:function(e){return a.post("CalculateOvertimeAllowance",e)},queryAssistanceForeign:function(e){return a.get("QueryAssistanceForeign",e)},getAssistanceForeign:function(e){return a.get("GetAssistanceForeign",e)},updateAssistanceForeign:function(e){return a.post("UpdateAssistanceForeign",e)},addAssistanceForeign:function(e){return a.post("AddAssistanceForeign",e)},deleteAssistanceForeign:function(e){return a.post("DeleteAssistanceForeign",e)},importAssistanceForeign:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportAssistanceForeign",t)},autoImportAssistanceForeign:function(e){return a.post("AutoImportAssistanceForeign",e)},queryPostdoctoralHousingAllowance:function(e){return a.get("QueryPostdoctoralHousingAllowance",e)},getPostdoctoralHousingAllowance:function(e){return a.get("GetPostdoctoralHousingAllowance",e)},calculatePostdoctoralHousingAllowance:function(e){return a.post("CalculatePostdoctoralHousingAllowance",e)},updatePostdoctoralHousingAllowance:function(e){return a.post("UpdatePostdoctoralHousingAllowance",e)},addPostdoctoralHousingAllowance:function(e){return a.post("AddPostdoctoralHousingAllowance",e)},deletePostdoctoralHousingAllowance:function(e){return a.post("DeletePostdoctoralHousingAllowance",e)},importPostdoctoralHousingAllowance:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportPostdoctoralHousingAllowance",t)},autoImportPostdoctoralHousingAllowance:function(e){return a.get("AutoImportPostdoctoralHousingAllowance",e)},querySalaryExtendedDetails:function(e){return a.get("QuerySalaryExtendedDetails",e)},getSalaryExtendedColumn:function(e){return a.get("GetSalaryExtendedColumn",e)},getSalaryExtendedDetails:function(e){return a.get("GetSalaryExtendedDetails",e)},updateSalaryExtendedDetails:function(e){return a.post("UpdateSalaryExtendedDetails",e)},addSalaryExtendedDetails:function(e){return a.post("AddSalaryExtendedDetails",e)},deleteSalaryExtendedDetails:function(e){return a.post("DeleteSalaryExtendedDetails",e)},importSalaryExtendedDetails:function(e,t,o,n){return t.append("file",e),t.append("salaryId",o),t.append("salaryType",n),a.postForm("ImportSalaryExtendedDetails",t)},querySalaryDetailGroup:function(e){return a.get("QuerySalaryDetailGroup",e)},getSalaryDetailGroup:function(e){return a.get("GetSalaryDetailGroup",e)},updateSalaryDetailGroup:function(e){return a.post("UpdateSalaryDetailGroup",e)},addSalaryExtendedColumn:function(e){return a.post("AddSalaryExtendedColumn",e)},deleteSalaryDetailGroup:function(e){return a.post("DeleteSalaryDetailGroup",e)},getEmployeeBasePay:function(e){return a.get("GetEmployeeBasePay",e)},querySalaryLeave:function(e){return a.get("QuerySalaryLeave",e)},getSalaryLeave:function(e){return a.get("GetSalaryLeave",e)},updateSalaryLeave:function(e){return a.post("UpdateSalaryLeave",e)},addSalaryLeave:function(e){return a.post("AddSalaryLeave",e)},deleteSalaryLeave:function(e){return a.post("DeleteSalaryLeave",e)},getSalaryLeaveParam:function(e){return a.get("GetSalaryLeaveParam",e)},calculateSalaryLeave:function(e){return a.post("CalculateSalaryLeave",e)},autoImportSalaryLeave:function(e){return a.post("AutoImportSalaryLeave",e)},importSalaryLeave:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportSalaryLeave",t)},queryNonMonthlySalaryLeave:function(e){return a.get("QueryNonMonthlySalaryLeave",e)},getNonMonthlySalaryLeave:function(e){return a.get("GetNonMonthlySalaryLeave",e)},updateNonMonthlySalaryLeave:function(e){return a.post("UpdateNonMonthlySalaryLeave",e)},addNonMonthlySalaryLeave:function(e){return a.post("AddNonMonthlySalaryLeave",e)},deleteNonMonthlySalaryLeave:function(e){return a.post("DeleteNonMonthlySalaryLeave",e)},importNonMonthlySalaryLeave:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportNonMonthlySalaryLeave",t)},querySporadicSalaryLeave:function(e){return a.get("QuerySporadicSalaryLeave",e)},getSporadicSalaryLeave:function(e){return a.get("GetSporadicSalaryLeave",e)},updateSporadicSalaryLeave:function(e){return a.post("UpdateSporadicSalaryLeave",e)},addSporadicSalaryLeave:function(e){return a.post("AddSporadicSalaryLeave",e)},deleteSporadicSalaryLeave:function(e){return a.post("DeleteSporadicSalaryLeave",e)},importSporadicSalaryLeave:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportSporadicSalaryLeave",t)},queryRetiredEmployeeReissue:function(e){return a.get("QueryRetiredEmployeeReissue",e)},getRetiredEmployeeReissue:function(e){return a.get("GetRetiredEmployeeReissue",e)},updateRetiredEmployeeReissue:function(e){return a.post("UpdateRetiredEmployeeReissue",e)},addRetiredEmployeeReissue:function(e){return a.post("AddRetiredEmployeeReissue",e)},deleteRetiredEmployeeReissue:function(e){return a.post("DeleteRetiredEmployeeReissue",e)},importRetiredEmployeeReissue:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportRetiredEmployeeReissue",t)},getEmployeeSalaryDetail:function(e){return a.get("GetEmployeeSalaryDetail",e)},querySalaryDataAdjust:function(e){return a.get("QuerySalaryDataAdjust",e)},getSalaryDataAdjust:function(e){return a.get("GetSalaryDataAdjust",e)},updateSalaryDataAdjust:function(e){return a.post("UpdateSalaryDataAdjust",e)},addSalaryDataAdjust:function(e){return a.post("AddSalaryDataAdjust",e)},deleteSalaryDataAdjust:function(e){return a.post("DeleteSalaryDataAdjust",e)},importSalaryDataAdjust:function(e,t,o){return t.append("file",e),t.append("salaryId",o),a.postForm("ImportSalaryDataAdjust",t)},queryEmployeeAnnualSocialSecurityBaseCorrection:function(e){return a.get("QueryEmployeeAnnualSocialSecurityBaseCorrection",e)},getEmployeeAnnualSocialSecurityBaseCorrection:function(e){return a.get("GetEmployeeAnnualSocialSecurityBaseCorrection",e)},calculateEmployeeAnnualSocialSecurityBaseCorrection:function(e){return a.post("CalculateEmployeeAnnualSocialSecurityBaseCorrection",e)},updateEmployeeAnnualSocialSecurityBaseCorrection:function(e){return a.post("UpdateEmployeeAnnualSocialSecurityBaseCorrection",e)},addEmployeeAnnualSocialSecurityBaseCorrection:function(e){return a.post("AddEmployeeAnnualSocialSecurityBaseCorrection",e)},deleteEmployeeAnnualSocialSecurityBaseCorrection:function(e){return a.post("DeleteEmployeeAnnualSocialSecurityBaseCorrection",e)},exportEmployeeAnnualSocialSecurityBaseCorrection:function(e){return a.post("ExportEmployeeAnnualSocialSecurityBaseCorrection",{data:e,responseType:"arraybuffer"})},queryThirteenthSalary:function(e){return a.get("QueryThirteenthSalary",e)},getThirteenthSalary:function(e){return a.get("GetThirteenthSalary",e)},addThirteenthSalary:function(e){return a.post("AddThirteenthSalary",e)},updateThirteenthSalary:function(e){return a.post("UpdateThirteenthSalary",e)},deleteThirteenthSalary:function(e){return a.post("DeleteThirteenthSalary",e)},autoImportThirteenthSalary:function(e){return a.post("AutoImportThirteenthSalary",e)},calculateThirteenthSalary:function(e){return a.post("CalculateThirteenthSalary",e)},exportThirteenthFinanceData:function(e){return a.post("ExportThirteenthFinanceData",{data:e,responseType:"arraybuffer"})},queryMinimumWageSubsidy:function(e){return a.get("QueryMinimumWageSubsidy",e)},getMinimumWageSubsidy:function(e){return a.get("GetMinimumWageSubsidy",e)},addMinimumWageSubsidy:function(e){return a.post("AddMinimumWageSubsidy",e)},updateMinimumWageSubsidy:function(e){return a.post("UpdateMinimumWageSubsidy",e)},deleteMinimumWageSubsidy:function(e){return a.post("DeleteMinimumWageSubsidy",e)},autoImportMinimumWageSubsidy:function(e){return a.get("AutoImportMinimumWageSubsidy",e)}}}}]);