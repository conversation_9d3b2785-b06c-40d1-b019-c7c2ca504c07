<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="90%" :top="'10vh'" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="100px" class="el-dialogform">
        <el-card style="margin-top: 5px;">
          <el-row>
            <el-col :span="6">
              <el-form-item label="员工姓名" label-width="120px">
                <div>
                  <span>
                    {{ dataModel.employeeModel.empName }}
                  </span>
                  <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="唯一码" label-width="120px">
                {{ dataModel.employeeModel.empUid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工号" label-width="120px">
                {{ dataModel.employeeModel.empCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="部门" label-width="120px">
                {{ dataModel.employeeModel.empDept }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="职别" label-width="120px">
                {{ dataModel.employeeModel.officialRankName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别" label-width="120px">
                {{ dataModel.employeeModel.genderDesc }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="在职方式" label-width="120px">
                {{ dataModel.employeeModel.hireStyleName }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="报到日期" label-width="120px">
                {{ dataModel.employeeModel.effHireDate ? (new Date(dataModel.employeeModel.effHireDate)).Format('yyyy-MM-dd') : '' }}
              </el-form-item>
            </el-col>

          </el-row>
        </el-card>
        <el-card style="margin-top: 5px;">
          <div slot="header">
            <span>博士后数据</span>
          </div>
          <el-row>
            <el-col :span="8">
              <el-row>
                <el-form-item label="中选项目" prop="successfulProject" label-width="120px">
                  <el-input 
                    v-model="dataModel.successfulProject" 
                    placeholder="" 
                    style="width: 60%" 
                    maxlength="300" 
                    clearable
                  />
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="补发/补扣类型" label-width="120px" prop="enumPaymentType">
                  <el-radio-group v-model="dataModel.enumPaymentType">
                    <el-radio :label="2">补扣</el-radio>
                    <el-radio :label="1">补发</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item :label="monthRangeLabel" label-width="120px" prop="monthRange">
                  <el-date-picker
                    v-model="dataModel.monthRange"
                    style="width: 60%;"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                    :picker-options="pickerOptions"
                    format="yyyy 年 MM 月"
                    value-format="yyyy-MM-dd"
                    @change="monthRangeChange"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item :label="monthCountLabel" label-width="120px" prop="backPayMonth">
                  <el-input-number v-model="dataModel.backPayMonth"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item :label="calculatedAmountLabel" prop="backPayDeduction" label-width="120px">
                  {{ dataModel.backPayDeduction | formatMoney2 }}
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item :label="actualAmountLabel" prop="actualBackPayDeduction" label-width="120px">
                  <el-input-number v-model="dataModel.actualBackPayDeduction"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="计算会费金额" label-width="120px">
                  {{ dataModel.calculatedMembershipFee | formatMoney2 }}
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="实际会费金额" label-width="120px">
                  <el-input-number v-model.number="dataModel.actualMembershipFee"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" :strictly="true" />
                </el-form-item>
              </el-row>
            </el-col>
            <el-col :span="16">
              <el-form label-width="120px" style="width: 90%; margin: 0 auto;border: 1px;">
                <div class="table-row">
                  <span class="table-header">类型</span>
                  <span class="table-header">原数据</span>
                  <span class="table-header">新数据</span>
                </div>
                <div class="table-row">
                  <span class="table-data">年薪</span>
                  <span class="table-data">
                    <div style="display: flex; align-items: center;">
                      <el-input-number v-model="dataModel.oldYearlySalary"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" @change="calculateOldMonthlySalary" />
                      <span style="margin-left: 5px;">万元</span>
                    </div>
                  </span>
                  <span class="table-data">
                    <div style="display: flex; align-items: center;">
                      <el-input-number v-model="dataModel.newYearlySalary"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" @change="calculateNewMonthlySalary" />
                      <span style="margin-left: 5px;">万元</span>
                    </div>
                  </span>
                </div>
                <div class="table-row">
                  <span class="table-data">月薪</span>
                  <span class="table-data">
                    <div style="display: flex; align-items: center;">
                      <el-input-number v-model="dataModel.oldMonthSalary"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                      <span style="margin-left: 5px;"></span>
                    </div>
                  </span>
                  <span class="table-data">
                    <div style="display: flex; align-items: center;">
                      <el-input-number v-model="dataModel.newMonthSalary"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" />
                      <span style="margin-left: 5px;"></span>
                    </div>
                  </span>
                </div>
                <div class="table-row">
                  <span class="table-data">会费</span>
                  <span class="table-data">
                    <div style="display: flex; align-items: center;">
                      <el-input-number v-model="dataModel.oldMembershipFee"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" @change="calculateMembershipFee" />
                      <span style="margin-left: 5px;"></span>
                    </div>
                  </span>
                  <span class="table-data">
                    <div style="display: flex; align-items: center;">
                      <el-input-number v-model="dataModel.newMembershipFee"  :min="0" :precision="2" :controls="false" placeholder="" style="width: 60%" maxlength="5" @change="calculateMembershipFee" />
                      <span style="margin-left: 5px;"></span>
                    </div>
                  </span>
                </div>
              </el-form>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" label-width="120px">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="300" placeholder="备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="calculate">计 算</el-button>
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>

    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />

  </div>
</template>
<script>
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'

export default {
  components: {
    selectUserComponent
  },
  data() {
    return {
      salaryId: '',
      showDialog: false,
      title: '',
      btnSaveLoading: false,
      isEdit: false,
      stationTrees: [],
      dataModel: {
        employeeModel: {}
      },
      housingAllowance: 2500, // 博士后房贴默认值
      rules: {
        // newYearlySalary: [
        //   { required: true, message: '调整后年薪必填', trigger: 'blur' },
        //   { validator: validateMoney, trigger: 'blur' }
        // ],
        backPayMonth: [
          { required: true, message: '补扣/补发月份数必填', trigger: 'blur' }
          // { validator: validateMoney, trigger: 'blur' }
        ],
        newMonthSalary: [
          { required: true, message: '新月薪必填', trigger: 'blur' }
        ],
        oldMonthSalary: [
          { required: true, message: '原月薪必填', trigger: 'blur' }
        ],
        // backPayDeduction: [
        //   { required: true, message: '补发/补扣必填', trigger: 'blur' },
        //   { validator: validateMoney, trigger: 'blur' }
        // ],
      },
      pickerOptions: {
        disabledDate: time => {
          const val = new Date(this.salaryMonth)
          return time.getTime() > val
        }
      }
    }
  },
  computed: {
    paymentTypeText() {
      return this.dataModel.enumPaymentType === 1 ? '补发' : '补扣'
    },
    // 动态起始年月标签
    monthRangeLabel() {
      return `起始${this.paymentTypeText}年月`
    },
    // 动态月份数标签
    monthCountLabel() {
      return `${this.paymentTypeText}月份数`
    },
    // 动态计算金额标签
    calculatedAmountLabel() {
      return `计算${this.paymentTypeText}`
    },
    // 动态实际金额标签
    actualAmountLabel() {
      return `实际${this.paymentTypeText}`
    }
  },
  methods: {
    initDialog(row) {
      if (!row) {
        this.title = '新增博士后修正(年薪)'
        this.isEdit = false
      } else {
        this.title = '编辑博士后修正(年薪)'
        this.isEdit = true
        this.getData(row.id)
      }
      this.queryStationTree()
      this.getSalaryDataCodes() // 获取博士后房贴数据
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'enumPaymentType', 1) // 默认选择补发
      this.showDialog = true
      this.getMaxDaysOfMonth(this.salaryId)
    },
    getMaxDaysOfMonth(salaryId) {
      salaryApi.getSalary({ id: salaryId }).then(res => {
        if (res.succeed) {
          const salaryData = res.data
          var recordMonth = salaryData.month

          var year = recordMonth.split('-')[0]
          var month = recordMonth.split('-')[1]
          var d = new Date(year, month, 0)
          var m = new Date(year, month - 1, 1)
          this.salaryMonth = m
          this.maxDaysOfMonth = d.getDate()
        }
      }).catch(res => {
      })
    },
    getSalaryDataCodes() {
      salaryApi.getSalaryDataCodes({ codes: ['PostdoctoralHousingAllowance'] }).then(res => {
        if (res.succeed) {
          const housingAllowanceData = res.data.find(item => item.code === 'PostdoctoralHousingAllowance')
          if (housingAllowanceData && housingAllowanceData.value) {
            this.housingAllowance = parseFloat(housingAllowanceData.value) || 2500
          }
        }
      }).catch(res => {
        // 如果获取失败，使用默认值2500
        console.log('获取博士后房贴数据失败，使用默认值2500')
      })
    },
    getData(id) {
      salaryApi.getPostdoctoralSalaryCorrectionYear({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setEmployeeModel(res.data.employee)
        }
      }).catch(res => {
      })
    },
    saveDialog() {
      if (this.dataModel.actualBackPayDeduction === undefined || this.dataModel.actualBackPayDeduction === 0) {
         this.$message.error('实际金额必须大于等于0')
         return
      }
      if (parseFloat(this.dataModel.backPayDeduction) !== parseFloat(this.dataModel.actualBackPayDeduction)) {
        if (this.dataModel.remark === '' || this.dataModel.remark === undefined) {
           this.$message.error('计算金额与实际金额不一致，请备注原因后再提交')
           return
        }
      }
      // 验证会费金额
      if (this.dataModel.actualMembershipFee !== undefined && this.dataModel.actualMembershipFee !== null) {
        if (parseFloat(this.dataModel.calculatedMembershipFee || 0) !== parseFloat(this.dataModel.actualMembershipFee)) {
          if (this.dataModel.remark === '' || this.dataModel.remark === undefined) {
             this.$message.error('计算会费金额与实际会费金额不一致，请备注原因后再提交')
             return
          }
        }
      }
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addPostdoctoralSalaryCorrectionYear(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updatePostdoctoralSalaryCorrectionYear(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      this.dataModel = { employeeModel: {}}
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      // 年薪
      this.$refs.selectEmployee.listQuery.salaryType = 2
      this.$refs.selectEmployee.showEmployee = true
    },
    setEmployee(emp) {
      this.dataModel = { employeeModel: {}}
      this.$set(this.dataModel, 'enumPaymentType', 1)
      this.calculatePostdoctoralSalaryCorrectionYear(emp)
    },
    setEmployeeModel(emp) {
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          deadDate: emp.employeeHR.deadDate,
          effHireDate: emp.employeeHR.effHireDate,
          officialRankName: emp.employeeHR.officialRankName
        })
    },
    calculatePostdoctoralSalaryCorrectionYear(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      salaryApi.calculatePostdoctoralSalaryCorrectionYear(this.dataModel).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setInitialMonth()
          this.setEmployeeModel(emp)
        }
      }).catch(res => {
        console.log(res)
      })
    },
    // 设置初始月份
    setInitialMonth() {
      // 获取当前薪资月的前一个月作为起始月份
      const currentMonth = new Date(this.salaryMonth)
      const prevMonth = new Date(currentMonth)
      prevMonth.setMonth(prevMonth.getMonth() - 1)

      // 设置月份范围为前一个月到当前月
      this.$set(this.dataModel, 'monthRange', [prevMonth, this.salaryMonth])
      this.dataModel.startTime = prevMonth
      this.dataModel.endTime = this.salaryMonth

      // 计算月份差值
      const getMonths = (date) => date.getFullYear() * 12 + date.getMonth()
      const monthDiff = getMonths(new Date(this.salaryMonth)) - getMonths(new Date(prevMonth))
      this.$set(this.dataModel, 'backPayMonth', monthDiff)

      // 计算补发/补扣金额
      this.changeBackPayMonth()
    },
    // 月薪
    changeMonthSalary() {
      const newStationWage = parseFloat(this.dataModel.newStationWage) || 0
      this.$set(this.dataModel, 'newMonthSalary', newStationWage.toFixed(2))

      const oldStationWage = parseFloat(this.dataModel.oldStationWage) || 0
      this.$set(this.dataModel, 'oldMonthSalary', oldStationWage.toFixed(2))
    },
    // 新社保基数
    changeNewSocialSecurityBase() {
      const newSocialSecurityBase = parseFloat(this.dataModel.socialSecurityBase) || 0

      const pensionInsurancePercentage = parseFloat(this.dataModel.pensionInsurancePercentage) || 0
      const pensionInsuranceValue = Math.ceil((newSocialSecurityBase * (pensionInsurancePercentage / 100)) * 10) / 10
      this.$set(this.dataModel, 'pensionInsurance', pensionInsuranceValue.toFixed(2))

      const medicalInsurancePercentage = parseFloat(this.dataModel.medicalInsurancePercentage) || 0
      const medicalInsurancePercentageValue = Math.ceil((newSocialSecurityBase * (medicalInsurancePercentage / 100)) * 10) / 10
      this.$set(this.dataModel, 'medicalInsurance', medicalInsurancePercentageValue.toFixed(2))

      const unemploymentInsurancePercentage = parseFloat(this.dataModel.unemploymentInsurancePercentage) || 0
      const unemploymentInsurancePercentageValue = Math.ceil((newSocialSecurityBase * (unemploymentInsurancePercentage / 100)) * 10) / 10
      this.$set(this.dataModel, 'unemploymentInsurance', unemploymentInsurancePercentageValue.toFixed(2))
    },
    // 公积金基数
    changeNewHousingFundBase() {
      const newHousingFundBase = parseFloat(this.dataModel.housingFundBase) || 0

      const housingFundPercentage = parseFloat(this.dataModel.housingFundPercentage) || 0
      const housingFundPercentageValue = Math.round(newHousingFundBase * (housingFundPercentage / 100))
      this.$set(this.dataModel, 'housingFund', housingFundPercentageValue.toFixed(2))

      const supplementaryHousingFundPercentage = parseFloat(this.dataModel.supplementaryHousingFundPercentage) || 0
      const supplementaryHousingFundPercentageValue = Math.round(newHousingFundBase * (supplementaryHousingFundPercentage / 100))
      this.$set(this.dataModel, 'supplementaryHousingFund', supplementaryHousingFundPercentageValue.toFixed(2))
    },
    /*
      // 岗资基数
      changeNewStationWage() {
        const newStationWage = parseFloat(this.dataModel.newStationWage) || 0
        const salaryScaleWage = parseFloat(this.dataModel.salaryScaleWage) || 0

        const postdoctoralMembershipFeePercentage = parseFloat(this.dataModel.postdoctoralMembershipFeePercentage) || 0
        this.$set(this.dataModel, 'membershipDues', ((newStationWage + salaryScaleWage) * (postdoctoralMembershipFeePercentage / 100)).toFixed(2))
      },
    */
    // 计算补发/补扣
    // changeBackPay() {
    //  const newStationWage = parseFloat(this.dataModel.newStationWage) || 0
    //  const salaryScaleWage = parseFloat(this.dataModel.salaryScaleWage) || 0
//
    //  const pensionInsurance = parseFloat(this.dataModel.backDeductionPensionInsurance) || 0
    //  const medicalInsurance = parseFloat(this.dataModel.backDeductionMedicalInsurance) || 0
    //  const unemploymentInsurance = parseFloat(this.dataModel.backDeductionUnemploymentInsurance) || 0
    //  const housingFund = parseFloat(this.dataModel.backDeductionHousingFund) || 0
    //  const supplementaryHousingFund = parseFloat(this.dataModel.backDeductionSupplementaryHousingFund) || 0
    //  const membershipFee = parseFloat(this.dataModel.backDeductionMembershipFee) || 0
//
    //  const backPayDeduction = parseFloat(newStationWage - salaryScaleWage - pensionInsurance - medicalInsurance - unemploymentInsurance - housingFund - supplementaryHousingFund - membershipFee)
    //  this.$set(this.dataModel, 'backPayDeduction', backPayDeduction)
    // },
    // // 计算保险补扣
    // changeBackPayInsurance() {
    //  const backPayMonth = parseFloat(this.dataModel.backPayMonth) || 0
//
    //  const insurances = [
    //    // 养老保险
    //    { key: 'pensionInsurance', originalKey: 'originalPensionInsurance', currentKey: 'pensionInsurance' },
    //    // 医疗保险
    //    { key: 'medicalInsurance', originalKey: 'originalMedicalInsurance', currentKey: 'medicalInsurance' },
    //    // 失业保险
    //    { key: 'unemploymentInsurance', originalKey: 'originalUnemploymentInsurance', currentKey: 'unemploymentInsurance' },
    //    // 公积金
    //    { key: 'housingFund', originalKey: 'originalHousingFund', currentKey: 'housingFund' },
    //    // 补充公积金
    //    { key: 'supplementaryHousingFund', originalKey: 'originalSupplementaryHousingFund', currentKey: 'supplementaryHousingFund' },
    //    // 会费
    //    { key: 'membershipFee', originalKey: 'originalMembershipFee', currentKey: 'membershipFee' }
    //  ]
    //  insurances.forEach(insurance => {
    //    const originalValue = parseFloat(this.dataModel[insurance.originalKey]) || 0
    //    const currentValue = parseFloat(this.dataModel[insurance.currentKey]) || 0
    //    const difference = currentValue - originalValue
    //    const key = `backDeduction${insurance.key.charAt(0).toUpperCase()}${insurance.key.slice(1)}`
    //    const value = (difference * backPayMonth).toFixed(2)
    //    this.$set(this.dataModel, key, value)
    //  })
    //  this.changeBackPay()
    // },
    // 新年薪
    // changeNewYearlySalary() {
      // const newYearlySalary = parseFloat(this.dataModel.newYearlySalary) || 0
      // 社保/公积金基数
      // const socialFundBase = ((newYearlySalary * 10000) / 12).toFixed(2)
      // this.$set(this.dataModel, 'housingFundBase', socialFundBase)
      // this.$set(this.dataModel, 'socialSecurityBase', socialFundBase)

      // this.changeNewSocialSecurityBase()
      // this.changeNewHousingFundBase()
    // },
    stationTreeChange() {
      this.stationTrees.forEach(parent => {
        if (parent.children != null) {
          var stationWage = parent.children.find(a => a.id === this.dataModel.newStationId)
          if (stationWage) {
            this.dataModel.newStationWage = stationWage.wage
          }
        }
      })
      if (this.dataModel.newStationId !== undefined && this.dataModel.newStationId !== null && this.dataModel.newStationId !== '' && this.dataModel.stationId === this.dataModel.newStationId) {
        this.$message({ message: '原岗位等级与新岗位等级一致', type: 'warning' })
      }
    },
    oldStationTreeChange() {
      this.stationTrees.forEach(parent => {
        if (parent.children != null) {
          var stationWage = parent.children.find(a => a.id === this.dataModel.oldStationId)
          if (stationWage) {
            this.dataModel.oldStationWage = stationWage.wage
          }
        }
      })

      var stationAllowance = this.stationAllowances.find(a => a.stationId === this.dataModel.oldStationId && a.workAge == this.societyAge)
      if (stationAllowance) {
        this.dataModel.oldStationAllowance = stationAllowance.allowance
      }
    },
    queryStationTree() {
      sysManageApi.queryStationTree()
        .then((result) => {
          this.stationTrees = result.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    // 计算差额
    calculateDifference() {
      // 岗位工资
      const newStationWage = parseFloat(this.dataModel.oldMonthSalary) || 0
      const oldStationWage = parseFloat(this.dataModel.newMonthSalary) || 0

      const difference = Math.abs(newStationWage -
        oldStationWage).toFixed(2)

      this.$set(this.dataModel, 'difference', difference)
    },
    calculate() {
      this.changeBackPayMonth()
    },
    // 根据原年薪自动计算原月薪
    calculateOldMonthlySalary() {
      const oldYearlySalary = parseFloat(this.dataModel.oldYearlySalary) || 0
      if (oldYearlySalary > 0) {
        // 计算月薪：年薪 × 10000 ÷ 12 - 房贴
        const oldMonthlySalary = ((oldYearlySalary * 10000 / 12) - this.housingAllowance).toFixed(2)
        this.$set(this.dataModel, 'oldMonthSalary', parseFloat(oldMonthlySalary))
      } else {
        this.$set(this.dataModel, 'oldMonthSalary', 0)
      }
    },
    // 根据新年薪自动计算新月薪
    calculateNewMonthlySalary() {
      const newYearlySalary = parseFloat(this.dataModel.newYearlySalary) || 0
      if (newYearlySalary > 0) {
        // 计算月薪：年薪 × 10000 ÷ 12 - 房贴
        const newMonthlySalary = ((newYearlySalary * 10000 / 12) - this.housingAllowance).toFixed(2)
        this.$set(this.dataModel, 'newMonthSalary', parseFloat(newMonthlySalary))
      } else {
        this.$set(this.dataModel, 'newMonthSalary', 0)
      }
    },
    // 补发/补扣月份数
    changeBackPayMonth() {
      this.calculateDifference()
      const backPayMonth = parseFloat(this.dataModel.backPayMonth) || 0
      const difference = parseFloat(this.dataModel.difference) || 0

      const value = Math.abs(backPayMonth * difference).toFixed(2)
      this.$set(this.dataModel, 'backPayDeduction', value)
      this.$set(this.dataModel, 'actualBackPayDeduction', value)

      // 计算会费金额
      this.calculateMembershipFee()
    },
    monthRangeChange(date) {
      if (date) {
          const [start, end] = this.dataModel.monthRange
          this.dataModel.startTime = start
          this.dataModel.endTime = end

          const getMonths = (date) => date.getFullYear() * 12 + date.getMonth()
          this.$set(this.dataModel, 'backPayMonth', getMonths(new Date(end)) - getMonths(new Date(start)))
      }
    },
    // 计算会费金额
    calculateMembershipFee() {
      const backPayMonth = parseFloat(this.dataModel.backPayMonth) || 0
      const newMembershipFee = parseFloat(this.dataModel.newMembershipFee) || 0
      const oldMembershipFee = parseFloat(this.dataModel.oldMembershipFee) || 0

      // 计算会费金额 = (新会费 - 旧会费) × 补发月份数，取绝对值
      const calculatedFee = Math.abs((newMembershipFee - oldMembershipFee) * backPayMonth).toFixed(2)

      // 将计算结果同时更新到计算会费金额和实际会费金额字段
      this.$set(this.dataModel, 'calculatedMembershipFee', calculatedFee)
      this.$set(this.dataModel, 'actualMembershipFee', calculatedFee)
    }
  }
}
</script>

<style>
</style>
