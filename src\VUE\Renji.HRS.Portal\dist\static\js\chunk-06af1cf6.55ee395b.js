(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-06af1cf6"],{"0e74":function(e,t,o){},"729a":function(e,t,o){"use strict";var r=o("0e74"),n=o.n(r);n.a},d368:function(e,t,o){"use strict";var r=o("cfe3"),n="Organization",a=new r["a"](n);t["a"]={QueryOrganizationHiddenTop:function(e){return a.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return a.get("QueryOrganization",e)},QueryDepartment:function(e){return a.get("QueryDepartment",e)},GetDepartment:function(e){return a.get("GetDepartment",e)},AddDepartment:function(e){return a.post("AddDepartment",e)},UpdateDepartment:function(e){return a.post("UpdateDepartment",e)},MoveDepartment:function(e){return a.post("MoveDepartment",e)},MergeDepartment:function(e){return a.post("MergeDepartment",e)},DeleteDepartment:function(e){return a.post("DeleteDepartment",e)},queryPosition:function(e){return a.post("QueryPosition",e)},getPosition:function(e){return a.get("GetPosition",e)},addPosition:function(e){return a.post("AddPosition",e)},updatePosition:function(e){return a.post("UpdatePosition",e)},deletePosition:function(e){return a.post("DeletePosition",e)},GetStation:function(e){return a.get("GetStation",e)},AddStation:function(e){return a.post("AddStation",e)},UpdateStation:function(e){return a.post("UpdateStation",e)},DeleteStation:function(e){return a.post("DeleteStation",e)},QueryPositionStationTree:function(e){return a.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return a.post("AllocatePosition",e)},DeletePositionStation:function(e){return a.post("DeletePositionStation",e)},queryDeptByUser:function(e){return a.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return a.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return a.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),a.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return a.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return a.get("GetStationAllowance",e)},addStationAllowance:function(e){return a.post("AddStationAllowance",e)},updateStationAllowance:function(e){return a.post("UpdateStationAllowance",e)},querySeniority:function(e){return a.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),a.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return a.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return a.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return a.get("GetSeniority",e)},addSeniority:function(e){return a.post("AddSeniority",e)},updateSeniority:function(e){return a.post("UpdateSeniority",e)},querySalaryScale:function(e){return a.get("QuerySalaryScale",e)},getSalaryScale:function(e){return a.get("GetSalaryScale",e)},addSalaryScale:function(e){return a.post("AddSalaryScale",e)},updateSalaryScale:function(e){return a.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return a.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),a.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return a.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return a.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return a.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return a.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return a.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return a.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return a.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return a.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return a.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return a.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return a.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return a.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return a.post("DeleteTelephoneFee",e)}}},e44c:function(e,t,o){"use strict";o("4160"),o("b64b"),o("159b");var r=o("cfe3"),n="HR",a=new r["a"](n);t["a"]={queryEmployee:function(e){return a.get("QueryEmployee",e)},queryEmployeeByCommonCondition:function(e){return a.post("QueryEmployeeByCommonCondition",e)},queryEmployeeStatus:function(){return a.get("QueryEmployeeStatus")},queryRank:function(){return a.get("QueryRank")},queryAdministrativePosition:function(){return a.get("queryAdministrativePosition")},queryMajorTechnical:function(){return a.get("queryMajorTechnical")},queryOfficialRank:function(){return a.get("QueryOfficialRank")},queryHireStyle:function(){return a.get("QueryHireStyle")},queryLeaveStyle:function(){return a.get("QueryLeaveStyle")},queryMarryList:function(){return a.get("QueryMarryList")},queryNationality:function(){return a.get("QueryNationality")},queryRegisterType:function(){return a.get("QueryRegisterType")},deleteEmployee:function(e){return a.post("DeleteEmployee",e)},queryDocumentType:function(){return a.get("QueryDocumentType")},addEmployee:function(e){return a.post("AddEmployee",e)},isEmpCodeExists:function(e){var t={id:e.id,empCode:e.empCode};return a.get("IsEmpCodeExists",t)},checkIdentityNumber:function(e){var t={id:e.id,identityNumber:e.identityNumber};return a.get("checkIdentityNumber",t)},getEmployee:function(e){return a.get("GetEmployee",e)},updateEmployee:function(e){return a.post("UpdateEmployee",e)},getPersonnelInformation:function(e){return a.get("GetPersonnelInformation",e)},postAddDocumentInformation:function(e){return a.get("PostAddDocumentInformation",e)},postUpdateDocumentInformation:function(e){return a.get("PostUpdateDocumentInformation",e)},postDeleteDocumentInformation:function(e){return a.get("PostDeleteDocumentInformation",e)},queryEmployeeByConditions:function(e){return a.post("QueryEmployeeByConditions",e)},queryEmployeeByCommonConditions:function(e){return a.post("QueryEmployeeByCommonConditions",e)},queryEmployeeQuerySetting:function(e){return a.get("QueryEmployeeQuerySetting",e)},querySettingColumns:function(){return a.get("QueryEmployeeInfoQuerySettingColumns")},queryOperations:function(e){return a.get("QueryOperationByColumnType",e)},addEmployeeQuerySetting:function(e){return a.post("AddEmployeeQuerySetting",e)},deleteEmployeeQuerySetting:function(e){return a.post("DeleteEmployeeQuerySetting",e)},queryDegrees:function(){return a.get("QueryDegrees")},queryEducation:function(){return a.get("QueryEducation")},QuerySocialSecurity:function(){return a.get("QuerySocialSecurity")},queryParty:function(){return a.get("QueryParty")},queryRecruitmentCategory:function(){return a.get("QueryRecruitmentCategory")},queryRecruitmentCompany:function(){return a.get("QueryRecruitmentCompany")},getEmployeeHR:function(e){return a.get("GetEmployeeHR",e)},updateEmployeeHR:function(e){return a.post("UpdateEmployeeHR",e)},updateCompanyAge:function(){return a.post("UpdateCompanyAge")},calculateGeneralHoliday:function(){return a.get("CalculateGeneralHoliday")},queryStation:function(e){return a.get("QueryStation",e)},queryPositionStation:function(e){return a.get("QueryPositionStation",e)},queryEmployeeStation:function(e){return a.get("QueryEmployeeStation",e)},addEmployeeStation:function(e){return a.post("AddEmployeeStation",e)},updateEmployeeStation:function(e){return a.post("UpdateEmployeeStation",e)},deleteEmployeeStation:function(e){return a.post("DeleteEmployeeStation",e)},queryLevel:function(){return a.get("QueryLevel")},queryEmployeeCertify:function(e){return a.get("QueryEmployeeCertify",e)},addEmployeeCertify:function(e){return a.post("AddEmployeeCertify",e)},updateEmployeeCertify:function(e){return a.post("UpdateEmployeeCertify",e)},deleteEmployeeCertify:function(e){return a.post("DeleteEmployeeCertify",e)},queryGraduation:function(){return a.get("QueryGraduation")},queryLearnWay:function(){return a.get("QueryLearnWay")},queryEmployeeEducation:function(e){return a.get("QueryEmployeeEducation",e)},addEmployeeEducation:function(e){return a.post("AddEmployeeEducation",e)},updateEmployeeEducation:function(e){return a.post("UpdateEmployeeEducation",e)},deleteEmployeeEducation:function(e){return a.post("DeleteEmployeeEducation",e)},queryEmployeeWork:function(e){return a.get("QueryEmployeeWork",e)},addEmployeeWork:function(e){return a.post("AddEmployeeWork",e)},updateEmployeeWork:function(e){return a.post("UpdateEmployeeWork",e)},deleteEmployeeWork:function(e){return a.post("DeleteEmployeeWork",e)},queryAbroadType:function(){return a.get("QueryAbroadType")},queryEmployeeAbroad:function(e){return a.get("QueryEmployeeAbroadInfo",e)},addEmployeeAbroad:function(e){return a.post("AddEmployeeAbroadInfo",e)},updateEmployeeAbroad:function(e){return a.post("UpdateEmployeeAbroadInfo",e)},deleteEmployeeAbroad:function(e){return a.post("DeleteEmployeeAbroadInfo",e)},queryContractType:function(){return a.get("QueryContractType")},queryEmployeeContract:function(e){return a.get("QueryEmployeeContract",e)},queryRenewEmployeeContract:function(e){return a.get("QueryRenewEmployeeContract",e)},updateNextTimeRemind:function(e){return a.post("UpdateNextTimeRemind",e)},sendEmailForRenewRemind:function(e){return a.post("SendEmailForRenewRemind",e)},batchUpdateNextTimeRemind:function(e){return a.post("BatchUpdateNextTimeRemind",e)},batchSendEmailForRenewRemind:function(e){return a.post("BatchSendEmailForRenewRemind",e)},addEmployeeContract:function(e){return a.post("AddEmployeeContract",e)},updateEmployeeContract:function(e){return a.post("UpdateEmployeeContract",e)},deleteEmployeeContract:function(e){return a.post("DeleteEmployeeContract",e)},queryTrainLevel:function(){return a.get("QueryTrainLevel")},queryEmployeeTrain:function(e){return a.get("QueryEmployeeTrain",e)},addEmployeeTrain:function(e){return a.post("AddEmployeeTrain",e)},updateEmployeeTrain:function(e){return a.post("UpdateEmployeeTrain",e)},deleteEmployeeTrain:function(e){return a.post("DeleteEmployeeTrain",e)},queryYearList:function(){return a.get("QueryYearList")},queryEvaluateResult:function(){return a.get("QueryEvaluateResult")},queryEmployeeAssessment:function(e){return a.get("QueryEmployeeAssessment",e)},addEmployeeAssessment:function(e){return a.post("AddEmployeeAssessment",e)},updateEmployeeAssessment:function(e){return a.post("UpdateEmployeeAssessment",e)},deleteEmployeeAssessment:function(e){return a.post("DeleteEmployeeAssessment",e)},queryEmployeeDeduct:function(e){return a.get("QueryEmployeeDeduct",e)},addEmployeeDeduct:function(e){return a.post("AddEmployeeDeduct",e)},updateEmployeeDeduct:function(e){return a.get("UpdateEmployeeDeduct",e)},deleteEmployeeDeduct:function(e){return a.post("DeleteEmployeeDeduct",e)},downloadEmployeeDeductTemplate:function(e){return a.getFile("DownlodaEmployeeDeductTemplate",e)},importEmployeeDeduct:function(e,t){var o=new FormData;return t&&Object.keys(t).forEach((function(e){return o.append(e,t[e])})),o.append("file",e),a.postForm("ImportEmployeeDeduct",o)},queryEmployeeDeductUnCalculate:function(e){return a.get("QueryEmployeeDeductUnCalculate",e)},queryEmployeeDeductCalculate:function(e){return a.get("QueryEmployeeDeductCalculate",e)},getEmployeeDeductCalculate:function(e){return a.get("GetEmployeeDeductCalculate",e)},updateEmployeeDeductCalculate:function(e){return a.post("UpdateEmployeeDeductCalculate",e)},exportEmployeeDeductCalculate:function(e){return a.post("ExportEmployeeDeductCalculate",{data:e,responseType:"arraybuffer"})},queryEmployeeDeductWorkingAge:function(e){return a.get("QueryEmployeeDeductWorkingAge",e)},getEmployeeDeductWorkingAge:function(e){return a.get("GetEmployeeDeductWorkingAge",e)},addEmployeeDeductWorkingAge:function(e){return a.post("AddEmployeeDeductWorkingAge",e)},updateEmployeeDeductWorkingAge:function(e){return a.post("UpdateEmployeeDeductWorkingAge",e)},deleteEmployeeDeductWorkingAge:function(e){return a.post("DeleteEmployeeDeductWorkingAge",e)},queryIncentType:function(){return a.get("QueryIncentType")},queryIncentLevel:function(){return a.get("QueryIncentLevel")},queryEmployeeIncentive:function(e){return a.get("QueryEmployeeIncentive",e)},addEmployeeIncentive:function(e){return a.post("AddEmployeeIncentive",e)},updateEmployeeIncentive:function(e){return a.post("UpdateEmployeeIncentive",e)},deleteEmployeeIncentive:function(e){return a.post("DeleteEmployeeIncentive",e)},getEmployeeHealth:function(e){return a.get("GetEmployeeHealth",e)},updateEmployeeHealth:function(e){return a.post("UpdateEmployeeHealth",e)},queryAccidentType:function(){return a.get("QueryAccidentType")},queryEmployeeAccident:function(e){return a.get("QueryEmployeeAccident",e)},addEmployeeAccident:function(e){return a.post("AddEmployeeAccident",e)},updateEmployeeAccident:function(e){return a.post("UpdateEmployeeAccident",e)},deleteEmployeeAccident:function(e){return a.post("DeleteEmployeeAccident",e)},queryEmployeeTeach:function(e){return a.get("QueryEmployeeTeach",e)},addEmployeeTeach:function(e){return a.post("AddEmployeeTeach",e)},updateEmployeeTeach:function(e){return a.post("UpdateEmployeeTeach",e)},deleteEmployeeTeach:function(e){return a.post("DeleteEmployeeTeach",e)},queryEmployeeRelation:function(e){return a.get("QueryEmployeeRelation",e)},addEmployeeRelation:function(e){return a.post("AddEmployeeRelation",e)},updateEmployeeRelation:function(e){return a.post("UpdateEmployeeRelation",e)},deleteEmployeeRelation:function(e){return a.post("DeleteEmployeeRelation",e)},queryIncomeType:function(){return a.get("QueryIncomeType")},queryEmployeeArticle:function(e){return a.get("QueryEmployeeArticle",e)},addEmployeeArticle:function(e){return a.post("AddEmployeeArticle",e)},updateEmployeeArticle:function(e){return a.post("UpdateEmployeeArticle",e)},deleteEmployeeArticle:function(e){return a.post("DeleteEmployeeArticle",e)},queryClassLevel:function(){return a.get("QueryClassLevel")},queryEmployeeClass:function(e){return a.get("QueryEmployeeClass",e)},addEmployeeClass:function(e){return a.post("AddEmployeeClass",e)},updateEmployeeClass:function(e){return a.post("UpdateEmployeeClass",e)},deleteEmployeeClass:function(e){return a.post("DeleteEmployeeClass",e)},queryEmployeePatent:function(e){return a.get("QueryEmployeePatent",e)},addEmployeePatent:function(e){return a.post("AddEmployeePatent",e)},updateEmployeePatent:function(e){return a.post("UpdateEmployeePatent",e)},deleteEmployeePatent:function(e){return a.post("DeleteEmployeePatent",e)},queryTeacherType:function(){return a.get("QueryTeacherType")},queryEmployeeTeacher:function(e){return a.get("QueryEmployeeTeacher",e)},addEmployeeTeacher:function(e){return a.post("AddEmployeeTeacher",e)},updateEmployeeTeacher:function(e){return a.post("UpdateEmployeeTeacher",e)},deleteEmployeeTeacher:function(e){return a.post("DeleteEmployeeTeacher",e)},queryAwardLevel:function(){return a.get("QueryAwardLevel")},queryDictByParentCode:function(e){return a.get("QueryDictByParentCode",e)},queryHighTalent:function(){return a.get("QueryHighTalent")},queryEmployeeAward:function(e){return a.get("QueryEmployeeAward",e)},addEmployeeAward:function(e){return a.post("AddEmployeeAward",e)},updateEmployeeAward:function(e){return a.post("UpdateEmployeeAward",e)},deleteEmployeeAward:function(e){return a.post("DeleteEmployeeAward",e)},addEmployeeHighTalent:function(e){return a.post("AddEmployeeHighTalent",e)},updateEmployeeHighTalent:function(e){return a.post("UpdateEmployeeHighTalent",e)},queryEmployeeHighTalent:function(e){return a.get("QueryEmployeeHighTalent",e)},deleteEmployeeHighTalent:function(e){return a.post("DeleteEmployeeHighTalent",e)},queryEmployeeDeptHistory:function(e){return a.get("QueryEmployeeDeptHistory",e)},updateEmployeeDept:function(e){return a.post("UpdateEmployeeDept",e)},batchUpdateEmployeeDept:function(e){return a.post("BatchUpdateEmployeeDept",e)},queryEmployeeList:function(e){return a.post("QueryEmployeeList",e)},queryEmployeeListTree:function(e){return a.get("QueryEmployeeListTree",e)},queryDictsSetting:function(e){return a.get("QueryDictsSetting",e)},queryEmployeeListSettingTree:function(e){return a.get("QueryEmployeeListSettingTree",e)},queryAdvancedQueryType:function(){return a.get("QueryAdvancedQueryType")},saveEmployeeList:function(e){return a.post("SaveEmployeeList",e)},deleteEmployeeList:function(e){return a.post("DeleteEmployeeList",e)},getEmployeeList:function(e){return a.get("GetEmployeeList",e)},getEmployeeListExcel:function(e){return a.getFile("GetEmployeeListExcel",e)},queryDeptPrincipal:function(e){return a.get("QueryDeptPrincipal",e)},queryOtherEmployeeInfo:function(e){return a.post("QueryOtherEmployeeInfo",e)},saveOtherEmployeeInfo:function(e){return a.post("SaveOtherEmployeeInfo",e)},deleteOtherEmployeeInfo:function(e){return a.post("DeleteOtherEmployeeInfo",e)},queryOtherEmpTypes:function(e){return a.get("QueryOtherEmpTypes",e)},importExcel:function(e,t){var o=new FormData;return t&&Object.keys(t).forEach((function(e){return o.append(e,t[e])})),o.append("file",e),a.postForm("ImportExcel",o)},downlodaImportExcelTemplate:function(e){return a.getFile("DownlodaImportExcelTemplate",e)},getSocialSecurityInfo:function(e){return a.get("getSocialSecurityInfo",e)},updateEmployeeSocialSecurity:function(e){return a.post("updateEmployeeSocialSecurity",e)},queryPayRollOrgClass:function(){return a.get("QueryPayRollOrgClass")},queryPayRollCompGroup:function(){return a.get("QueryPayRollCompGroup")},queryPayRollOrgSalary:function(e){return a.get("QueryPayRollOrgSalary",e)},queryPayRollOrgSalaryLevel:function(e){return a.get("QueryPayRollOrgSalaryLevel",e)},queryPayRollOrgPositionSalarys:function(e){return a.get("QueryPayRollOrgPositionSalarys",e)},queryWorkState:function(){return a.get("QueryWorkState")},getWagesInfo:function(e){return a.get("GetWagesInfo",e)},updateEmployeeBenefit:function(e){return a.post("UpdateEmployeeBenefit",e)},queryWagesHistory:function(e){return a.get("QueryWagesHistory",e)},getEmployeePayrollPrint:function(e){return a.get("GetEmployeePayrollPrint",e)},updateEmployeePayrollPrint:function(e){return a.post("UpdateEmployeePayrollPrint",e)},deleteEmployeePayrollPrint:function(e){return a.post("DeleteEmployeePayrollPrint",e)},getPrintDetailsInfo:function(e){return a.get("GetPrintDetailsInfo",e)},getEmployeeWage:function(e){return a.get("GetEmployeeWage",e)},editEmployeeWage:function(e){return a.post("EditEmployeeWage",e)},editEmployeeParty:function(e){return a.post("EditEmployeeParty",e)},queryEmployeeHRAffiliatedBranch:function(e){return a.get("QueryEmployeeHRAffiliatedBranch",e)},getEmployeeHRAffiliatedBranch:function(e){return a.get("GetEmployeeHRAffiliatedBranch",e)},addEmployeeHRAffiliatedBranch:function(e){return a.post("AddEmployeeHRAffiliatedBranch",e)},updateEmployeeHRAffiliatedBranch:function(e){return a.post("UpdateEmployeeHRAffiliatedBranch",e)},deleteEmployeeHRAffiliatedBranch:function(e){return a.post("DeleteEmployeeHRAffiliatedBranch",e)},queryEmployeeHRDict:function(e){return a.get("QueryEmployeeHRDict",e)},getEmployeeHRDict:function(e){return a.get("GetEmployeeHRDict",e)},addEmployeeHRDict:function(e){return a.post("AddEmployeeHRDict",e)},updateEmployeeHRDict:function(e){return a.post("UpdateEmployeeHRDict",e)},deleteEmployeeHRDict:function(e){return a.post("DeleteEmployeeHRDict",e)},queryEmployeeHRPartyMemberHonor:function(e){return a.get("QueryEmployeeHRPartyMemberHonor",e)},getEmployeeHRPartyMemberHonor:function(e){return a.get("GetEmployeeHRPartyMemberHonor",e)},addEmployeeHRPartyMemberHonor:function(e){return a.post("AddEmployeeHRPartyMemberHonor",e)},updateEmployeeHRPartyMemberHonor:function(e){return a.post("UpdateEmployeeHRPartyMemberHonor",e)},deleteEmployeeHRPartyMemberHonor:function(e){return a.post("DeleteEmployeeHRPartyMemberHonor",e)}}},e9b0:function(e,t,o){"use strict";o.r(t);var r=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("layout1",{scopedSlots:e._u([{key:"header",fn:function(){return[o("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"部门编码"},model:{value:e.deptListQuery.code,callback:function(t){e.$set(e.deptListQuery,"code",t)},expression:"deptListQuery.code"}}),o("el-input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"部门名称"},model:{value:e.deptListQuery.name,callback:function(t){e.$set(e.deptListQuery,"name",t)},expression:"deptListQuery.name"}}),o("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.loadTree}},[e._v("查询")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addDialog}},[e._v("添加")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-edit"},on:{click:e.updateDialog}},[e._v("更新")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-delete"},on:{click:e.deleteDept}},[e._v("删除")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-rank"},on:{click:e.moveDialog}},[e._v("移动")]),o("el-button",{attrs:{type:"primary",icon:"el-icon-copy-document"},on:{click:e.mergeDialog}},[e._v("合并")])]},proxy:!0},{key:"aside",fn:function(){return[o("c-tree",{attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[o("el-card",[o("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[o("span",[e._v("组织信息")])]),o("div",{staticClass:"text-container"},[o("el-form",{attrs:{"label-position":"right","label-width":"100px"}},[o("el-row",[o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"部门编号"}},[e._v(" "+e._s(e.deptInfo.code)+" ")])],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"组织结构类型"}},[e._v(" "+e._s(e.deptInfo.deptTypeName)+" ")])],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"中文名"}},[e._v(" "+e._s(e.deptInfo.name)+" ")])],1)],1),o("el-row",[o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"英文名"}},[e._v(" "+e._s(e.deptInfo.eName)+" ")])],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"虚拟组织"}},[null!=e.deptInfo.id?o("span",[o("svg-icon",{attrs:{"icon-class":e.deptInfo.isVirtual?"勾":"叉"}})],1):e._e()])],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"HisCode"}},[e._v(" "+e._s(e.deptInfo.hospitalCode)+" ")])],1)],1),o("el-row",[o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"OASerial"}},[e._v(" "+e._s(e.deptInfo.orgSerial)+" ")])],1),o("el-col",{attrs:{span:16}},[o("el-form-item",{attrs:{label:"地理位置"}},[e._v(" "+e._s(e.deptInfo.location)+" ")])],1)],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"描述"}},[e._v(" "+e._s(e.deptInfo.description)+" ")])],1)],1)],1)],1)])]},proxy:!0}])}),o("div",[o("el-dialog",{attrs:{title:"添加",visible:e.addDialogVisible,width:"60%"},on:{"update:visible":function(t){e.addDialogVisible=t}}},[o("el-form",{ref:"ref_addForm",attrs:{rules:e.rules,model:e.addForm,"label-width":"100px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"部门编号",prop:"code"}},[o("el-input",{attrs:{placeholder:"请输入部门编号",maxlength:"50"},model:{value:e.addForm.code,callback:function(t){e.$set(e.addForm,"code",t)},expression:"addForm.code"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"组织结构类型",prop:"deptTypeId"}},[o("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.addForm.deptTypeId,callback:function(t){e.$set(e.addForm,"deptTypeId",t)},expression:"addForm.deptTypeId"}},e._l(e.departmentTypes,(function(e){return o("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"中文名",prop:"name"}},[o("el-input",{attrs:{placeholder:"请输入中文名",maxlength:"50"},model:{value:e.addForm.name,callback:function(t){e.$set(e.addForm,"name",t)},expression:"addForm.name"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"英文名",prop:"eName"}},[o("el-input",{attrs:{placeholder:"请输入英文名",maxlength:"50"},model:{value:e.addForm.eName,callback:function(t){e.$set(e.addForm,"eName",t)},expression:"addForm.eName"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"上级部门",prop:"parentName"}},[e._v(" "+e._s(e.addForm.parentName)+" ")])],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"虚拟组织",prop:"isVirtual"}},[o("el-checkbox",{model:{value:e.addForm.isVirtual,callback:function(t){e.$set(e.addForm,"isVirtual",t)},expression:"addForm.isVirtual"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"HisCode",prop:"hospitalCode"}},[o("el-input",{attrs:{placeholder:"请输入HisCode",maxlength:"10"},model:{value:e.addForm.hospitalCode,callback:function(t){e.$set(e.addForm,"hospitalCode",t)},expression:"addForm.hospitalCode"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"院区",prop:"hospitalAreaId"}},[o("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.addForm.hospitalAreaId,callback:function(t){e.$set(e.addForm,"hospitalAreaId",t)},expression:"addForm.hospitalAreaId"}},e._l(e.hospitalArea,(function(e){return o("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"OASerial",prop:"OrgSerial"}},[o("el-input",{attrs:{placeholder:"请输入Org Serial",maxlength:"20"},model:{value:e.addForm.orgSerial,callback:function(t){e.$set(e.addForm,"orgSerial",t)},expression:"addForm.orgSerial"}})],1)],1),o("el-col",{attrs:{span:12}})],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"地理位置",prop:"location"}},[o("el-input",{attrs:{type:"textarea",placeholder:"请输入地理位置",maxlength:"50"},model:{value:e.addForm.location,callback:function(t){e.$set(e.addForm,"location",t)},expression:"addForm.location"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"描述",prop:"description"}},[o("el-input",{attrs:{type:"textarea",placeholder:"请输入描述",maxlength:"100"},model:{value:e.addForm.description,callback:function(t){e.$set(e.addForm,"description",t)},expression:"addForm.description"}})],1)],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.addDialogVisible=!1}}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:e.submitAddForm}},[e._v("确 定")])],1)],1),o("el-dialog",{attrs:{title:"更新",visible:e.updateDialogVisible,width:"60%"},on:{"update:visible":function(t){e.updateDialogVisible=t}}},[o("el-form",{ref:"ref_updateForm",attrs:{rules:e.rules,model:e.updateForm,"label-width":"100px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"部门编号",porp:"code"}},[o("el-input",{attrs:{placeholder:"请输入部门编号",maxlength:"50"},model:{value:e.updateForm.code,callback:function(t){e.$set(e.updateForm,"code",t)},expression:"updateForm.code"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"组织结构类型",prop:"deptTypeId"}},[o("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.updateForm.deptTypeId,callback:function(t){e.$set(e.updateForm,"deptTypeId",t)},expression:"updateForm.deptTypeId"}},e._l(e.departmentTypes,(function(e){return o("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"中文名",prop:"name"}},[o("el-input",{attrs:{placeholder:"请输入中文名",maxlength:"50"},model:{value:e.updateForm.name,callback:function(t){e.$set(e.updateForm,"name",t)},expression:"updateForm.name"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"英文名",prop:"eName"}},[o("el-input",{attrs:{placeholder:"请输入英文名",maxlength:"50"},model:{value:e.updateForm.eName,callback:function(t){e.$set(e.updateForm,"eName",t)},expression:"updateForm.eName"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"上级部门",prop:"parentName"}},[e._v(" "+e._s(e.updateForm.parentName)+" ")])],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"虚拟组织",prop:"isVirtual"}},[o("el-checkbox",{model:{value:e.updateForm.isVirtual,callback:function(t){e.$set(e.updateForm,"isVirtual",t)},expression:"updateForm.isVirtual"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"HisCode",prop:"hospitalCode"}},[o("el-input",{attrs:{placeholder:"请输入HisCode",maxlength:"10"},model:{value:e.updateForm.hospitalCode,callback:function(t){e.$set(e.updateForm,"hospitalCode",t)},expression:"updateForm.hospitalCode"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"院区",prop:"hospitalAreaId"}},[o("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.updateForm.hospitalAreaId,callback:function(t){e.$set(e.updateForm,"hospitalAreaId",t)},expression:"updateForm.hospitalAreaId"}},e._l(e.hospitalArea,(function(e){return o("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"OASerial",prop:"OrgSerial"}},[o("el-input",{attrs:{placeholder:"请输入Org Serial",maxlength:"20"},model:{value:e.updateForm.orgSerial,callback:function(t){e.$set(e.updateForm,"orgSerial",t)},expression:"updateForm.orgSerial"}})],1)],1),o("el-col",{attrs:{span:12}})],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"地理位置",prop:"location"}},[o("el-input",{attrs:{type:"textarea",placeholder:"请输入地理位置",maxlength:"50"},model:{value:e.updateForm.location,callback:function(t){e.$set(e.updateForm,"location",t)},expression:"updateForm.location"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"描述",prop:"description"}},[o("el-input",{attrs:{type:"textarea",placeholder:"请输入描述",maxlength:"100"},model:{value:e.updateForm.description,callback:function(t){e.$set(e.updateForm,"description",t)},expression:"updateForm.description"}})],1)],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.updateDialogVisible=!1}}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:e.submitUpdateForm}},[e._v("确 定")])],1)],1),o("el-dialog",{attrs:{title:"移动",visible:e.moveDialogVisible,width:"30%"},on:{"update:visible":function(t){e.moveDialogVisible=t}}},[o("el-form",{ref:"ref_moveForm",attrs:{rules:e.moveRules,model:e.moveForm,"label-width":"100px"}},[o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"移动对象","hide-required-asterisk":"",prop:"originDept"}},[o("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},on:{change:e.selectedFun},model:{value:e.moveForm.originDept,callback:function(t){e.$set(e.moveForm,"originDept",t)},expression:"moveForm.originDept"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"目标上级",prop:"toParentDept"}},[o("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},on:{change:e.selectedFun},model:{value:e.moveForm.toParentDept,callback:function(t){e.$set(e.moveForm,"toParentDept",t)},expression:"moveForm.toParentDept"}})],1)],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.moveDialogVisible=!1}}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:e.submitMoveForm}},[e._v("确 定")])],1)],1),o("el-dialog",{attrs:{title:"合并",visible:e.mergeDialogVisible,width:"30%"},on:{"update:visible":function(t){e.mergeDialogVisible=t}}},[o("el-form",{ref:"ref_mergeForm",attrs:{rules:e.mergeRules,model:e.mergeForm,"label-width":"100px"}},[o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"原部门",prop:"originDept"}},[o("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},on:{change:e.selectedFun},model:{value:e.mergeForm.originDept,callback:function(t){e.$set(e.mergeForm,"originDept",t)},expression:"mergeForm.originDept"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"合并到",prop:"mergeToDept"}},[o("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},on:{change:e.selectedFun},model:{value:e.mergeForm.mergeToDept,callback:function(t){e.$set(e.mergeForm,"mergeToDept",t)},expression:"mergeForm.mergeToDept"}})],1)],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.mergeDialogVisible=!1}}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:e.submitMergeForm}},[e._v("确 定")])],1)],1)],1)],1)},n=[],a=(o("b0c0"),o("d3b7"),o("25f0"),o("d368")),i=o("f9ac"),l=o("e44c"),u={components:{},data:function(){var e=this,t=function(t,o,r){""===o?r(new Error("请选择目标上级")):e.moveForm.originDept===o?r(new Error("目标上级和移动对象不能相同!")):r()},o=function(t,o,r){""===o?r(new Error("请选择合并到")):e.mergeForm.originDept===o?r(new Error("合并到和原部门不能相同!")):r()};return{treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:["1"],currentNode:null,departmentTypes:[],deptInfo:{},addForm:{},updateForm:{},moveForm:{},mergeForm:{},rules:{},hospitalArea:[],moveRules:{originDept:{required:!0,message:"请选择移动对象",trigger:"change"},toParentDept:[{required:!0,message:"请选择目标上级",trigger:"change"},{validator:t,trigger:"change"}]},mergeRules:{originDept:{required:!0,message:"请选择原部门",trigger:"change"},mergeToDept:[{required:!0,message:"请选择合并到目标",trigger:"change"},{validator:o,trigger:"change"}]},addDialogVisible:!1,updateDialogVisible:!1,moveDialogVisible:!1,mergeDialogVisible:!1,selected:"",deptListQuery:{}}},created:function(){this.loadTree(),this.loadDepartmentType(),this.loadHospitalArea()},methods:{loadTree:function(){var e=this;a["a"].QueryOrganization(this.deptListQuery).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)})),this.resertCurrentNode()},loadDepartmentType:function(){var e=this;i["a"].queryDict({ParentName:"组织结构类型"}).then((function(t){e.departmentTypes=t.data.datas})).catch((function(e){console.log(e)}))},loadHospitalArea:function(){var e=this;i["a"].queryDict({ParentName:"院区"}).then((function(t){e.hospitalArea=t.data.datas})).catch((function(e){console.log(e)}))},treeNodeClick:function(e){this.deptInfo=e,this.currentNode=e},resertCurrentNode:function(){this.currentNode=null,this.deptInfo={}},resetAddForm:function(){var e=this;this.addForm={code:"",deptTypeId:"",deptTypeName:"",name:"",eName:"",isVirtual:!1,location:"",description:"",hospitalAreaId:""},this.$nextTick((function(){e.$refs["ref_addForm"].clearValidate()}))},addDialog:function(){this.currentNode?(this.resetAddForm(),this.addForm.parentName=this.currentNode.name,this.addForm.parentId=this.currentNode.id,this.addDialogVisible=!0):this.$message({showClose:!0,message:"请选择一个部门"})},resetUpdateForm:function(){var e=this;this.updateForm={id:"",code:"",deptTypeId:"",deptTypeName:"",name:"",eName:"",isVirtual:!1,location:"",description:"",hospitalAreaId:""},this.$nextTick((function(){e.$refs["ref_updateForm"].clearValidate()}))},updateDialog:function(){var e=this;this.currentNode?(this.resetUpdateForm(),a["a"].GetDepartment({id:this.currentNode.id}).then((function(t){e.updateForm=t.data})).catch((function(e){console.log(e)})),this.updateDialogVisible=!0):this.$message({showClose:!0,message:"请选择一个部门"})},moveDialog:function(){this.currentNode?(this.resetMoveForm(),this.moveDialogVisible=!0,this.moveForm={originDept:this.currentNode.id}):this.$message({showClose:!0,message:"请选择一个部门"})},mergeDialog:function(){this.currentNode?(this.resetMergeForm(),this.mergeDialogVisible=!0,this.mergeForm={originDept:this.currentNode.id}):this.$message({showClose:!0,message:"请选择一个部门"})},submitAddForm:function(){var e=this;this.$refs["ref_addForm"].validate((function(t){t&&a["a"].AddDepartment(e.addForm).then((function(t){if(t.succeed){var o=t.data.uid;e.$notice.message("新增成功","success"),e.addDeptPost(o),e.addDialogVisible=!1,e.loadTree()}else-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))}))},submitUpdateForm:function(){var e=this;this.$refs["ref_updateForm"].validate((function(t){t&&a["a"].UpdateDepartment(e.updateForm).then((function(t){if(t.succeed){var o=t.data.uid;e.$notice.message("更新成功","success"),e.updateDeptPost(o),e.updateDialogVisible=!1,e.loadTree()}else-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("更新失败。","error")}))}))},addDeptPost:function(e){var t={condition:e};l["a"].postAddDocumentInformation(t).then((function(e){})).catch((function(e){console.log(e)}))},updateDeptPost:function(e){var t={condition:e};l["a"].postUpdateDocumentInformation(t).then((function(e){})).catch((function(e){console.log(e)}))},deleteDeptPost:function(e){var t={condition:e};l["a"].postDeleteDocumentInformation(t).then((function(e){})).catch((function(e){console.log(e)}))},resetMoveForm:function(){var e=this;this.moveForm={originDept:"",toParentDept:""},this.$nextTick((function(){e.$refs["ref_moveForm"].clearValidate()}))},submitMoveForm:function(){var e=this;this.$refs["ref_moveForm"].validate((function(t){t&&e.$confirm("确定移动吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a["a"].MoveDepartment(e.moveForm).then((function(t){t.succeed?(e.$notice.message("移动成功","success"),e.moveDialogVisible=!1,e.loadTree()):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("移动失败。","error")}))}))}))},resetMergeForm:function(){var e=this;this.mergeForm={originDept:"",mergeToDept:""},this.$nextTick((function(){e.$refs["ref_mergeForm"].clearValidate()}))},submitMergeForm:function(){var e=this;this.$refs["ref_mergeForm"].validate((function(t){t&&e.$confirm("确定合并吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a["a"].MergeDepartment(e.mergeForm).then((function(t){t.succeed?(e.$notice.message("合并成功","success"),e.mergeDialogVisible=!1,e.loadTree()):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("合并失败。","error")}))}))}))},deleteDept:function(){var e=this;this.currentNode?this.$confirm("确定删除该部门吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deptInfo.confirmToDelete=!1,a["a"].DeleteDepartment(e.deptInfo).then((function(t){if(t.succeed){var o=t.data;e.loadTree(),e.$notice.message("删除成功","success"),e.deleteDeptPost(o)}else-3===t.type&&e.$confirm(t.messages.toString(),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deptInfo.confirmToDelete=!0,a["a"].DeleteDepartment(e.deptInfo).then((function(t){t.succeed?(e.loadTree(),e.$notice.message("删除成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))})).catch((function(t){t.succeed||(console.log(t),e.$notice.message("取消删除","info"))}))})).catch((function(e){console.log(e)}))})).catch((function(t){t.succeed||(console.log(t),e.$notice.message("取消删除","info"))})):this.$message({showClose:!0,message:"请选择一个部门"})},selectedFun:function(e){}}},p=u,c=(o("729a"),o("2877")),s=Object(c["a"])(p,r,n,!1,null,"0780fc5d",null);t["default"]=s.exports},f9ac:function(e,t,o){"use strict";var r=o("cfe3"),n="SysManage",a=new r["a"](n);t["a"]={queryDict:function(e){return a.get("QueryDict",e)},queryDictType:function(e){return a.post("QueryDictType",e)},addDict:function(e){return a.post("AddDict",e)},deleteDict:function(e){return a.post("DeleteDict",e)},updateDict:function(e){return a.post("UpdateDict",e)},getDict:function(e){return a.get("GetDict",e)},querySysSetting:function(e){return a.get("QuerySysSetting",e)},addSysSetting:function(e){return a.post("AddSysSetting",e)},deleteSysSetting:function(e){return a.post("DeleteSysSetting",e)},updateSysSetting:function(e){return a.post("UpdateSysSetting",e)},getSysSetting:function(e){return a.get("GetSysSetting",e)},queryLanguage:function(e){return a.get("QueryLanguage",e)},getEnumInfos:function(e){return a.get("GetEnumInfos",e)},queryUserGroups:function(e){return a.post("QueryUserGroups",e)},saveUserGroup:function(e){return a.post("SaveUserGroup",e)},deleteUserGroup:function(e){return a.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return a.get("DropdownUserGroups",e)},queryUsers:function(e){return a.post("QueryUsers",e)},saveUser:function(e){return a.post("SaveUser",e)},deleteUser:function(e){return a.post("DeleteUser",e)},initPwd:function(e){return a.post("InitPwd",e)},getUserById:function(e){return a.get("GetUserById",e)},queryEmployees:function(e){return a.post("QueryEmployees",e)},queryModuleInfos:function(e){return a.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return a.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return a.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return a.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return a.post("SaveRightOfDept",e)},queryControlRight:function(e){return a.post("QueryControlRight",e)},saveControlRights:function(e){return a.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return a.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return a.get("QueryStationTree",e)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(e){return a.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return a.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)}}}}]);