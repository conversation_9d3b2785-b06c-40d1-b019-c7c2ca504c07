(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67cc30b4"],{"030a":function(e,i,o){},"9ed6":function(e,i,o){"use strict";o.r(i);var t=function(){var e=this,i=e.$createElement,o=e._self._c||i;return o("div",{staticClass:"login-container"},[o("div",{staticClass:"login-form"},[o("el-form",{ref:"loginForm",attrs:{model:e.loginForm,rules:e.loginRules,"auto-complete":"on","label-position":"left"}},[o("div",{staticClass:"title-container"},[o("h3",{staticClass:"title"},[e._v("欢迎登录")])]),o("el-form-item",{attrs:{prop:"loginName"}},[o("el-input",{ref:"loginName",attrs:{name:"loginName",placeholder:"请输入用户名",type:"text",tabindex:"1","auto-complete":"on",size:"medium"},model:{value:e.loginForm.loginName,callback:function(i){e.$set(e.loginForm,"loginName",i)},expression:"loginForm.loginName"}},[o("svg-icon",{attrs:{slot:"prefix","icon-class":"user"},slot:"prefix"})],1)],1),o("el-form-item",{attrs:{prop:"password"}},[o("el-input",{ref:"password",attrs:{name:"password",placeholder:"请输入密码",type:"password",tabindex:"2","auto-complete":"on","show-password":"",size:"medium"},nativeOn:{keyup:function(i){return!i.type.indexOf("key")&&e._k(i.keyCode,"enter",13,i.key,"Enter")?null:e.handleLogin(i)}},model:{value:e.loginForm.password,callback:function(i){e.$set(e.loginForm,"password",i)},expression:"loginForm.password"}},[o("svg-icon",{attrs:{slot:"prefix","icon-class":"password"},slot:"prefix"})],1)],1),o("el-form-item",{attrs:{prop:"verificationCode"}},[o("el-row",[o("el-col",{attrs:{span:17}},[o("el-input",{ref:"verificationCode",attrs:{name:"verificationCode",placeholder:"请输入验证码",type:"text",tabindex:"3",size:"medium"},nativeOn:{keyup:function(i){return!i.type.indexOf("key")&&e._k(i.keyCode,"enter",13,i.key,"Enter")?null:e.handleLogin(i)}},model:{value:e.loginForm.verificationCode,callback:function(i){e.$set(e.loginForm,"verificationCode",i)},expression:"loginForm.verificationCode"}})],1),o("el-col",{attrs:{span:7}},[o("el-image",{staticStyle:{width:"100%",height:"35px","margin-left":"3px"},attrs:{src:"data:image/png;base64,"+e.tempVerificationCode,fit:"fill"},on:{click:e.getVerificationCode}})],1)],1)],1),o("el-button",{staticClass:"loginBtn",attrs:{loading:e.loading,type:"primary",size:"medium"},nativeOn:{click:function(i){return i.preventDefault(),e.handleLogin(i)}}},[e._v("登录")])],1)],1)])},n=[],r=(o("d3b7"),o("ac1f"),o("25f0"),o("5319"),o("498a"),o("b27e")),a=o("c24f"),l={name:"Login",data:function(){var e=function(e,i,o){o()},i=function(e,i,o){o()};return{loginForm:{loginName:"",password:"",verificationKey:"",verificationCode:""},loginRules:{loginName:[{required:!0,trigger:"blur",validator:e}],password:[{required:!0,trigger:"blur",validator:i}]},loading:!1,redirect:void 0,tempVerificationCode:"获取验证码"}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},created:function(){this.tempVerificationCode="获取验证码",this.getVerificationCode()},methods:{handleLogin:function(){var e=this;this.loginForm.loginName&&""!==this.loginForm.loginName.trim()?this.loginForm.password&&""!==this.loginForm.password.trim()?this.loginForm.verificationCode&&""!==this.loginForm.verificationCode.trim()&&4==this.loginForm.verificationCode.length?this.$refs.loginForm.validate((function(i){if(!i)return console.log("error submit!!"),!1;e.loading=!0;var o={loginName:Object(r["a"])(e.loginForm.loginName),password:Object(r["a"])(e.loginForm.password),verificationKey:e.loginForm.verificationKey,verificationCode:e.loginForm.verificationCode};e.$store.dispatch("user/login",o).then((function(){e.$router.push({path:e.redirect||"/"}),e.loading=!1})).catch((function(){e.getVerificationCode(),e.loading=!1}))})):this.$message.error("请输入4位验证码"):this.$message.error("请输入密码"):this.$message.error("请输入用户名")},getVerificationCode:function(){var e=this;this.loginForm.verificationKey=this.generateGuid(),this.loginForm.verificationCode="",a["a"].getVerificationCode(this.loginForm).then((function(i){e.tempVerificationCode=i.data})).catch((function(e){console.log(e)}))},generateGuid:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var i=16*Math.random()|0,o="x"===e?i:3&i|8;return o.toString(16)}))}}},s=l,c=(o("f364"),o("2877")),d=Object(c["a"])(s,t,n,!1,null,"0c339d4c",null);i["default"]=d.exports},f364:function(e,i,o){"use strict";var t=o("030a"),n=o.n(t);n.a}}]);