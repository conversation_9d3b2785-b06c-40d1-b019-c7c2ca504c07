(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-42fb5610"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"547b":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[n("el-form-item",[n("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),n("el-form-item",[n("el-select",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),n("el-form-item",[n("el-input",{attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addDialog}},[e._v("添加")])],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"中文名"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.name))])]}}])}),n("el-table-column",{attrs:{label:"中文描述"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.description))])]}}])}),n("el-table-column",{attrs:{label:"序号"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.ordinal))])]}}])}),n("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.updateDialog(o)}}},[e._v(" 编辑 ")]),n("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.deleteRecord(o)}}},[e._v(" 删除 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),n("el-dialog",{attrs:{title:"添加",visible:e.addDialogVisible,width:"60%"},on:{"update:visible":function(t){e.addDialogVisible=t},close:e.closeAddDialog}},[n("el-form",{ref:"ref_addForm",attrs:{rules:e.rules,model:e.addForm,"label-width":"100px"}},[n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"中文名",prop:"name"}},[n("el-input",{attrs:{placeholder:"中文名",clearable:"",maxlength:"50"},model:{value:e.addForm.name,callback:function(t){e.$set(e.addForm,"name",t)},expression:"addForm.name"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"序号",prop:"ordinal"}},[n("el-input",{attrs:{placeholder:"序号",maxlength:"10",clearable:""},model:{value:e.addForm.ordinal,callback:function(t){e.$set(e.addForm,"ordinal",e._n(t))},expression:"addForm.ordinal"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"中文描述",prop:"description"}},[n("el-input",{attrs:{placeholder:"中文描述",maxlength:"50",clearable:""},model:{value:e.addForm.description,callback:function(t){e.$set(e.addForm,"description",t)},expression:"addForm.description"}})],1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.addDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitAddForm}},[e._v("保 存")])],1)],1),n("el-dialog",{attrs:{title:"更新",visible:e.updateDialogVisible,width:"60%"},on:{"update:visible":function(t){e.updateDialogVisible=t}}},[n("el-form",{ref:"ref_updateForm",attrs:{rules:e.rules,model:e.updateForm,"label-width":"100px"}},[n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"中文名",prop:"name"}},[n("el-input",{attrs:{placeholder:"中文名",maxlength:"50",clearable:""},model:{value:e.updateForm.name,callback:function(t){e.$set(e.updateForm,"name",t)},expression:"updateForm.name"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"序号",prop:"ordinal"}},[n("el-input",{attrs:{placeholder:"序号",maxlength:"10",clearable:""},model:{value:e.updateForm.ordinal,callback:function(t){e.$set(e.updateForm,"ordinal",e._n(t))},expression:"updateForm.ordinal"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"中文描述",prop:"description"}},[n("el-input",{attrs:{placeholder:"中文描述",clearable:"",maxlength:"50"},model:{value:e.updateForm.description,callback:function(t){e.$set(e.updateForm,"description",t)},expression:"updateForm.description"}})],1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.updateDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitUpdateForm}},[e._v("保 存")])],1)],1)],1)},r=[],i=(n("a9e3"),n("8ba40"),n("d3b7"),n("ac1f"),n("25f0"),n("841c"),n("d368")),a=n("f9ac"),u={components:{},data:function(){var e=function(e,t,n){t&&!Number.isInteger(t)?n(new Error("请输入数字值")):n()};return{addForm:{},updateForm:{},rules:{name:[{required:!0,message:"请输入中文名",trigger:"blur"}],ordinal:[{validator:e,trigger:"blur"}]},addDialogVisible:!1,updateDialogVisible:!1,pageList:[],total:1,listQuery:{queryCondition:{},pageIndex:1,pageSize:10},listLoading:!1,temp:{},dataColumns:[{value:"1",label:"中文名",type:"System.String",columnName:"Name"},{value:"2",label:"中文描述",type:"System.String",columnName:"Description"},{value:"3",label:"序号",type:"System.String",columnName:"Ordinal"}],selectConditionOptions:[]}},created:function(){this.getPageList(),this.loadConditions()},methods:{search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(){},loadConditions:function(){var e=this;a["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getPageList:function(){var e=this;this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),console.log(this.listQuery),i["a"].queryPosition(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},resetTemp:function(){this.temp={}},addDialog:function(){this.resetTemp(),this.addDialogVisible=!0},updateDialog:function(e){var t=this;this.resetTemp(),this.updateDialogVisible=!0,this.temp=Object.assign({},e),i["a"].getPosition({id:this.temp.id}).then((function(e){t.$refs["ref_updateForm"].resetFields(),t.$refs["ref_updateForm"].clearValidate(),e.succeed?t.updateForm=e.data:t.$notice.resultTip(e)})).catch((function(e){console.log(e)}))},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()},submitAddForm:function(){var e=this;this.$refs["ref_addForm"].validate((function(t){t&&i["a"].addPosition(e.addForm).then((function(t){t.succeed?(e.addDialogVisible=!1,e.search(),e.$notice.message("创建成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))}))},submitUpdateForm:function(){var e=this;this.$refs["ref_updateForm"].validate((function(t){t&&i["a"].updatePosition(e.updateForm).then((function(t){t.succeed?(e.updateDialogVisible=!1,e.search(),e.$notice.message("更新成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))}))},deleteRecord:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.temp=Object.assign({},e),t.temp.confirmToDelete=!1,i["a"].deletePosition(t.temp).then((function(e){e.succeed?(t.search(),t.$notice.message("删除成功","success")):-3===e.type&&t.$confirm(e.messages.toString(),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.temp.confirmToDelete=!0,i["a"].deletePosition(t.temp).then((function(e){e.succeed?(t.search(),t.$notice.message("删除成功","success")):t.$notice.resultTip(e)})).catch((function(e){console.log(e)}))})).catch((function(e){e.succeed||t.$notice.message("取消删除","info")}))})).catch((function(e){console.log(e)}))})).catch((function(e){e.succeed||t.$notice.message("取消删除","info")}))}}},l=u,s=n("2877"),c=Object(s["a"])(l,o,r,!1,null,null,null);t["default"]=c.exports},"5e89":function(e,t,n){var o=n("861d"),r=Math.floor;e.exports=function(e){return!o(e)&&isFinite(e)&&r(e)===e}},"841c":function(e,t,n){"use strict";var o=n("d784"),r=n("825a"),i=n("1d80"),a=n("129f"),u=n("14c3");o("search",1,(function(e,t,n){return[function(t){var n=i(this),o=void 0==t?void 0:t[e];return void 0!==o?o.call(t,n):new RegExp(t)[e](String(n))},function(e){var o=n(t,e,this);if(o.done)return o.value;var i=r(e),l=String(this),s=i.lastIndex;a(s,0)||(i.lastIndex=0);var c=u(i,l);return a(i.lastIndex,s)||(i.lastIndex=s),null===c?-1:c.index}]}))},"8ba40":function(e,t,n){var o=n("23e7"),r=n("5e89");o({target:"Number",stat:!0},{isInteger:r})},d368:function(e,t,n){"use strict";var o=n("cfe3"),r="Organization",i=new o["a"](r);t["a"]={QueryOrganizationHiddenTop:function(e){return i.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return i.get("QueryOrganization",e)},QueryDepartment:function(e){return i.get("QueryDepartment",e)},GetDepartment:function(e){return i.get("GetDepartment",e)},AddDepartment:function(e){return i.post("AddDepartment",e)},UpdateDepartment:function(e){return i.post("UpdateDepartment",e)},MoveDepartment:function(e){return i.post("MoveDepartment",e)},MergeDepartment:function(e){return i.post("MergeDepartment",e)},DeleteDepartment:function(e){return i.post("DeleteDepartment",e)},queryPosition:function(e){return i.post("QueryPosition",e)},getPosition:function(e){return i.get("GetPosition",e)},addPosition:function(e){return i.post("AddPosition",e)},updatePosition:function(e){return i.post("UpdatePosition",e)},deletePosition:function(e){return i.post("DeletePosition",e)},GetStation:function(e){return i.get("GetStation",e)},AddStation:function(e){return i.post("AddStation",e)},UpdateStation:function(e){return i.post("UpdateStation",e)},DeleteStation:function(e){return i.post("DeleteStation",e)},QueryPositionStationTree:function(e){return i.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return i.post("AllocatePosition",e)},DeletePositionStation:function(e){return i.post("DeletePositionStation",e)},queryDeptByUser:function(e){return i.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return i.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return i.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return i.get("QuerySenioritySelect")},queryStationAllowance:function(e){return i.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return i.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),i.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return i.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return i.get("GetStationAllowance",e)},addStationAllowance:function(e){return i.post("AddStationAllowance",e)},updateStationAllowance:function(e){return i.post("UpdateStationAllowance",e)},querySeniority:function(e){return i.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),i.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return i.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return i.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return i.get("GetSeniority",e)},addSeniority:function(e){return i.post("AddSeniority",e)},updateSeniority:function(e){return i.post("UpdateSeniority",e)},querySalaryScale:function(e){return i.get("QuerySalaryScale",e)},getSalaryScale:function(e){return i.get("GetSalaryScale",e)},addSalaryScale:function(e){return i.post("AddSalaryScale",e)},updateSalaryScale:function(e){return i.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return i.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),i.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return i.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return i.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return i.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return i.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return i.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return i.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return i.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return i.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return i.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return i.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return i.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return i.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return i.post("DeleteTelephoneFee",e)}}},f9ac:function(e,t,n){"use strict";var o=n("cfe3"),r="SysManage",i=new o["a"](r);t["a"]={queryDict:function(e){return i.get("QueryDict",e)},queryDictType:function(e){return i.post("QueryDictType",e)},addDict:function(e){return i.post("AddDict",e)},deleteDict:function(e){return i.post("DeleteDict",e)},updateDict:function(e){return i.post("UpdateDict",e)},getDict:function(e){return i.get("GetDict",e)},querySysSetting:function(e){return i.get("QuerySysSetting",e)},addSysSetting:function(e){return i.post("AddSysSetting",e)},deleteSysSetting:function(e){return i.post("DeleteSysSetting",e)},updateSysSetting:function(e){return i.post("UpdateSysSetting",e)},getSysSetting:function(e){return i.get("GetSysSetting",e)},queryLanguage:function(e){return i.get("QueryLanguage",e)},getEnumInfos:function(e){return i.get("GetEnumInfos",e)},queryUserGroups:function(e){return i.post("QueryUserGroups",e)},saveUserGroup:function(e){return i.post("SaveUserGroup",e)},deleteUserGroup:function(e){return i.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return i.get("DropdownUserGroups",e)},queryUsers:function(e){return i.post("QueryUsers",e)},saveUser:function(e){return i.post("SaveUser",e)},deleteUser:function(e){return i.post("DeleteUser",e)},initPwd:function(e){return i.post("InitPwd",e)},getUserById:function(e){return i.get("GetUserById",e)},queryEmployees:function(e){return i.post("QueryEmployees",e)},queryModuleInfos:function(e){return i.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return i.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return i.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return i.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return i.post("SaveRightOfDept",e)},queryControlRight:function(e){return i.post("QueryControlRight",e)},saveControlRights:function(e){return i.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return i.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return i.get("QueryStationTree",e)},queryStationTypeSelector:function(){return i.get("QueryStationTypeSelector")},queryStationSelector:function(e){return i.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return i.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return i.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return i.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return i.get("QueryStationAllowance",e)}}}}]);