(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e7ab977"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"19de":function(t,e){t.exports=function(t,e,a,n){var o="undefined"!==typeof n?[n,t]:[t],i=new Blob(o,{type:a||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(i,e);else{var r=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(i):window.webkitURL.createObjectURL(i),l=document.createElement("a");l.style.display="none",l.href=r,l.setAttribute("download",e),"undefined"===typeof l.download&&l.setAttribute("target","_blank"),document.body.appendChild(l),l.click(),setTimeout((function(){document.body.removeChild(l),window.URL.revokeObjectURL(r)}),200)}}},"841c":function(t,e,a){"use strict";var n=a("d784"),o=a("825a"),i=a("1d80"),r=a("129f"),l=a("14c3");n("search",1,(function(t,e,a){return[function(e){var a=i(this),n=void 0==e?void 0:e[t];return void 0!==n?n.call(e,a):new RegExp(e)[t](String(a))},function(t){var n=a(e,t,this);if(n.done)return n.value;var i=o(t),s=String(this),c=i.lastIndex;r(c,0)||(i.lastIndex=0);var u=l(i,s);return r(i.lastIndex,c)||(i.lastIndex=c),null===u?-1:u.index}]}))},"9c98":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:t._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:10,type:"flex"}},[a("el-col",{attrs:{span:4}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位类型"},on:{change:t.changeParentStation},model:{value:t.listQuery.parentStationId,callback:function(e){t.$set(t.listQuery,"parentStationId",e)},expression:"listQuery.parentStationId"}},t._l(t.parentStationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-col",{attrs:{span:4}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位"},model:{value:t.listQuery.stationId,callback:function(e){t.$set(t.listQuery,"stationId",e)},expression:"listQuery.stationId"}},t._l(t.stationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"工龄",clearable:""},model:{value:t.listQuery.workAge,callback:function(e){t.$set(t.listQuery,"workAge",t._n(e))},expression:"listQuery.workAge"}})],1),a("el-col",{staticClass:"filter-button",attrs:{span:8}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.showDialog()}}},[t._v("添加")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:t.downloadexceltemplate}},[t._v("下载模板")]),a("el-upload",{staticStyle:{"margin-left":"10px"},attrs:{action:"","http-request":t.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload2",type:"primary"},slot:"trigger"},[t._v("导入")])],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":t.sortChange}},[a("el-table-column",{attrs:{label:"岗位类型",sortable:"custom",prop:"Parent.Name"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.parentStationName))])]}}])}),a("el-table-column",{attrs:{label:"岗位",sortable:"custom",prop:"Station.Name"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.stationName))])]}}])}),a("el-table-column",{attrs:{label:"工龄",sortable:"custom",prop:"WorkAge"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.workAge))])]}}])}),a("el-table-column",{attrs:{label:"岗位津贴",sortable:"custom",prop:"Allowance"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(t._f("formatMoney2")(n.allowance)))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(e){return t.showDialog(n)}}},[t._v(" 编辑 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[10,20,50],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",attrs:{"parent-station-list":t.parentStationList},on:{refreshData:t.getPageList}})],1)},o=[],i=(a("ac1f"),a("841c"),a("d368")),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{attrs:{title:t.title,visible:t.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:t.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"岗位类型",prop:"parentStationId"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位类型",disabled:t.isEdit},on:{change:t.changeParentStation},model:{value:t.dataModel.parentStationId,callback:function(e){t.$set(t.dataModel,"parentStationId",e)},expression:"dataModel.parentStationId"}},t._l(t.parentStationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"岗位",prop:"stationId"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位",disabled:t.isEdit},model:{value:t.dataModel.stationId,callback:function(e){t.$set(t.dataModel,"stationId",e)},expression:"dataModel.stationId"}},t._l(t.stationList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"工龄",prop:"workAge"}},[a("el-input",{attrs:{placeholder:"工龄",disabled:t.isEdit,clearable:""},model:{value:t.dataModel.workAge,callback:function(e){t.$set(t.dataModel,"workAge",e)},expression:"dataModel.workAge"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"岗位津贴",prop:"allowance"}},[a("el-input",{attrs:{placeholder:"岗位津贴"},model:{value:t.dataModel.allowance,callback:function(e){t.$set(t.dataModel,"allowance",e)},expression:"dataModel.allowance"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.closeDialog}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnSaveLoading},on:{click:t.saveDialog}},[t._v("保 存")])],1)],1)],1)},l=[],s={props:{parentStationList:{type:Array,default:function(){return[]}}},data:function(){var t=function(t,e,a){/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/.test(e)?a():a(new Error("请输入0以上的数字"))},e=function(t,e,a){/^[+]{0,1}(\d+)$|^[+]{0,1}$/.test(e)?a():a(new Error("请输入正整数"))};return{showDialog:!1,title:"",stationList:[],rules:{parentStationId:[{required:!0,message:"请选择岗位类型",trigger:"change"}],stationId:[{required:!0,message:"请选择岗位",trigger:"change"}],workAge:[{required:!0,message:"请输入工龄",trigger:"blur"},{validator:e,trigger:"blur"}],allowance:[{required:!0,message:"请输入岗位津贴",trigger:"blur"},{validator:t,trigger:"blur"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{}}},created:function(){},methods:{initDialog:function(t){t?(this.title="编辑岗位津贴",this.isEdit=!0,this.getData(t.id)):(this.title="新增岗位津贴",this.isEdit=!1),this.showDialog=!0},getData:function(t){var e=this;i["a"].getStationAllowance({id:t}).then((function(t){t.succeed&&(e.dataModel=t.data,e.queryTwoLevelStation(e.dataModel.parentStationId))})).catch((function(t){}))},queryTwoLevelStation:function(t){var e=this;i["a"].queryTwoLevelStation({id:t}).then((function(t){e.stationList=t.data.datas})).catch((function(t){console.log(t)}))},changeParentStation:function(t){t?this.queryTwoLevelStation(t):(this.stationList=[],this.dataModel.stationId="")},saveDialog:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&(t.btnSaveLoading=!0,t.isEdit?i["a"].updateStationAllowance(t.dataModel).then((function(e){e.succeed&&(t.$message({message:"修改成功",type:"success"}),t.btnSaveLoading=!1,t.$emit("refreshData"),t.closeDialog())})).catch((function(e){t.btnSaveLoading=!1})):i["a"].addStationAllowance(t.dataModel).then((function(e){e.succeed&&(t.$message({message:"添加成功",type:"success"}),t.btnSaveLoading=!1,t.$emit("refreshData"),t.closeDialog())})).catch((function(e){t.btnSaveLoading=!1})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}},c=s,u=a("2877"),d=Object(u["a"])(c,r,l,!1,null,null,null),p=d.exports,f={components:{editDialog:p},data:function(){return{addForm:{},dataList:[],total:0,listQuery:{pageIndex:1,pageSize:10},parentStationList:[],stationList:[],listLoading:!1}},created:function(){this.queryOneLevelStation(),this.getPageList()},methods:{search:function(){this.listQuery.pageIndex=1,this.getPageList()},queryOneLevelStation:function(){var t=this;i["a"].queryOneLevelStation().then((function(e){t.parentStationList=e.data.datas})).catch((function(t){console.log(t)}))},sortChange:function(t){this.listQuery.pageIndex=1;var e="";"descending"===t.order&&(e="-"),"ascending"===t.order&&(e="+"),this.listQuery.order=e+t.prop,this.getPageList()},queryTwoLevelStation:function(t){var e=this;i["a"].queryTwoLevelStation({id:t}).then((function(t){e.stationList=t.data.datas})).catch((function(t){console.log(t)}))},changeParentStation:function(t){t?this.queryTwoLevelStation(t):(this.stationList=[],this.listQuery.stationId="")},importExcel:function(t){var e=this,a=t.file,n=new FormData;i["a"].importStationAllowance(a,n).then((function(t){t.succeed&&(e.$message({message:"导入成功",type:"success"}),e.search())})).catch((function(t){e.search()}))},exportExcel:function(){var t=this;i["a"].exportStationAllowance(this.listQuery).then((function(e){console.log(e);var n=a("19de"),o="岗位津贴"+t.$moment().format("YYYYMMDDHHmmss")+".xlsx";e.data?n(e.data,o):n(e,o)}))},downloadexceltemplate:function(){i["a"].downloadStationAllowanceTemplate().then((function(t){var e=a("19de"),n="StationAllowanceTemplate.xlsx";t.data?e(t.data,n):e(t,n)})).catch((function(t){}))},getPageList:function(){var t=this;this.listLoading=!0,i["a"].queryStationAllowance(this.listQuery).then((function(e){t.listLoading=!1,e.succeed?(t.dataList=e.data.datas,console.log(t.dataList),t.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e),t.listLoading=!1}))},showDialog:function(t){this.$refs.editDialog.initDialog(t)},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()}}},g=f,y=Object(u["a"])(g,n,o,!1,null,null,null);e["default"]=y.exports},d368:function(t,e,a){"use strict";var n=a("cfe3"),o="Organization",i=new n["a"](o);e["a"]={QueryOrganizationHiddenTop:function(t){return i.get("QueryOrganizationHiddenTop",t)},QueryOrganization:function(t){return i.get("QueryOrganization",t)},QueryDepartment:function(t){return i.get("QueryDepartment",t)},GetDepartment:function(t){return i.get("GetDepartment",t)},AddDepartment:function(t){return i.post("AddDepartment",t)},UpdateDepartment:function(t){return i.post("UpdateDepartment",t)},MoveDepartment:function(t){return i.post("MoveDepartment",t)},MergeDepartment:function(t){return i.post("MergeDepartment",t)},DeleteDepartment:function(t){return i.post("DeleteDepartment",t)},queryPosition:function(t){return i.post("QueryPosition",t)},getPosition:function(t){return i.get("GetPosition",t)},addPosition:function(t){return i.post("AddPosition",t)},updatePosition:function(t){return i.post("UpdatePosition",t)},deletePosition:function(t){return i.post("DeletePosition",t)},GetStation:function(t){return i.get("GetStation",t)},AddStation:function(t){return i.post("AddStation",t)},UpdateStation:function(t){return i.post("UpdateStation",t)},DeleteStation:function(t){return i.post("DeleteStation",t)},QueryPositionStationTree:function(t){return i.get("QueryPositionStationTree",t)},AllocatePosition:function(t){return i.post("AllocatePosition",t)},DeletePositionStation:function(t){return i.post("DeletePositionStation",t)},queryDeptByUser:function(t){return i.get("QueryDeptByUser",t)},queryOneLevelStation:function(){return i.get("QueryOneLevelStation")},queryTwoLevelStation:function(t){return i.get("QueryTwoLevelStation",t)},querySenioritySelect:function(){return i.get("QuerySenioritySelect")},queryStationAllowance:function(t){return i.get("QueryStationAllowance",t)},downloadStationAllowanceTemplate:function(t){return i.post("DownloadStationAllowanceTemplate",{data:t,responseType:"arraybuffer"})},importStationAllowance:function(t,e){return e.append("file",t),i.postForm("ImportStationAllowance",e)},exportStationAllowance:function(t){return i.post("ExportStationAllowance",{data:t,responseType:"arraybuffer"})},getStationAllowance:function(t){return i.get("GetStationAllowance",t)},addStationAllowance:function(t){return i.post("AddStationAllowance",t)},updateStationAllowance:function(t){return i.post("UpdateStationAllowance",t)},querySeniority:function(t){return i.get("QuerySeniority",t)},importSeniority:function(t,e){return e.append("file",t),i.postForm("ImportSeniority",e)},downloadSeniorityTemplate:function(t){return i.post("DownloadSeniorityTemplate",{data:t,responseType:"arraybuffer"})},exportSeniority:function(t){return i.post("ExportSeniority",{data:t,responseType:"arraybuffer"})},getSeniority:function(t){return i.get("GetSeniority",t)},addSeniority:function(t){return i.post("AddSeniority",t)},updateSeniority:function(t){return i.post("UpdateSeniority",t)},querySalaryScale:function(t){return i.get("QuerySalaryScale",t)},getSalaryScale:function(t){return i.get("GetSalaryScale",t)},addSalaryScale:function(t){return i.post("AddSalaryScale",t)},updateSalaryScale:function(t){return i.post("UpdateSalaryScale",t)},deleteSalaryScale:function(t){return i.post("DeleteSalaryScale",t)},importSalaryScale:function(t,e){return e.append("file",t),i.postForm("ImportSalaryScale",e)},exportSalaryScale:function(t){return i.post("ExportSalaryScale",{data:t,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(t){return i.get("QuerySalaryScaleByStationId",t)},queryStationAllowanceByStationId:function(t){return i.get("QueryStationAllowanceByStationId",t)},queryCarSubsidy:function(t){return i.get("QueryCarSubsidy",t)},getCarSubsidy:function(t){return i.get("GetCarSubsidy",t)},addCarSubsidy:function(t){return i.post("AddCarSubsidy",t)},updateCarSubsidy:function(t){return i.post("UpdateCarSubsidy",t)},deleteCarSubsidy:function(t){return i.post("DeleteCarSubsidy",t)},queryTelephoneFee:function(t){return i.get("QueryTelephoneFee",t)},getTelephoneFee:function(t){return i.get("GetTelephoneFee",t)},addTelephoneFee:function(t){return i.post("AddTelephoneFee",t)},updateTelephoneFee:function(t){return i.post("UpdateTelephoneFee",t)},deleteTelephoneFee:function(t){return i.post("DeleteTelephoneFee",t)}}}}]);