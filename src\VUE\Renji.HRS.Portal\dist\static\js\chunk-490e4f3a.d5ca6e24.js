(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-490e4f3a"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"841c":function(e,t,n){"use strict";var r=n("d784"),o=n("825a"),i=n("1d80"),l=n("129f"),a=n("14c3");r("search",1,(function(e,t,n){return[function(t){var n=i(this),r=void 0==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var i=o(e),u=String(this),d=i.lastIndex;l(d,0)||(i.lastIndex=0);var p=a(i,u);return l(i.lastIndex,d)||(i.lastIndex=d),null===p?-1:p.index}]}))},"8c24":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[n("el-form-item",[n("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),n("el-form-item",[n("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),n("el-form-item",[n("el-select",{attrs:{clearable:"",placeholder:"合同状态"},model:{value:e.listQuery.queryType,callback:function(t){e.$set(e.listQuery,"queryType",t)},expression:"listQuery.queryType"}},e._l(e.queryTypes,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-edit"},on:{click:e.remind}},[e._v("发送提醒")])],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{fixed:"",type:"selection",width:"40"}}),n("el-table-column",{attrs:{label:"唯一码",sortable:"custom",prop:"Employee.Uid"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empUid))])]}}])}),n("el-table-column",{attrs:{label:"工号",sortable:"custom",prop:"Employee.EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empCode))])]}}])}),n("el-table-column",{attrs:{label:"姓名",sortable:"custom",prop:"Employee.DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empName))])]}}])}),n("el-table-column",{attrs:{label:"合同开始日期",sortable:"custom",prop:"StartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.startDate?new Date(r.startDate).Format("yyyy-MM-dd"):""))])]}}])}),n("el-table-column",{attrs:{label:"合同结束日期",sortable:"custom",prop:"EndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.endDate?new Date(r.endDate).Format("yyyy-MM-dd"):""))])]}}])}),n("el-table-column",{attrs:{label:"试用期合同结束日期","min-width":"120px",sortable:"custom",prop:"ProEndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.proEndDate?new Date(r.proEndDate).Format("yyyy-MM-dd"):""))])]}}])}),n("el-table-column",{attrs:{label:"是否下次提醒",sortable:"custom",prop:"IsNextTime"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.isNextTime?"是":"否"))])]}}])}),n("el-table-column",{attrs:{label:"下次提醒时间",sortable:"custom",prop:"IsNextTime"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.reminderDate?new Date(r.reminderDate).Format("yyyy-MM-dd"):""))])]}}])}),n("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.sendRemindDialog(r)}}},[e._v(" 提醒 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),n("el-dialog",{attrs:{top:"5vh",title:"合同签订提醒",visible:e.sendRemindDialogVisible,width:"55%"},on:{"update:visible":function(t){e.sendRemindDialogVisible=t},close:e.closeSendRemindDialog}},[n("el-form",{ref:"ref_remindForm",attrs:{rules:e.rules,model:e.remindModel,"label-width":"140px"}},[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"唯一码",prop:"empUid"}},[e._v(" "+e._s(e.empContract.empUid)+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工号",prop:"empCode"}},[e._v(" "+e._s(e.empContract.empCode)+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"姓名",prop:"empName"}},[e._v(" "+e._s(e.empContract.empName)+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"合同开始日期",prop:"oldStartDate"}},[e._v(" "+e._s(e.empContract.oldStartDate?new Date(e.empContract.oldStartDate).Format("yyyy-MM-dd"):"")+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"合同结束日期",prop:"oldEndDate"}},[e._v(" "+e._s(e.empContract.oldEndDate?new Date(e.empContract.oldEndDate).Format("yyyy-MM-dd"):"")+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"试用期合同结束日期",prop:"proEndDate"}},[e._v(" "+e._s(e.empContract.proEndDate?new Date(e.empContract.proEndDate).Format("yyyy-MM-dd"):"")+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"是否发送提醒"}},[n("el-checkbox",{model:{value:e.remindModel.isSendRemind,callback:function(t){e.$set(e.remindModel,"isSendRemind",t)},expression:"remindModel.isSendRemind"}},[e._v("发送")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"是否临近退休"}},[n("el-checkbox",{on:{change:e.retireChange},model:{value:e.isNearRetirement,callback:function(t){e.isNearRetirement=t},expression:"isNearRetirement"}})],1)],1),e.remindModel.isSendRemind?e._e():n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"合同到期提醒时间",prop:"reminderDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择下次合同到期提醒时间"},model:{value:e.remindModel.reminderDate,callback:function(t){e.$set(e.remindModel,"reminderDate",t)},expression:"remindModel.reminderDate"}})],1)],1)],1),e.remindModel.isSendRemind?n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"合同起始日期",prop:"startDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择合同起始日期"},model:{value:e.remindModel.startDate,callback:function(t){e.$set(e.remindModel,"startDate",t)},expression:"remindModel.startDate"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"合同结束日期",prop:e.isNearRetirement?"":"endDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择合同结束日期"},model:{value:e.remindModel.endDate,callback:function(t){e.$set(e.remindModel,"endDate",t)},expression:"remindModel.endDate"}})],1)],1)],1):e._e()],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.sendRemindDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitRemindForm}},[e._v("保 存")])],1)],1),n("el-dialog",{attrs:{top:"5vh",title:"合同签订提醒",visible:e.dialogBatchRemindVisible,width:"55%"},on:{"update:visible":function(t){e.dialogBatchRemindVisible=t},close:e.closeBatchRemindDialog}},[n("el-form",{ref:"ref_batchRemindForm",attrs:{rules:e.rules,model:e.remindModel,"label-width":"140px"}},[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"是否发送提醒"}},[n("el-checkbox",{model:{value:e.remindModel.isSendRemind,callback:function(t){e.$set(e.remindModel,"isSendRemind",t)},expression:"remindModel.isSendRemind"}},[e._v("发送")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"是否临近退休"}},[n("el-checkbox",{on:{change:e.retireChange2},model:{value:e.isNearRetirement,callback:function(t){e.isNearRetirement=t},expression:"isNearRetirement"}})],1)],1),e.remindModel.isSendRemind?e._e():n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"合同到期提醒时间",prop:"reminderDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择下次合同到期提醒时间"},model:{value:e.remindModel.reminderDate,callback:function(t){e.$set(e.remindModel,"reminderDate",t)},expression:"remindModel.reminderDate"}})],1)],1)],1),e.remindModel.isSendRemind?n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"合同起始日期",prop:"startDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择合同起始日期"},model:{value:e.remindModel.startDate,callback:function(t){e.$set(e.remindModel,"startDate",t)},expression:"remindModel.startDate"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"合同结束日期",prop:e.isNearRetirement?"":"endDate"}},[n("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择合同结束日期"},model:{value:e.remindModel.endDate,callback:function(t){e.$set(e.remindModel,"endDate",t)},expression:"remindModel.endDate"}})],1)],1)],1):e._e()],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogBatchRemindVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.batchRemindForm}},[e._v("保 存")])],1)],1)],1)},o=[],i=(n("d81d"),n("d3b7"),n("ac1f"),n("841c"),n("e44c")),l={components:{},data:function(){var e=this,t=function(t,n,r){if(!n)return r();setTimeout((function(){var t=e.remindModel.endDate;if(t){var o=new Date(t),i=new Date(n);i<=o?r():r(new Error("合同起始日期不得晚于合同结束日期"))}else r()}),100)},n=function(t,n,r){if(!n)return r();setTimeout((function(){var t=e.remindModel.startDate;if(t){var o=new Date(t),i=new Date(n);i>=o?r():r(new Error("合同结束日期不得早于合同起始日期。"))}else r()}),100)};return{rules:{startDate:[{required:!0,message:"请选择合同起始日期",trigger:"blur"},{validator:t,trigger:"blur"}],endDate:[{required:!0,message:"请选择合同结束日期",trigger:"blur"},{validator:n,trigger:"blur"}],reminderDate:[{required:!0,message:"请选择合同结束日期",trigger:"blur"}]},sendRemindDialogVisible:!1,pageList:[],listQuery:{queryCondition:{},total:1,pageIndex:1,pageSize:10},listLoading:!1,queryTypes:[{id:1,name:"试用期合同"},{id:2,name:"到期合同"},{id:3,name:"下次提醒合同"}],empContract:{},remindModel:{},editIds:[],dialogBatchRemindVisible:!1,isNearRetirement:!1}},created:function(){this.getPageList()},methods:{sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.search()},search:function(){this.listQuery.pageIndex=1,this.getPageList()},getPageList:function(){var e=this;this.listLoading=!0,i["a"].queryRenewEmployeeContract(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},sendRemindDialog:function(e){this.remindModel={id:e.id,isSendRemind:!0},this.empContract={empUid:e.empUid,empCode:e.empCode,empName:e.empName,oldStartDate:e.startDate,oldEndDate:e.endDate,proEndDate:e.proEndDate},this.sendRemindDialogVisible=!0},closeSendRemindDialog:function(){this.$refs["ref_remindForm"].resetFields(),this.$refs["ref_remindForm"].clearValidate()},submitRemindForm:function(){var e=this;this.$refs["ref_remindForm"].validate((function(t){t&&(e.remindModel.isSendRemind?i["a"].sendEmailForRenewRemind(e.remindModel).then((function(t){t.succeed?(e.sendRemindDialogVisible=!1,e.search(),e.$notice.message("邮件提醒发送成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})):i["a"].updateNextTimeRemind(e.remindModel).then((function(t){t.succeed?(e.sendRemindDialogVisible=!1,e.search(),e.$notice.message("已更改为下个月提醒","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})))}))},handleSelectionChange:function(e){this.editIds=e.map((function(e){return e.id}))},remind:function(){this.editIds.length>0?this.dialogBatchRemindVisible=!0:this.$notice.message("请选择合同到期人员","warning")},closeBatchRemindDialog:function(){this.$refs["ref_batchRemindForm"].resetFields(),this.$refs["ref_batchRemindForm"].clearValidate()},batchRemindForm:function(){var e=this;this.$refs["ref_batchRemindForm"].validate((function(t){t&&(e.remindModel.Ids=e.editIds,e.remindModel.isSendRemind?i["a"].batchSendEmailForRenewRemind(e.remindModel).then((function(t){t.succeed?(e.dialogBatchRemindVisible=!1,e.search(),e.$notice.message("邮件提醒发送成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})):i["a"].batchUpdateNextTimeRemind(e.remindModel).then((function(t){t.succeed?(e.dialogBatchRemindVisible=!1,e.search(),e.$notice.message("已更改为下个月提醒","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})))}))},retireChange:function(){this.isNearRetirement&&this.$refs["ref_remindForm"].clearValidate("endDate")},retireChange2:function(){this.isNearRetirement&&this.$refs["ref_batchRemindForm"].clearValidate("endDate")}}},a=l,u=n("2877"),d=Object(u["a"])(a,r,o,!1,null,"16404c28",null);t["default"]=d.exports},e44c:function(e,t,n){"use strict";n("4160"),n("b64b"),n("159b");var r=n("cfe3"),o="HR",i=new r["a"](o);t["a"]={queryEmployee:function(e){return i.get("QueryEmployee",e)},queryEmployeeByCommonCondition:function(e){return i.post("QueryEmployeeByCommonCondition",e)},queryEmployeeStatus:function(){return i.get("QueryEmployeeStatus")},queryRank:function(){return i.get("QueryRank")},queryAdministrativePosition:function(){return i.get("queryAdministrativePosition")},queryMajorTechnical:function(){return i.get("queryMajorTechnical")},queryOfficialRank:function(){return i.get("QueryOfficialRank")},queryHireStyle:function(){return i.get("QueryHireStyle")},queryLeaveStyle:function(){return i.get("QueryLeaveStyle")},queryMarryList:function(){return i.get("QueryMarryList")},queryNationality:function(){return i.get("QueryNationality")},queryRegisterType:function(){return i.get("QueryRegisterType")},deleteEmployee:function(e){return i.post("DeleteEmployee",e)},queryDocumentType:function(){return i.get("QueryDocumentType")},addEmployee:function(e){return i.post("AddEmployee",e)},isEmpCodeExists:function(e){var t={id:e.id,empCode:e.empCode};return i.get("IsEmpCodeExists",t)},checkIdentityNumber:function(e){var t={id:e.id,identityNumber:e.identityNumber};return i.get("checkIdentityNumber",t)},getEmployee:function(e){return i.get("GetEmployee",e)},updateEmployee:function(e){return i.post("UpdateEmployee",e)},getPersonnelInformation:function(e){return i.get("GetPersonnelInformation",e)},postAddDocumentInformation:function(e){return i.get("PostAddDocumentInformation",e)},postUpdateDocumentInformation:function(e){return i.get("PostUpdateDocumentInformation",e)},postDeleteDocumentInformation:function(e){return i.get("PostDeleteDocumentInformation",e)},queryEmployeeByConditions:function(e){return i.post("QueryEmployeeByConditions",e)},queryEmployeeByCommonConditions:function(e){return i.post("QueryEmployeeByCommonConditions",e)},queryEmployeeQuerySetting:function(e){return i.get("QueryEmployeeQuerySetting",e)},querySettingColumns:function(){return i.get("QueryEmployeeInfoQuerySettingColumns")},queryOperations:function(e){return i.get("QueryOperationByColumnType",e)},addEmployeeQuerySetting:function(e){return i.post("AddEmployeeQuerySetting",e)},deleteEmployeeQuerySetting:function(e){return i.post("DeleteEmployeeQuerySetting",e)},queryDegrees:function(){return i.get("QueryDegrees")},queryEducation:function(){return i.get("QueryEducation")},QuerySocialSecurity:function(){return i.get("QuerySocialSecurity")},queryParty:function(){return i.get("QueryParty")},queryRecruitmentCategory:function(){return i.get("QueryRecruitmentCategory")},queryRecruitmentCompany:function(){return i.get("QueryRecruitmentCompany")},getEmployeeHR:function(e){return i.get("GetEmployeeHR",e)},updateEmployeeHR:function(e){return i.post("UpdateEmployeeHR",e)},updateCompanyAge:function(){return i.post("UpdateCompanyAge")},calculateGeneralHoliday:function(){return i.get("CalculateGeneralHoliday")},queryStation:function(e){return i.get("QueryStation",e)},queryPositionStation:function(e){return i.get("QueryPositionStation",e)},queryEmployeeStation:function(e){return i.get("QueryEmployeeStation",e)},addEmployeeStation:function(e){return i.post("AddEmployeeStation",e)},updateEmployeeStation:function(e){return i.post("UpdateEmployeeStation",e)},deleteEmployeeStation:function(e){return i.post("DeleteEmployeeStation",e)},queryLevel:function(){return i.get("QueryLevel")},queryEmployeeCertify:function(e){return i.get("QueryEmployeeCertify",e)},addEmployeeCertify:function(e){return i.post("AddEmployeeCertify",e)},updateEmployeeCertify:function(e){return i.post("UpdateEmployeeCertify",e)},deleteEmployeeCertify:function(e){return i.post("DeleteEmployeeCertify",e)},queryGraduation:function(){return i.get("QueryGraduation")},queryLearnWay:function(){return i.get("QueryLearnWay")},queryEmployeeEducation:function(e){return i.get("QueryEmployeeEducation",e)},addEmployeeEducation:function(e){return i.post("AddEmployeeEducation",e)},updateEmployeeEducation:function(e){return i.post("UpdateEmployeeEducation",e)},deleteEmployeeEducation:function(e){return i.post("DeleteEmployeeEducation",e)},queryEmployeeWork:function(e){return i.get("QueryEmployeeWork",e)},addEmployeeWork:function(e){return i.post("AddEmployeeWork",e)},updateEmployeeWork:function(e){return i.post("UpdateEmployeeWork",e)},deleteEmployeeWork:function(e){return i.post("DeleteEmployeeWork",e)},queryAbroadType:function(){return i.get("QueryAbroadType")},queryEmployeeAbroad:function(e){return i.get("QueryEmployeeAbroadInfo",e)},addEmployeeAbroad:function(e){return i.post("AddEmployeeAbroadInfo",e)},updateEmployeeAbroad:function(e){return i.post("UpdateEmployeeAbroadInfo",e)},deleteEmployeeAbroad:function(e){return i.post("DeleteEmployeeAbroadInfo",e)},queryContractType:function(){return i.get("QueryContractType")},queryEmployeeContract:function(e){return i.get("QueryEmployeeContract",e)},queryRenewEmployeeContract:function(e){return i.get("QueryRenewEmployeeContract",e)},updateNextTimeRemind:function(e){return i.post("UpdateNextTimeRemind",e)},sendEmailForRenewRemind:function(e){return i.post("SendEmailForRenewRemind",e)},batchUpdateNextTimeRemind:function(e){return i.post("BatchUpdateNextTimeRemind",e)},batchSendEmailForRenewRemind:function(e){return i.post("BatchSendEmailForRenewRemind",e)},addEmployeeContract:function(e){return i.post("AddEmployeeContract",e)},updateEmployeeContract:function(e){return i.post("UpdateEmployeeContract",e)},deleteEmployeeContract:function(e){return i.post("DeleteEmployeeContract",e)},queryTrainLevel:function(){return i.get("QueryTrainLevel")},queryEmployeeTrain:function(e){return i.get("QueryEmployeeTrain",e)},addEmployeeTrain:function(e){return i.post("AddEmployeeTrain",e)},updateEmployeeTrain:function(e){return i.post("UpdateEmployeeTrain",e)},deleteEmployeeTrain:function(e){return i.post("DeleteEmployeeTrain",e)},queryYearList:function(){return i.get("QueryYearList")},queryEvaluateResult:function(){return i.get("QueryEvaluateResult")},queryEmployeeAssessment:function(e){return i.get("QueryEmployeeAssessment",e)},addEmployeeAssessment:function(e){return i.post("AddEmployeeAssessment",e)},updateEmployeeAssessment:function(e){return i.post("UpdateEmployeeAssessment",e)},deleteEmployeeAssessment:function(e){return i.post("DeleteEmployeeAssessment",e)},queryEmployeeDeduct:function(e){return i.get("QueryEmployeeDeduct",e)},addEmployeeDeduct:function(e){return i.post("AddEmployeeDeduct",e)},updateEmployeeDeduct:function(e){return i.get("UpdateEmployeeDeduct",e)},deleteEmployeeDeduct:function(e){return i.post("DeleteEmployeeDeduct",e)},downloadEmployeeDeductTemplate:function(e){return i.getFile("DownlodaEmployeeDeductTemplate",e)},importEmployeeDeduct:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),i.postForm("ImportEmployeeDeduct",n)},queryEmployeeDeductUnCalculate:function(e){return i.get("QueryEmployeeDeductUnCalculate",e)},queryEmployeeDeductCalculate:function(e){return i.get("QueryEmployeeDeductCalculate",e)},getEmployeeDeductCalculate:function(e){return i.get("GetEmployeeDeductCalculate",e)},updateEmployeeDeductCalculate:function(e){return i.post("UpdateEmployeeDeductCalculate",e)},exportEmployeeDeductCalculate:function(e){return i.post("ExportEmployeeDeductCalculate",{data:e,responseType:"arraybuffer"})},queryEmployeeDeductWorkingAge:function(e){return i.get("QueryEmployeeDeductWorkingAge",e)},getEmployeeDeductWorkingAge:function(e){return i.get("GetEmployeeDeductWorkingAge",e)},addEmployeeDeductWorkingAge:function(e){return i.post("AddEmployeeDeductWorkingAge",e)},updateEmployeeDeductWorkingAge:function(e){return i.post("UpdateEmployeeDeductWorkingAge",e)},deleteEmployeeDeductWorkingAge:function(e){return i.post("DeleteEmployeeDeductWorkingAge",e)},queryIncentType:function(){return i.get("QueryIncentType")},queryIncentLevel:function(){return i.get("QueryIncentLevel")},queryEmployeeIncentive:function(e){return i.get("QueryEmployeeIncentive",e)},addEmployeeIncentive:function(e){return i.post("AddEmployeeIncentive",e)},updateEmployeeIncentive:function(e){return i.post("UpdateEmployeeIncentive",e)},deleteEmployeeIncentive:function(e){return i.post("DeleteEmployeeIncentive",e)},getEmployeeHealth:function(e){return i.get("GetEmployeeHealth",e)},updateEmployeeHealth:function(e){return i.post("UpdateEmployeeHealth",e)},queryAccidentType:function(){return i.get("QueryAccidentType")},queryEmployeeAccident:function(e){return i.get("QueryEmployeeAccident",e)},addEmployeeAccident:function(e){return i.post("AddEmployeeAccident",e)},updateEmployeeAccident:function(e){return i.post("UpdateEmployeeAccident",e)},deleteEmployeeAccident:function(e){return i.post("DeleteEmployeeAccident",e)},queryEmployeeTeach:function(e){return i.get("QueryEmployeeTeach",e)},addEmployeeTeach:function(e){return i.post("AddEmployeeTeach",e)},updateEmployeeTeach:function(e){return i.post("UpdateEmployeeTeach",e)},deleteEmployeeTeach:function(e){return i.post("DeleteEmployeeTeach",e)},queryEmployeeRelation:function(e){return i.get("QueryEmployeeRelation",e)},addEmployeeRelation:function(e){return i.post("AddEmployeeRelation",e)},updateEmployeeRelation:function(e){return i.post("UpdateEmployeeRelation",e)},deleteEmployeeRelation:function(e){return i.post("DeleteEmployeeRelation",e)},queryIncomeType:function(){return i.get("QueryIncomeType")},queryEmployeeArticle:function(e){return i.get("QueryEmployeeArticle",e)},addEmployeeArticle:function(e){return i.post("AddEmployeeArticle",e)},updateEmployeeArticle:function(e){return i.post("UpdateEmployeeArticle",e)},deleteEmployeeArticle:function(e){return i.post("DeleteEmployeeArticle",e)},queryClassLevel:function(){return i.get("QueryClassLevel")},queryEmployeeClass:function(e){return i.get("QueryEmployeeClass",e)},addEmployeeClass:function(e){return i.post("AddEmployeeClass",e)},updateEmployeeClass:function(e){return i.post("UpdateEmployeeClass",e)},deleteEmployeeClass:function(e){return i.post("DeleteEmployeeClass",e)},queryEmployeePatent:function(e){return i.get("QueryEmployeePatent",e)},addEmployeePatent:function(e){return i.post("AddEmployeePatent",e)},updateEmployeePatent:function(e){return i.post("UpdateEmployeePatent",e)},deleteEmployeePatent:function(e){return i.post("DeleteEmployeePatent",e)},queryTeacherType:function(){return i.get("QueryTeacherType")},queryEmployeeTeacher:function(e){return i.get("QueryEmployeeTeacher",e)},addEmployeeTeacher:function(e){return i.post("AddEmployeeTeacher",e)},updateEmployeeTeacher:function(e){return i.post("UpdateEmployeeTeacher",e)},deleteEmployeeTeacher:function(e){return i.post("DeleteEmployeeTeacher",e)},queryAwardLevel:function(){return i.get("QueryAwardLevel")},queryDictByParentCode:function(e){return i.get("QueryDictByParentCode",e)},queryHighTalent:function(){return i.get("QueryHighTalent")},queryEmployeeAward:function(e){return i.get("QueryEmployeeAward",e)},addEmployeeAward:function(e){return i.post("AddEmployeeAward",e)},updateEmployeeAward:function(e){return i.post("UpdateEmployeeAward",e)},deleteEmployeeAward:function(e){return i.post("DeleteEmployeeAward",e)},addEmployeeHighTalent:function(e){return i.post("AddEmployeeHighTalent",e)},updateEmployeeHighTalent:function(e){return i.post("UpdateEmployeeHighTalent",e)},queryEmployeeHighTalent:function(e){return i.get("QueryEmployeeHighTalent",e)},deleteEmployeeHighTalent:function(e){return i.post("DeleteEmployeeHighTalent",e)},queryEmployeeDeptHistory:function(e){return i.get("QueryEmployeeDeptHistory",e)},updateEmployeeDept:function(e){return i.post("UpdateEmployeeDept",e)},batchUpdateEmployeeDept:function(e){return i.post("BatchUpdateEmployeeDept",e)},queryEmployeeList:function(e){return i.post("QueryEmployeeList",e)},queryEmployeeListTree:function(e){return i.get("QueryEmployeeListTree",e)},queryDictsSetting:function(e){return i.get("QueryDictsSetting",e)},queryEmployeeListSettingTree:function(e){return i.get("QueryEmployeeListSettingTree",e)},queryAdvancedQueryType:function(){return i.get("QueryAdvancedQueryType")},saveEmployeeList:function(e){return i.post("SaveEmployeeList",e)},deleteEmployeeList:function(e){return i.post("DeleteEmployeeList",e)},getEmployeeList:function(e){return i.get("GetEmployeeList",e)},getEmployeeListExcel:function(e){return i.getFile("GetEmployeeListExcel",e)},queryDeptPrincipal:function(e){return i.get("QueryDeptPrincipal",e)},queryOtherEmployeeInfo:function(e){return i.post("QueryOtherEmployeeInfo",e)},saveOtherEmployeeInfo:function(e){return i.post("SaveOtherEmployeeInfo",e)},deleteOtherEmployeeInfo:function(e){return i.post("DeleteOtherEmployeeInfo",e)},queryOtherEmpTypes:function(e){return i.get("QueryOtherEmpTypes",e)},importExcel:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),i.postForm("ImportExcel",n)},downlodaImportExcelTemplate:function(e){return i.getFile("DownlodaImportExcelTemplate",e)},getSocialSecurityInfo:function(e){return i.get("getSocialSecurityInfo",e)},updateEmployeeSocialSecurity:function(e){return i.post("updateEmployeeSocialSecurity",e)},queryPayRollOrgClass:function(){return i.get("QueryPayRollOrgClass")},queryPayRollCompGroup:function(){return i.get("QueryPayRollCompGroup")},queryPayRollOrgSalary:function(e){return i.get("QueryPayRollOrgSalary",e)},queryPayRollOrgSalaryLevel:function(e){return i.get("QueryPayRollOrgSalaryLevel",e)},queryPayRollOrgPositionSalarys:function(e){return i.get("QueryPayRollOrgPositionSalarys",e)},queryWorkState:function(){return i.get("QueryWorkState")},getWagesInfo:function(e){return i.get("GetWagesInfo",e)},updateEmployeeBenefit:function(e){return i.post("UpdateEmployeeBenefit",e)},queryWagesHistory:function(e){return i.get("QueryWagesHistory",e)},getEmployeePayrollPrint:function(e){return i.get("GetEmployeePayrollPrint",e)},updateEmployeePayrollPrint:function(e){return i.post("UpdateEmployeePayrollPrint",e)},deleteEmployeePayrollPrint:function(e){return i.post("DeleteEmployeePayrollPrint",e)},getPrintDetailsInfo:function(e){return i.get("GetPrintDetailsInfo",e)},getEmployeeWage:function(e){return i.get("GetEmployeeWage",e)},editEmployeeWage:function(e){return i.post("EditEmployeeWage",e)},editEmployeeParty:function(e){return i.post("EditEmployeeParty",e)},queryEmployeeHRAffiliatedBranch:function(e){return i.get("QueryEmployeeHRAffiliatedBranch",e)},getEmployeeHRAffiliatedBranch:function(e){return i.get("GetEmployeeHRAffiliatedBranch",e)},addEmployeeHRAffiliatedBranch:function(e){return i.post("AddEmployeeHRAffiliatedBranch",e)},updateEmployeeHRAffiliatedBranch:function(e){return i.post("UpdateEmployeeHRAffiliatedBranch",e)},deleteEmployeeHRAffiliatedBranch:function(e){return i.post("DeleteEmployeeHRAffiliatedBranch",e)},queryEmployeeHRDict:function(e){return i.get("QueryEmployeeHRDict",e)},getEmployeeHRDict:function(e){return i.get("GetEmployeeHRDict",e)},addEmployeeHRDict:function(e){return i.post("AddEmployeeHRDict",e)},updateEmployeeHRDict:function(e){return i.post("UpdateEmployeeHRDict",e)},deleteEmployeeHRDict:function(e){return i.post("DeleteEmployeeHRDict",e)},queryEmployeeHRPartyMemberHonor:function(e){return i.get("QueryEmployeeHRPartyMemberHonor",e)},getEmployeeHRPartyMemberHonor:function(e){return i.get("GetEmployeeHRPartyMemberHonor",e)},addEmployeeHRPartyMemberHonor:function(e){return i.post("AddEmployeeHRPartyMemberHonor",e)},updateEmployeeHRPartyMemberHonor:function(e){return i.post("UpdateEmployeeHRPartyMemberHonor",e)},deleteEmployeeHRPartyMemberHonor:function(e){return i.post("DeleteEmployeeHRPartyMemberHonor",e)}}}}]);