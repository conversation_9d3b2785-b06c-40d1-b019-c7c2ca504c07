(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ce671318"],{1058:function(e,t,a){"use strict";var o=a("6d4b"),r=a.n(o);r.a},"12b0":function(e,t,a){},"12ed":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",{attrs:{type:"flex",justify:"end"}},[e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1):e._e(),e.userPermission.isShowBtnClear?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document-delete"},on:{click:e.clear}},[e._v("清除")])],1):e._e(),e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate1}},[e._v("模板下载")])],1):e._e(),e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel1,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("社保信息")])]),a("el-form",{ref:"dataFormSocialSecurity",attrs:{rules:e.rulesSocialSecurity,model:e.tempData.tempFormSocialSecurityModel,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"社保账号",prop:"annuityAccount"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入社保账号"},model:{value:e.tempData.tempFormSocialSecurityModel.annuityAccount,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"annuityAccount",t)},expression:"tempData.tempFormSocialSecurityModel.annuityAccount"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"社保缴费基数",prop:"annuityCurr"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入社保缴费基数"},model:{value:e.tempData.tempFormSocialSecurityModel.annuityCurr,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"annuityCurr",e._n(t))},expression:"\n                tempData.tempFormSocialSecurityModel.annuityCurr\n              "}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"社保类型",prop:"siType"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择社保类型"},model:{value:e.tempData.tempFormSocialSecurityModel.siType,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"siType",t)},expression:"tempData.tempFormSocialSecurityModel.siType"}},e._l(e.SocialSecurityOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1)],1)],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("财务信息")])]),a("el-form",{ref:"dataFormFinance",attrs:{rules:e.rulesFinance,model:e.tempData.tempFormSocialSecurityModel,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工资单编号",prop:"paySlipNumber"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入工资单编号"},model:{value:e.tempData.tempFormSocialSecurityModel.paySlipNumber,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"paySlipNumber",t)},expression:"tempData.tempFormSocialSecurityModel.paySlipNumber"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"公积金账号",prop:"accumFundAccount"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入公积金账号"},model:{value:e.tempData.tempFormSocialSecurityModel.accumFundAccount,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"accumFundAccount",t)},expression:"tempData.tempFormSocialSecurityModel.accumFundAccount"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"公积金缴费基数",prop:"accumFundCurr"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入公积金缴费基数"},model:{value:e.tempData.tempFormSocialSecurityModel.accumFundCurr,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"accumFundCurr",e._n(t))},expression:"\n                tempData.tempFormSocialSecurityModel.accumFundCurr\n              "}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"银行名称一",prop:"bankName1"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入银行名称一"},model:{value:e.tempData.tempFormSocialSecurityModel.bankName1,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"bankName1",t)},expression:"tempData.tempFormSocialSecurityModel.bankName1"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"银行名称二",prop:"bankName2"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入银行名称二"},model:{value:e.tempData.tempFormSocialSecurityModel.bankName2,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"bankName2",t)},expression:"tempData.tempFormSocialSecurityModel.bankName2"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"银行名称三",prop:"bankName3"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入银行名称三"},model:{value:e.tempData.tempFormSocialSecurityModel.bankName3,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"bankName3",t)},expression:"tempData.tempFormSocialSecurityModel.bankName3"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"银行账号一",prop:"bankAccount1"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入银行账号一"},model:{value:e.tempData.tempFormSocialSecurityModel.bankAccount1,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"bankAccount1",t)},expression:"tempData.tempFormSocialSecurityModel.bankAccount1"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"银行账号二",prop:"bankAccount2"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入银行账号二"},model:{value:e.tempData.tempFormSocialSecurityModel.bankAccount2,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"bankAccount2",t)},expression:"tempData.tempFormSocialSecurityModel.bankAccount2"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"银行账号三",prop:"bankAccount3"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入银行账号三"},model:{value:e.tempData.tempFormSocialSecurityModel.bankAccount3,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"bankAccount3",t)},expression:"tempData.tempFormSocialSecurityModel.bankAccount3"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"免个人所得税",prop:"dispenseTax"}},[a("el-checkbox",{model:{value:e.tempData.tempFormSocialSecurityModel.dispenseTax,callback:function(t){e.$set(e.tempData.tempFormSocialSecurityModel,"dispenseTax",t)},expression:"tempData.tempFormSocialSecurityModel.dispenseTax"}})],1)],1)],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{rulesSocialSecurity:{annuityAccount:[{max:50,type:"string",message:"社保账号不允许超过50位字符",trigger:"change"}],annuityCurr:[{required:!0,type:"number",min:0,max:**************,message:"社保缴费基数必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}]},rulesFinance:{paySlipNumber:[{max:50,type:"string",message:"工资单号不允许超过50位字符",trigger:"blur"}],accumFundAccount:[{max:50,type:"string",message:"公积金账号不允许超过50位字符",trigger:"change"}],accumFundCurr:[{required:!0,type:"number",min:0,max:**************,message:"公积金缴费基数必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],bankName1:[{max:50,type:"string",message:"银行名称一长度不可超过50位",trigger:"change"}],bankName2:[{max:50,type:"string",message:"银行名称二长度不可超过50位",trigger:"change"}],bankName3:[{max:50,type:"string",message:"银行名称三长度不可超过50位",trigger:"change"}],bankAccount1:[{max:50,type:"string",message:"银行账号一长度不可超过50位",trigger:"change"}],bankAccount2:[{max:50,type:"string",message:"银行账号二长度不可超过50位",trigger:"change"}],bankAccount3:[{max:50,type:"string",message:"银行账号三长度不可超过50位",trigger:"change"}]},tempData:{tempFormSocialSecurityModel:{}},SocialSecurityOptions:[]}},created:function(){this.init(),this.loadSocialSecurityOptions()},methods:{init:function(){this.empId&&this.getSocialSecurityInfo(this.empId)},getSocialSecurityInfo:function(e){var t=this;l["a"].getSocialSecurityInfo({id:e}).then((function(e){e.data&&(t.tempData.tempFormSocialSecurityModel=e.data)}))},loadSocialSecurityOptions:function(){var e=this;l["a"].QuerySocialSecurity().then((function(t){e.SocialSecurityOptions=t.data.datas})).catch((function(e){}))},save:function(){var e=!1,t=!1;this.$refs["dataFormSocialSecurity"].validate((function(t){e=t})),this.$refs["dataFormFinance"].validate((function(e){t=e})),e&&t&&this.update()},update:function(){var e=this;this.tempData.tempFormSocialSecurityModel.ID=this.empId,l["a"].updateEmployeeSocialSecurity(this.tempData.tempFormSocialSecurityModel).then((function(t){t.succeed?e.$notice.message("修改成功","success"):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){this.$refs["dataFormSocialSecurity"].resetFields(),this.$refs["dataFormFinance"].resetFields();var e=this.tempData.tempFormSocialSecurityModel.ID;e&&(this.tempData.tempFormSocialSecurityModel.ID=e),this.tempData.tempFormSocialSecurityModel.dispenseTax=!1},downloadexceltemplate1:function(){l["a"].downlodaImportExcelTemplate({type:"importempsoc"}).then((function(e){var t=a("19de"),o="EmployeeSocialInsuranceTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel1:function(e){var t=this,a=e.file;l["a"].importExcel(a,{type:"importempsoc"}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a)}})).catch((function(e){}))}}},n=i,s=(a("4c5f"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"8a25e7d6",null);t["a"]=c.exports},"185f":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-card",[a("el-form",{ref:"dataFormParty",attrs:{rules:e.rules,model:e.tempFormModelParty,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"政治面貌",prop:"partyId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择政治面貌"},model:{value:e.tempFormModelParty.partyId,callback:function(t){e.$set(e.tempFormModelParty,"partyId",t)},expression:"tempFormModelParty.partyId"}},e._l(e.partyList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{prop:"partyJoiningTime",label:"入党时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择入党时间","value-format":"yyyy-MM-dd"},model:{value:e.tempFormModelParty.partyJoiningTime,callback:function(t){e.$set(e.tempFormModelParty,"partyJoiningTime",t)},expression:"tempFormModelParty.partyJoiningTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{prop:"probationTime",label:"转正时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择转正时间","value-format":"yyyy-MM-dd"},model:{value:e.tempFormModelParty.probationTime,callback:function(t){e.$set(e.tempFormModelParty,"probationTime",t)},expression:"tempFormModelParty.probationTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{prop:"officeTime",label:"任职时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择任职时间","value-format":"yyyy-MM-dd"},model:{value:e.tempFormModelParty.officeTime,callback:function(t){e.$set(e.tempFormModelParty,"officeTime",t)},expression:"tempFormModelParty.officeTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"组织关系",prop:"organization"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"组织关系",maxlength:"50"},model:{value:e.tempFormModelParty.organization,callback:function(t){e.$set(e.tempFormModelParty,"organization",t)},expression:"tempFormModelParty.organization"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"党费标准",prop:"partyFeeStandard"}},[a("el-input",{staticClass:"numrule",staticStyle:{width:"100%"},attrs:{type:"number",placeholder:"党费标准"},on:{change:e.partyFeeStandardChange},model:{value:e.tempFormModelParty.partyFeeStandard,callback:function(t){e.$set(e.tempFormModelParty,"partyFeeStandard",t)},expression:"tempFormModelParty.partyFeeStandard"}})],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"10px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("所在支部")]),this.tempFormModelParty.id?a("el-button",{staticStyle:{float:"right","margin-top":"-5px"},attrs:{type:"primary",size:"mini",icon:"el-icon-plus eltablei"},on:{click:e.addAffiliatedBranch}},[e._v("添加")]):e._e()],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.affiliatedBranchList,stripe:"",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":e.handleRowClass}},[a("el-table-column",{attrs:{label:"所在支部","min-width":"100px","header-align":"left",align:"left","header-class-name":"star"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.affiliatedBranch))])]}}])}),a("el-table-column",{attrs:{label:"原党组织","min-width":"100px","header-align":"left",align:"left","header-class-name":"star"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.origPartyOrganization))])]}}])}),a("el-table-column",{attrs:{label:"转入时间","min-width":"100px","header-align":"center",align:"center","header-class-name":"star"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.transferTime?new Date(o.transferTime).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"转出时间","min-width":"100px","header-align":"center",align:"center","header-class-name":"star"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.transferOutTime?new Date(o.transferOutTime).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"150px","header-align":"left ",align:"left ","header-class-name":"star "},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{staticStyle:{"white-space":"pre-wrap"}},[e._v(e._s(o.remark))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center",width:"170","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.updateAffiliatedBranch(o)}}},[e._v(" 编辑 ")]),a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.deleteAffiliatedBranch(o)}}},[e._v(" 删除 ")])]}}])})],1)],1)],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"10px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("党内职务")]),this.tempFormModelParty.id?a("el-button",{staticStyle:{float:"right","margin-top":"-5px"},attrs:{type:"primary",size:"mini",icon:"el-icon-plus eltablei"},on:{click:e.addEmployeeHRDict}},[e._v("添加")]):e._e()],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.employeeHRDictList,stripe:"",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":e.handleRowClass}},[a("el-table-column",{attrs:{label:"当前组织","min-width":"100px","header-align":"left",align:"left","header-class-name":"star"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.organizationName))])]}}])}),a("el-table-column",{attrs:{label:"党内职务","min-width":"100px ","header-align":"left ",align:"left ","header-class-name":"star "},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dictName))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center",width:"170","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.updateEmployeeHRDict(o)}}},[e._v(" 编辑 ")]),a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.deleteEmployeeHRDict(o)}}},[e._v(" 删除 ")])]}}])})],1)],1)],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"10px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("党员奖惩")]),this.tempFormModelParty.id?a("el-button",{staticStyle:{float:"right","margin-top":"-5px"},attrs:{type:"primary",size:"mini",icon:"el-icon-plus eltablei"},on:{click:e.addPartyMemberHonor}},[e._v("添加")]):e._e()],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.partyMemberHonorList,stripe:"",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":e.handleRowClass}},[a("el-table-column",{attrs:{label:"荣誉/处分类型","min-width":"100px","header-align":"left",align:"left","header-class-name":"star"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.honorOrPunish))])]}}])}),a("el-table-column",{attrs:{label:"颁奖单位/处分单位","min-width":"100px","header-align":"left",align:"left","header-class-name":"star"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.honorOrPunishCompany))])]}}])}),a("el-table-column",{attrs:{label:"时间","min-width":"100px","header-align":"center",align:"center","header-class-name":"star"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.time?new Date(o.time).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"150px","header-align":"left ",align:"left ","header-class-name":"star "},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{staticStyle:{"white-space":"pre-wrap"}},[e._v(e._s(o.remark))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center",width:"170","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.updatePartyMemberHonor(o)}}},[e._v(" 编辑 ")]),a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.deletePartyMemberHonor(o)}}},[e._v(" 删除 ")])]}}])})],1)],1)],1)],1)],1)],1)],1),e.dialogEditAffiliatedBranchVisible?a("modifyAffiliatedBranch",{attrs:{id:e.affiliatedBranchId,"employee-h-r-id":this.empId,title:e.modifyAffiliatedBranchDialogTitle},on:{hidden:function(t){return e.onAffiliatedBranchHidden()},refresh:function(t){return e.onAffiliatedBranchRefresh()}}}):e._e(),e.dialogEditEmployeeHRDictVisible?a("modifyPartyOrganizations",{attrs:{id:e.employeeHRDictId,"employee-h-r-id":this.empId,title:e.modifyEmployeeHRDictDialogTitle},on:{hidden:function(t){return e.onEmployeeHRDictHidden()},refresh:function(t){return e.onEmployeeHRDictRefresh()}}}):e._e(),e.dialogEditPartyMemberHonorVisible?a("modifyPartyMemberHonor",{attrs:{id:e.partyMemberHonorId,"employee-h-r-id":this.empId,title:e.modifyPartyMemberHonorDialogTitle},on:{hidden:function(t){return e.onPartyMemberHonorHidden()},refresh:function(t){return e.onPartyMemberHonorRefresh()}}}):e._e()],1)},r=[],l=(a("a9e3"),a("b680"),a("ac1f"),a("5319"),a("e44c")),i=(a("cfa9"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{"append-to-body":"",title:e.title,width:"60%","close-on-click-modal":!1,visible:!0},on:{close:function(t){return e.cancle()}}},[a("el-form",{ref:"dataForm",staticClass:"el-dialogform",attrs:{rules:e.rules,model:e.tempFormModel,"label-position":"right","label-width":"80px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{prop:"affiliatedBranch",label:"所在支部"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"所在支部",maxlength:"50"},model:{value:e.tempFormModel.affiliatedBranch,callback:function(t){e.$set(e.tempFormModel,"affiliatedBranch",t)},expression:"tempFormModel.affiliatedBranch"}})],1)],1),a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{prop:"origPartyOrganization",label:"原党组织"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"原党组织",maxlength:"50"},model:{value:e.tempFormModel.origPartyOrganization,callback:function(t){e.$set(e.tempFormModel,"origPartyOrganization",t)},expression:"tempFormModel.origPartyOrganization"}})],1)],1),a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{prop:"transferTime",label:"转入时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"转入时间","value-format":"yyyy-MM-dd"},model:{value:e.tempFormModel.transferTime,callback:function(t){e.$set(e.tempFormModel,"transferTime",t)},expression:"tempFormModel.transferTime"}})],1)],1),a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{prop:"transferOutTime",label:"转出时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"转出时间","value-format":"yyyy-MM-dd"},model:{value:e.tempFormModel.transferOutTime,callback:function(t){e.$set(e.tempFormModel,"transferOutTime",t)},expression:"tempFormModel.transferOutTime"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"备注"},model:{value:e.tempFormModel.remark,callback:function(t){e.$set(e.tempFormModel,"remark",t)},expression:"tempFormModel.remark"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{icon:"el-icon-close"},on:{click:function(t){return e.cancle()}}},[e._v(" 关闭 ")]),a("el-button",{attrs:{loading:e.btnSaveLoading,type:"primary",icon:"el-icon-check"},on:{click:function(t){return e.save()}}},[e._v(" 保存 ")])],1)],1)],1)}),n=[],s={computed:{},name:"modifyAffiliatedBranch",components:{},props:{id:{type:String,default:""},employeeHRId:{type:String,default:""},title:{type:String,default:""}},data:function(){return{span:12,rules:{},tempFormModel:{id:"",employeeHRId:""},btnSaveLoading:!1}},watch:{id:function(e){this.tempFormModel.id=e},employeeHRId:function(e){this.tempFormModel.employeeHRId=e}},mounted:function(){},created:function(){this.init()},methods:{init:function(){this.id?this.id&&this.get(this.id):this.clear()},get:function(e){var t=this;this.btnSaveLoading=!0,l["a"].getEmployeeHRAffiliatedBranch({id:this.id}).then((function(e){t.tempFormModel=e.data,t.btnSaveLoading=!1})).catch((function(e){t.btnSaveLoading=!1}))},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.btnSaveLoading=!0,l["a"].addEmployeeHRAffiliatedBranch(this.tempFormModel).then((function(t){e.btnSaveLoading=!1,t.succeed?(e.tempFormModel=t.data,e.$notice.message("新增成功","success"),e.close()):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.btnSaveLoading=!1,t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;this.btnSaveLoading=!0,l["a"].updateEmployeeHRAffiliatedBranch(this.tempFormModel).then((function(t){e.btnSaveLoading=!1,t.succeed?(e.tempFormModel=t.data,e.$notice.message("修改成功","success"),e.close()):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.btnSaveLoading=!1,t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){this.$refs["dataForm"]&&this.$refs["dataForm"].resetFields(),this.tempFormModel={id:this.id,employeeHRId:this.employeeHRId}},close:function(){this.clear(),this.$emit("refresh")},cancle:function(){this.clear(),this.$emit("hidden")}}},c=s,p=a("2877"),m=Object(p["a"])(c,i,n,!1,null,null,null),d=m.exports,u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{"append-to-body":"",title:e.title,width:"60%","close-on-click-modal":!1,visible:!0},on:{close:function(t){return e.cancle()}}},[a("el-form",{ref:"dataForm",staticClass:"el-dialogform",attrs:{rules:e.rules,model:e.tempFormModel,"label-position":"right","label-width":"160px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{prop:"honorOrPunish",label:"荣誉/处分类型"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"荣誉/处分类型",maxlength:"50"},model:{value:e.tempFormModel.honorOrPunish,callback:function(t){e.$set(e.tempFormModel,"honorOrPunish",t)},expression:"tempFormModel.honorOrPunish"}})],1)],1),a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{prop:"honorOrPunishCompany",label:"颁奖单位/处分单位"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"颁奖单位/处分单位",maxlength:"50"},model:{value:e.tempFormModel.honorOrPunishCompany,callback:function(t){e.$set(e.tempFormModel,"honorOrPunishCompany",t)},expression:"tempFormModel.honorOrPunishCompany"}})],1)],1),a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{prop:"time",label:"时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"时间","value-format":"yyyy-MM-dd"},model:{value:e.tempFormModel.time,callback:function(t){e.$set(e.tempFormModel,"time",t)},expression:"tempFormModel.time"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"备注"},model:{value:e.tempFormModel.remark,callback:function(t){e.$set(e.tempFormModel,"remark",t)},expression:"tempFormModel.remark"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{icon:"el-icon-close"},on:{click:function(t){return e.cancle()}}},[e._v(" 关闭 ")]),a("el-button",{attrs:{loading:e.btnSaveLoading,type:"primary",icon:"el-icon-check"},on:{click:function(t){return e.save()}}},[e._v(" 保存 ")])],1)],1)],1)},h=[],f={computed:{},name:"modifyPartyMemberHonor",components:{},props:{id:{type:String,default:""},employeeHRId:{type:String,default:""},title:{type:String,default:""}},data:function(){return{span:12,rules:{},tempFormModel:{id:"",employeeHRId:""},btnSaveLoading:!1}},watch:{id:function(e){this.tempFormModel.id=e},employeeHRId:function(e){this.tempFormModel.employeeHRId=e}},mounted:function(){},created:function(){this.init()},methods:{init:function(){this.id?this.id&&this.get(this.id):this.clear()},get:function(e){var t=this;this.btnSaveLoading=!0,l["a"].getEmployeeHRPartyMemberHonor({id:this.id}).then((function(e){t.tempFormModel=e.data,t.btnSaveLoading=!1})).catch((function(e){t.btnSaveLoading=!1}))},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.btnSaveLoading=!0,l["a"].addEmployeeHRPartyMemberHonor(this.tempFormModel).then((function(t){e.btnSaveLoading=!1,t.succeed?(e.tempFormModel=t.data,e.$notice.message("新增成功","success"),e.close()):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.btnSaveLoading=!1,t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;this.btnSaveLoading=!0,l["a"].updateEmployeeHRPartyMemberHonor(this.tempFormModel).then((function(t){e.btnSaveLoading=!1,t.succeed?(e.tempFormModel=t.data,e.$notice.message("修改成功","success"),e.close()):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.btnSaveLoading=!1,t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){this.$refs["dataForm"]&&this.$refs["dataForm"].resetFields(),this.tempFormModel={id:this.id,employeeHRId:this.employeeHRId}},close:function(){this.clear(),this.$emit("refresh")},cancle:function(){this.clear(),this.$emit("hidden")}}},g=f,y=Object(p["a"])(g,u,h,!1,null,null,null),b=y.exports,v=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{"append-to-body":"",title:e.title,width:"60%","close-on-click-modal":!1,visible:!0},on:{close:function(t){return e.cancle()}}},[a("el-form",{ref:"dataForm",staticClass:"el-dialogform",attrs:{rules:e.rules,model:e.tempFormModel,"label-position":"right","label-width":"80px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{prop:"name",label:"当前组织"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"当前组织",maxlength:"50"},model:{value:e.tempFormModel.organizationName,callback:function(t){e.$set(e.tempFormModel,"organizationName",t)},expression:"tempFormModel.organizationName"}})],1)],1),a("el-col",{attrs:{span:e.span}},[a("el-form-item",{attrs:{label:"党内职务",prop:"dictId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择党内职务"},model:{value:e.tempFormModel.dictId,callback:function(t){e.$set(e.tempFormModel,"dictId",t)},expression:"tempFormModel.dictId"}},e._l(e.dictList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{icon:"el-icon-close"},on:{click:function(t){return e.cancle()}}},[e._v(" 关闭 ")]),a("el-button",{attrs:{loading:e.btnSaveLoading,type:"primary",icon:"el-icon-check"},on:{click:function(t){return e.save()}}},[e._v(" 保存 ")])],1)],1)],1)},D=[],F={name:"ModifyPartyOrganizations",components:{},props:{id:{type:String,default:""},employeeHRId:{type:String,default:""},title:{type:String,default:""}},data:function(){return{span:12,rules:{},tempFormModel:{id:"",employeeHRId:""},btnSaveLoading:!1,dictList:[]}},computed:{},watch:{id:function(e){this.tempFormModel.id=e},employeeHRId:function(e){this.tempFormModel.employeeHRId=e}},mounted:function(){},created:function(){this.init(),this.loadDictList()},methods:{init:function(){this.id?this.id&&this.get(this.id):this.clear()},loadDictList:function(){var e=this;l["a"].queryDictByParentCode({code:"00081"}).then((function(t){e.dictList=t.data.datas})).catch((function(e){console.log(e)}))},get:function(e){var t=this;this.btnSaveLoading=!0,l["a"].getEmployeeHRDict({id:this.id}).then((function(e){t.tempFormModel=e.data,t.btnSaveLoading=!1})).catch((function(e){t.btnSaveLoading=!1}))},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.btnSaveLoading=!0,l["a"].addEmployeeHRDict(this.tempFormModel).then((function(t){e.btnSaveLoading=!1,t.succeed?(e.tempFormModel=t.data,e.$notice.message("新增成功","success"),e.close()):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.btnSaveLoading=!1,t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;this.btnSaveLoading=!0,l["a"].updateEmployeeHRDict(this.tempFormModel).then((function(t){e.btnSaveLoading=!1,t.succeed?(e.tempFormModel=t.data,e.$notice.message("修改成功","success"),e.close()):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.btnSaveLoading=!1,t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){this.$refs["dataForm"]&&this.$refs["dataForm"].resetFields(),this.tempFormModel={id:this.id,employeeHRId:this.employeeHRId}},close:function(){this.clear(),this.$emit("refresh")},cancle:function(){this.clear(),this.$emit("hidden")}}},w=F,S=Object(p["a"])(w,v,D,!1,null,null,null),x=S.exports,M={name:"Party",components:{modifyAffiliatedBranch:d,modifyPartyMemberHonor:b,modifyPartyOrganizations:x},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{tempFormModelParty:{},partyList:[],partyPositionsList:[],affiliatedBranchList:[],dialogEditAffiliatedBranchVisible:!1,modifyAffiliatedBranchDialogTitle:"",affiliatedBranchId:null,partyMemberHonorList:[],dialogEditPartyMemberHonorVisible:!1,modifyPartyMemberHonorDialogTitle:"",partyMemberHonorId:null,employeeHRDictList:[],dialogEditEmployeeHRDictVisible:!1,modifyEmployeeHRDictDialogTitle:"",employeeHRDictId:null,rules:{partyId:[{required:!0,message:"请选择政治面貌",trigger:"blur"}]}}},computed:{},created:function(){this.loadPartyList(),this.init()},methods:{init:function(){this.empId&&(this.getEmployeeHR(this.empId),this.initAffiliatedBranch(this.empId),this.initPartyMemberHonor(this.empId),this.initEmployeeHRDict(this.empId))},getEmployeeHR:function(e){var t=this;l["a"].getEmployeeHR({id:e}).then((function(e){e.data&&(t.tempFormModelParty=e.data)})).catch((function(e){console.log(e)}))},save:function(){var e=this;this.$set(this.tempFormModelParty,"id",this.empId),l["a"].editEmployeeParty(this.tempFormModelParty).then((function(t){t.succeed?e.$notice.message("修改成功","success"):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},loadPartyList:function(){var e=this;l["a"].queryDictByParentCode({code:"00017"}).then((function(t){e.partyList=t.data.datas})).catch((function(e){console.log(e)}))},partyFeeStandardChange:function(){if(this.tempFormModelParty.partyFeeStandard){var e=(this.tempFormModelParty.partyFeeStandard+"").replace(/,/g,""),t=Number(e).toFixed(2);this.$set(this.tempFormModelParty,"partyFeeStandard",Number(t))}else this.$set(this.tempFormModelParty,"partyFeeStandard","")},initAffiliatedBranch:function(e){var t=this;l["a"].queryEmployeeHRAffiliatedBranch({id:e}).then((function(e){e.data?t.affiliatedBranchList=e.data:t.affiliatedBranchList=[]})).catch((function(e){console.log(e)}))},addAffiliatedBranch:function(){this.affiliatedBranchId=null,this.dialogEditAffiliatedBranchVisible=!0,this.modifyDialogTitle="添加所在支部"},updateAffiliatedBranch:function(e){this.affiliatedBranchId=e.id,this.dialogEditAffiliatedBranchVisible=!0,this.modifyDialogTitle="修改所在支部"},deleteAffiliatedBranch:function(e){var t=this,a=this;this.$confirm("确定删除所在支部?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeHRAffiliatedBranch(e).then((function(e){e.succeed?(a.initAffiliatedBranch(a.empId),t.$notice.message("删除所在支部成功","success")):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){e.processed||t.$notice.message("删除失败","error")}))})).catch((function(e){e.succeed}))},onAffiliatedBranchHidden:function(){this.dialogEditAffiliatedBranchVisible=!1},onAffiliatedBranchRefresh:function(){this.initAffiliatedBranch(this.empId),this.dialogEditAffiliatedBranchVisible=!1},initPartyMemberHonor:function(e){var t=this;l["a"].queryEmployeeHRPartyMemberHonor({id:e}).then((function(e){e.data?t.partyMemberHonorList=e.data:t.partyMemberHonorList=[]})).catch((function(e){console.log(e)}))},addPartyMemberHonor:function(){this.partyMemberHonorId=null,this.dialogEditPartyMemberHonorVisible=!0,this.modifyDialogTitle="添加党员奖惩"},updatePartyMemberHonor:function(e){this.partyMemberHonorId=e.id,this.dialogEditPartyMemberHonorVisible=!0,this.modifyDialogTitle="修改党员奖惩"},deletePartyMemberHonor:function(e){var t=this,a=this;this.$confirm("确定删除党员奖惩?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeHRPartyMemberHonor(e).then((function(e){e.succeed?(a.initPartyMemberHonor(a.empId),t.$notice.message("删除党员奖惩成功","success")):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){e.processed||t.$notice.message("删除失败","error")}))})).catch((function(e){e.succeed}))},onPartyMemberHonorHidden:function(){this.dialogEditPartyMemberHonorVisible=!1},onPartyMemberHonorRefresh:function(){this.initPartyMemberHonor(this.empId),this.dialogEditPartyMemberHonorVisible=!1},handleRowClass:function(e,t){return e.rowIndex%2===0?"cellStyle":"stripedStyle"},initEmployeeHRDict:function(e){var t=this;l["a"].queryEmployeeHRDict({id:e}).then((function(e){e.data?t.employeeHRDictList=e.data:t.employeeHRDictList=[]})).catch((function(e){console.log(e)}))},addEmployeeHRDict:function(){this.employeeHRDictId=null,this.dialogEditEmployeeHRDictVisible=!0,this.modifyDialogTitle="添加当前组织"},updateEmployeeHRDict:function(e){this.employeeHRDictId=e.id,this.dialogEditEmployeeHRDictVisible=!0,this.modifyDialogTitle="修改当前组织"},deleteEmployeeHRDict:function(e){var t=this,a=this;this.$confirm("确定删除当前组织?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeHRDict(e).then((function(e){e.succeed?(a.initEmployeeHRDict(a.empId),t.$notice.message("删除当前组织成功","success")):-3!==e.type&&t.$notice.resultTip(e)})).catch((function(e){e.processed||t.$notice.message("删除失败","error")}))})).catch((function(e){console.log(e)}))},onEmployeeHRDictHidden:function(){this.dialogEditEmployeeHRDictVisible=!1},onEmployeeHRDictRefresh:function(){this.initEmployeeHRDict(this.empId),this.dialogEditEmployeeHRDictVisible=!1},clear:function(){}}},_=M,k=(a("3238"),a("2e19"),Object(p["a"])(_,o,r,!1,null,"33265038",null));t["a"]=k.exports},"19de":function(e,t){e.exports=function(e,t,a,o){var r="undefined"!==typeof o?[o,e]:[e],l=new Blob(r,{type:a||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(l,t);else{var i=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(l):window.webkitURL.createObjectURL(l),n=document.createElement("a");n.style.display="none",n.href=i,n.setAttribute("download",t),"undefined"===typeof n.download&&n.setAttribute("target","_blank"),document.body.appendChild(n),n.click(),setTimeout((function(){document.body.removeChild(n),window.URL.revokeObjectURL(i)}),200)}}},2195:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{visible:e.addBaseInfoDialogVisible,rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"},on:{"update:visible":function(t){e.addBaseInfoDialogVisible=t}}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1):e._e(),e.userPermission.isShowBtnClear?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document-delete"},on:{click:e.clear}},[e._v("清除")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{slot:"trigger",size:"mini",type:"primary"},slot:"trigger"},[e._v("导入")])],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:16}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名",prop:"displayName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入姓名"},model:{value:e.tempData.tempFormModel.displayName,callback:function(t){e.$set(e.tempData.tempFormModel,"displayName",t)},expression:"tempData.tempFormModel.displayName"}}),a("el-button",{attrs:{size:"mini"},on:{click:e.checkDisplayName}},[e._v("校验")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"拼音简写",prop:"pinyin"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入拼音简写"},model:{value:e.tempData.tempFormModel.pinyin,callback:function(t){e.$set(e.tempData.tempFormModel,"pinyin",t)},expression:"tempData.tempFormModel.pinyin"}}),a("el-button",{attrs:{size:"mini"},on:{click:e.getSuoxie}},[e._v("自动")])],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"唯一码",prop:"uid"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.tempData.tempFormModel.uid,callback:function(t){e.$set(e.tempData.tempFormModel,"uid",t)},expression:"tempData.tempFormModel.uid"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工号",prop:"empCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入工号"},model:{value:e.tempData.tempFormModel.empCode,callback:function(t){e.$set(e.tempData.tempFormModel,"empCode",t)},expression:"tempData.tempFormModel.empCode"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"生日",prop:"birthday"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择出生日期","value-format":"yyyy-MM-dd","picker-options":e.datePickerOptions},on:{input:e.birthdaychange},model:{value:e.tempData.tempFormModel.birthday,callback:function(t){e.$set(e.tempData.tempFormModel,"birthday",t)},expression:"tempData.tempFormModel.birthday"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"年龄",prop:"age"}},[a("el-input",{attrs:{type:"number",clearable:"",placeholder:"请输入年龄"},model:{value:e.tempData.tempFormModel.age,callback:function(t){e.$set(e.tempData.tempFormModel,"age",e._n(t))},expression:"tempData.tempFormModel.age"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"性别",prop:"enumGender"}},[a("el-radio-group",{on:{change:function(t){return e.$forceUpdate()}},model:{value:e.tempData.tempFormModel.enumGender,callback:function(t){e.$set(e.tempData.tempFormModel,"enumGender",t)},expression:"tempData.tempFormModel.enumGender"}},[a("el-radio",{attrs:{label:1}},[e._v("男")]),a("el-radio",{attrs:{label:2}},[e._v("女")])],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"婚姻状况",prop:"marryId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择婚姻状况"},model:{value:e.tempData.tempFormModel.marryId,callback:function(t){e.$set(e.tempData.tempFormModel,"marryId",t)},expression:"tempData.tempFormModel.marryId"}},e._l(e.marryOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"民族",prop:"nationalityId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择民族"},model:{value:e.tempData.tempFormModel.nationalityId,callback:function(t){e.$set(e.tempData.tempFormModel,"nationalityId",t)},expression:"tempData.tempFormModel.nationalityId"}},e._l(e.nationalityOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"籍贯",prop:"nativePlace"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入籍贯"},model:{value:e.tempData.tempFormModel.nativePlace,callback:function(t){e.$set(e.tempData.tempFormModel,"nativePlace",t)},expression:"tempData.tempFormModel.nativePlace"}})],1)],1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"照片",prop:"attachmentId"}},[a("image-upload",{ref:"upload",attrs:{limit:1,"attachment-id":e.tempData.tempFormModel.attachmentId,clearable:""},on:{image:e.getReturnData}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"证件类型",prop:"documentTypeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择证件类型"},model:{value:e.tempData.tempFormModel.documentTypeId,callback:function(t){e.$set(e.tempData.tempFormModel,"documentTypeId",t)},expression:"tempData.tempFormModel.documentTypeId"}},e._l(e.documentTypeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"证件编号",prop:"identityNumber"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入证件编号"},model:{value:e.tempData.tempFormModel.identityNumber,callback:function(t){e.$set(e.tempData.tempFormModel,"identityNumber",t)},expression:"tempData.tempFormModel.identityNumber"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"兼职部门一",prop:"partTimeDeptId1"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{clearable:"",options:e.departmentOptions2,props:{expandTrigger:"hover",value:"id",label:"name",checkStrictly:!0},disabled:!1,placeholder:"请选择兼职部门"},model:{value:e.tempData.tempFormModel.partTimeDeptId1,callback:function(t){e.$set(e.tempData.tempFormModel,"partTimeDeptId1",t)},expression:"tempData.tempFormModel.partTimeDeptId1"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"部门",prop:"deptId"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{clearable:"",options:e.departmentOptions,props:{expandTrigger:"hover",value:"id",label:"name",checkStrictly:!0},disabled:!0,placeholder:"请选择部门"},model:{value:e.tempData.tempFormModel.deptId,callback:function(t){e.$set(e.tempData.tempFormModel,"deptId",t)},expression:"tempData.tempFormModel.deptId"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"兼职部门二",prop:"partTimeDeptId2"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{clearable:"",options:e.departmentOptions2,props:{expandTrigger:"hover",value:"id",label:"name",checkStrictly:!0},disabled:!1,placeholder:"请选择兼职部门"},model:{value:e.tempData.tempFormModel.partTimeDeptId2,callback:function(t){e.$set(e.tempData.tempFormModel,"partTimeDeptId2",t)},expression:"tempData.tempFormModel.partTimeDeptId2"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"电子邮件",prop:"email"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入电子邮件"},model:{value:e.tempData.tempFormModel.email,callback:function(t){e.$set(e.tempData.tempFormModel,"email",t)},expression:"tempData.tempFormModel.email"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"家庭电话",prop:"phone"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入家庭电话"},model:{value:e.tempData.tempFormModel.phone,callback:function(t){e.$set(e.tempData.tempFormModel,"phone",t)},expression:"tempData.tempFormModel.phone"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"手机",prop:"mobile"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入手机"},model:{value:e.tempData.tempFormModel.mobile,callback:function(t){e.$set(e.tempData.tempFormModel,"mobile",t)},expression:"tempData.tempFormModel.mobile"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"家庭地址",prop:"address"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入家庭地址"},model:{value:e.tempData.tempFormModel.address,callback:function(t){e.$set(e.tempData.tempFormModel,"address",t)},expression:"tempData.tempFormModel.address"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"家庭地址邮编",prop:"zipCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入家庭地址邮编"},model:{value:e.tempData.tempFormModel.zipCode,callback:function(t){e.$set(e.tempData.tempFormModel,"zipCode",t)},expression:"tempData.tempFormModel.zipCode"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职务",prop:"position"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入职务"},model:{value:e.tempData.tempFormModel.position,callback:function(t){e.$set(e.tempData.tempFormModel,"position",t)},expression:"tempData.tempFormModel.position"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职务聘任时间",prop:"positionAppointmentTime"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"请输入职务聘任时间"},model:{value:e.tempData.tempFormModel.positionAppointmentTime,callback:function(t){e.$set(e.tempData.tempFormModel,"positionAppointmentTime",t)},expression:"tempData.tempFormModel.positionAppointmentTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职务标记",prop:"positionTab"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入职务标记"},model:{value:e.tempData.tempFormModel.positionTab,callback:function(t){e.$set(e.tempData.tempFormModel,"positionTab",t)},expression:"tempData.tempFormModel.positionTab"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职称",prop:"qualification"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入职称"},model:{value:e.tempData.tempFormModel.qualification,callback:function(t){e.$set(e.tempData.tempFormModel,"qualification",t)},expression:"tempData.tempFormModel.qualification"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职称聘任时间",prop:"qualificationAppointmentTime"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"请输入职称聘任时间"},model:{value:e.tempData.tempFormModel.qualificationAppointmentTime,callback:function(t){e.$set(e.tempData.tempFormModel,"qualificationAppointmentTime",t)},expression:"tempData.tempFormModel.qualificationAppointmentTime"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职称标记",prop:"qualificationTab"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入职称标记"},model:{value:e.tempData.tempFormModel.qualificationTab,callback:function(t){e.$set(e.tempData.tempFormModel,"qualificationTab",t)},expression:"tempData.tempFormModel.qualificationTab"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"户口类型",prop:"registerTypeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择户口类型"},model:{value:e.tempData.tempFormModel.registerTypeId,callback:function(t){e.$set(e.tempData.tempFormModel,"registerTypeId",t)},expression:"tempData.tempFormModel.registerTypeId"}},e._l(e.registerTypeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"部门负责人"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"",clearable:""},model:{value:e.deptPrincipa.name,callback:function(t){e.$set(e.deptPrincipa,"name",t)},expression:"deptPrincipa.name"}}),a("el-button",{on:{click:e.selectEmployeeDialog}},[e._v("选 择")])],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否开饭卡",prop:"isMealCard"}},[a("el-checkbox",{model:{value:e.tempData.tempFormModel.isMealCard,callback:function(t){e.$set(e.tempData.tempFormModel,"isMealCard",t)},expression:"tempData.tempFormModel.isMealCard"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否发饭贴",prop:"isMealSubsidy"}},[a("el-checkbox",{model:{value:e.tempData.tempFormModel.isMealSubsidy,callback:function(t){e.$set(e.tempData.tempFormModel,"isMealSubsidy",t)},expression:"tempData.tempFormModel.isMealSubsidy"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否开门禁",prop:"isEntrance"}},[a("el-checkbox",{model:{value:e.tempData.tempFormModel.isEntrance,callback:function(t){e.$set(e.tempData.tempFormModel,"isEntrance",t)},expression:"tempData.tempFormModel.isEntrance"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否会议签到",prop:"isMettingSign"}},[a("el-checkbox",{model:{value:e.tempData.tempFormModel.isMettingSign,callback:function(t){e.$set(e.tempData.tempFormModel,"isMettingSign",t)},expression:"tempData.tempFormModel.isMettingSign"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否开启移动端",prop:"isMobileUser"}},[a("el-checkbox",{model:{value:e.tempData.tempFormModel.isMobileUser,callback:function(t){e.$set(e.tempData.tempFormModel,"isMobileUser",t)},expression:"tempData.tempFormModel.isMobileUser"}})],1)],1)],1)],1),e.checkDisplayNameDialogVisible?a("el-dialog",{attrs:{title:"检验员工姓名",visible:e.checkDisplayNameDialogVisible,width:"90%","append-to-body":""},on:{"update:visible":function(t){e.checkDisplayNameDialogVisible=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageEmpList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{fixed:"",label:"唯一码",sortable:"custom",prop:"Uid","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{staticClass:"link-type",on:{click:function(t){return e.handleChooseEmployee(o)}}},[e._v(e._s(o.uid))])]}}],null,!1,3200892435)}),a("el-table-column",{attrs:{label:"工号","min-width":"80px",sortable:"custom",prop:"EmpCode"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.empCode))])]}}],null,!1,2960556977)}),a("el-table-column",{attrs:{label:"姓名","min-width":"80px",sortable:"custom",prop:"DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.displayName))])]}}],null,!1,3748713369)}),a("el-table-column",{attrs:{label:"部门","min-width":"80px",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.departmentName))])]}}],null,!1,3021220391)}),a("el-table-column",{attrs:{label:"职别","min-width":"80px",sortable:"custom",prop:"EmployeeHR.OfficialRank.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.officialRank))])]}}],null,!1,2643951635)})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getEmpList}})],1):e._e(),a("select-user",{ref:"selectempc",on:{selectRow:e.setEmp}})],1)},r=[],l=(a("4de4"),a("b0c0"),a("a9e3"),a("ac1f"),a("5319"),a("1276"),a("53ca")),i=a("d368"),n=a("e44c"),s=a("e0ec"),c=(a("c975"),a("4d63"),a("25f0"),{ConvertPinyin:function(e){for(var t={a:"啊阿锕",ai:"埃挨哎唉哀皑癌蔼矮艾碍爱隘诶捱嗳嗌嫒瑷暧砹锿霭",an:"鞍氨安俺按暗岸胺案谙埯揞犴庵桉铵鹌顸黯",ang:"肮昂盎",ao:"凹敖熬翱袄傲奥懊澳坳拗嗷噢岙廒遨媪骜聱螯鏊鳌鏖",ba:"芭捌扒叭吧笆八疤巴拔跋靶把耙坝霸罢爸茇菝萆捭岜灞杷钯粑鲅魃",bai:"白柏百摆佰败拜稗薜掰鞴",ban:"斑班搬扳般颁板版扮拌伴瓣半办绊阪坂豳钣瘢癍舨",bang:"邦帮梆榜膀绑棒磅蚌镑傍谤蒡螃",bao:"苞胞包褒雹保堡饱宝抱报暴豹鲍爆勹葆宀孢煲鸨褓趵龅",bo:"剥薄玻菠播拨钵波博勃搏铂箔伯帛舶脖膊渤泊驳亳蕃啵饽檗擘礴钹鹁簸跛",bei:"杯碑悲卑北辈背贝钡倍狈备惫焙被孛陂邶埤蓓呗怫悖碚鹎褙鐾",ben:"奔苯本笨畚坌锛",beng:"崩绷甭泵蹦迸唪嘣甏",bi:"逼鼻比鄙笔彼碧蓖蔽毕毙毖币庇痹闭敝弊必辟壁臂避陛匕仳俾芘荜荸吡哔狴庳愎滗濞弼妣婢嬖璧贲畀铋秕裨筚箅篦舭襞跸髀",bian:"鞭边编贬扁便变卞辨辩辫遍匾弁苄忭汴缏煸砭碥稹窆蝙笾鳊",biao:"标彪膘表婊骠飑飙飚灬镖镳瘭裱鳔",bie:"鳖憋别瘪蹩鳘",bin:"彬斌濒滨宾摈傧浜缤玢殡膑镔髌鬓",bing:"兵冰柄丙秉饼炳病并禀邴摒绠枋槟燹",bu:"捕卜哺补埠不布步簿部怖拊卟逋瓿晡钚醭",ca:"擦嚓礤",cai:"猜裁材才财睬踩采彩菜蔡",can:"餐参蚕残惭惨灿骖璨粲黪",cang:"苍舱仓沧藏伧",cao:"操糙槽曹草艹嘈漕螬艚",ce:"厕策侧册测刂帻恻",ceng:"层蹭噌",cha:"插叉茬茶查碴搽察岔差诧猹馇汊姹杈楂槎檫钗锸镲衩",chai:"拆柴豺侪茈瘥虿龇",chan:"搀掺蝉馋谗缠铲产阐颤冁谄谶蒇廛忏潺澶孱羼婵嬗骣觇禅镡裣蟾躔",chang:"昌猖场尝常长偿肠厂敞畅唱倡伥鬯苌菖徜怅惝阊娼嫦昶氅鲳",chao:"超抄钞朝嘲潮巢吵炒怊绉晁耖",che:"车扯撤掣彻澈坼屮砗",chen:"郴臣辰尘晨忱沉陈趁衬称谌抻嗔宸琛榇肜胂碜龀",cheng:"撑城橙成呈乘程惩澄诚承逞骋秤埕嵊徵浈枨柽樘晟塍瞠铖裎蛏酲",chi:"吃痴持匙池迟弛驰耻齿侈尺赤翅斥炽傺墀芪茌搋叱哧啻嗤彳饬沲媸敕胝眙眵鸱瘛褫蚩螭笞篪豉踅踟魑",chong:"充冲虫崇宠茺忡憧铳艟",chou:"抽酬畴踌稠愁筹仇绸瞅丑俦圳帱惆溴妯瘳雠鲋",chu:"臭初出橱厨躇锄雏滁除楚础储矗搐触处亍刍憷绌杵楮樗蜍蹰黜",chuan:"揣川穿椽传船喘串掾舛惴遄巛氚钏镩舡",chuang:"疮窗幢床闯创怆",chui:"吹炊捶锤垂陲棰槌",chun:"春椿醇唇淳纯蠢促莼沌肫朐鹑蝽",chuo:"戳绰蔟辶辍镞踔龊",ci:"疵茨磁雌辞慈瓷词此刺赐次荠呲嵯鹚螅糍趑",cong:"聪葱囱匆从丛偬苁淙骢琮璁枞",cu:"凑粗醋簇猝殂蹙",cuan:"蹿篡窜汆撺昕爨",cui:"摧崔催脆瘁粹淬翠萃悴璀榱隹",cun:"村存寸磋忖皴",cuo:"撮搓措挫错厝脞锉矬痤鹾蹉躜",da:"搭达答瘩打大耷哒嗒怛妲疸褡笪靼鞑",dai:"呆歹傣戴带殆代贷袋待逮怠埭甙呔岱迨逯骀绐玳黛",dan:"耽担丹单郸掸胆旦氮但惮淡诞弹蛋亻儋卩萏啖澹檐殚赕眈瘅聃箪",dang:"当挡党荡档谠凼菪宕砀铛裆",dao:"刀捣蹈倒岛祷导到稻悼道盗叨啁忉洮氘焘忑纛",de:"德得的锝",deng:"蹬灯登等瞪凳邓噔嶝戥磴镫簦",di:"堤低滴迪敌笛狄涤翟嫡抵底地蒂第帝弟递缔氐籴诋谛邸坻莜荻嘀娣柢棣觌砥碲睇镝羝骶",dian:"颠掂滇碘点典靛垫电佃甸店惦奠淀殿丶阽坫埝巅玷癜癫簟踮",diao:"碉叼雕凋刁掉吊钓调轺铞蜩粜貂",die:"跌爹碟蝶迭谍叠佚垤堞揲喋渫轶牒瓞褶耋蹀鲽鳎",ding:"丁盯叮钉顶鼎锭定订丢仃啶玎腚碇町铤疔耵酊",dong:"东冬董懂动栋侗恫冻洞垌咚岽峒夂氡胨胴硐鸫",dou:"兜抖斗陡豆逗痘蔸钭窦窬蚪篼酡",du:"都督毒犊独读堵睹赌杜镀肚度渡妒芏嘟渎椟橐牍蠹笃髑黩",duan:"端短锻段断缎彖椴煅簖",dui:"堆兑队对怼憝碓",dun:"墩吨蹲敦顿囤钝盾遁炖砘礅盹镦趸",duo:"掇哆多夺垛躲朵跺舵剁惰堕咄哚缍柁铎裰踱",e:"蛾峨鹅俄额讹娥恶厄扼遏鄂饿噩谔垩垭苊莪萼呃愕屙婀轭曷腭硪锇锷鹗颚鳄",en:"恩蒽摁唔嗯",er:"而儿耳尔饵洱二贰迩珥铒鸸鲕",fa:"发罚筏伐乏阀法珐垡砝",fan:"藩帆番翻樊矾钒繁凡烦反返范贩犯饭泛蘩幡犭梵攵燔畈蹯",fang:"坊芳方肪房防妨仿访纺放匚邡彷钫舫鲂",fei:"菲非啡飞肥匪诽吠肺废沸费芾狒悱淝妃绋绯榧腓斐扉祓砩镄痱蜚篚翡霏鲱",fen:"芬酚吩氛分纷坟焚汾粉奋份忿愤粪偾瀵棼愍鲼鼢",feng:"丰封枫蜂峰锋风疯烽逢冯缝讽奉凤俸酆葑沣砜",fu:"佛否夫敷肤孵扶拂辐幅氟符伏俘服浮涪福袱弗甫抚辅俯釜斧脯腑府腐赴副覆赋复傅付阜父腹负富讣附妇缚咐匐凫郛芙苻茯莩菔呋幞滏艴孚驸绂桴赙黻黼罘稃馥虍蚨蜉蝠蝮麸趺跗鳆",ga:"噶嘎蛤尬呷尕尜旮钆",gai:"该改概钙盖溉丐陔垓戤赅胲",gan:"干甘杆柑竿肝赶感秆敢赣坩苷尴擀泔淦澉绀橄旰矸疳酐",gang:"冈刚钢缸肛纲岗港戆罡颃筻",gong:"杠工攻功恭龚供躬公宫弓巩汞拱贡共蕻廾咣珙肱蚣蛩觥",gao:"篙皋高膏羔糕搞镐稿告睾诰郜蒿藁缟槔槁杲锆",ge:"哥歌搁戈鸽胳疙割革葛格阁隔铬个各鬲仡哿塥嗝纥搿膈硌铪镉袼颌虼舸骼髂",gei:"给",gen:"根跟亘茛哏艮",geng:"耕更庚羹埂耿梗哽赓鲠",gou:"钩勾沟苟狗垢构购够佝诟岣遘媾缑觏彀鸲笱篝鞲",gu:"辜菇咕箍估沽孤姑鼓古蛊骨谷股故顾固雇嘏诂菰哌崮汩梏轱牯牿胍臌毂瞽罟钴锢瓠鸪鹄痼蛄酤觚鲴骰鹘",gua:"刮瓜剐寡挂褂卦诖呱栝鸹",guai:"乖拐怪哙",guan:"棺关官冠观管馆罐惯灌贯倌莞掼涫盥鹳鳏",guang:"光广逛犷桄胱疒",gui:"瑰规圭硅归龟闺轨鬼诡癸桂柜跪贵刽匦刿庋宄妫桧炅晷皈簋鲑鳜",gun:"辊滚棍丨衮绲磙鲧",guo:"锅郭国果裹过馘蠃埚掴呙囗帼崞猓椁虢锞聒蜮蜾蝈",ha:"哈",hai:"骸孩海氦亥害骇咴嗨颏醢",han:"酣憨邯韩含涵寒函喊罕翰撼捍旱憾悍焊汗汉邗菡撖阚瀚晗焓颔蚶鼾",hen:"夯痕很狠恨",hang:"杭航沆绗珩桁",hao:"壕嚎豪毫郝好耗号浩薅嗥嚆濠灏昊皓颢蚝",he:"呵喝荷菏核禾和何合盒貉阂河涸赫褐鹤贺诃劾壑藿嗑嗬阖盍蚵翮",hei:"嘿黑",heng:"哼亨横衡恒訇蘅",hong:"轰哄烘虹鸿洪宏弘红黉讧荭薨闳泓",hou:"喉侯猴吼厚候后堠後逅瘊篌糇鲎骺",hu:"呼乎忽瑚壶葫胡蝴狐糊湖弧虎唬护互沪户冱唿囫岵猢怙惚浒滹琥槲轷觳烀煳戽扈祜鹕鹱笏醐斛",hua:"花哗华猾滑画划化话劐浍骅桦铧稞",huai:"槐徊怀淮坏还踝",huan:"欢环桓缓换患唤痪豢焕涣宦幻郇奂垸擐圜洹浣漶寰逭缳锾鲩鬟",huang:"荒慌黄磺蝗簧皇凰惶煌晃幌恍谎隍徨湟潢遑璜肓癀蟥篁鳇",hui:"灰挥辉徽恢蛔回毁悔慧卉惠晦贿秽会烩汇讳诲绘诙茴荟蕙哕喙隳洄彗缋珲晖恚虺蟪麾",hun:"荤昏婚魂浑混诨馄阍溷缗",huo:"豁活伙火获或惑霍货祸攉嚯夥钬锪镬耠蠖",ji:"击圾基机畸稽积箕肌饥迹激讥鸡姬绩缉吉极棘辑籍集及急疾汲即嫉级挤几脊己蓟技冀季伎祭剂悸济寄寂计记既忌际妓继纪居丌乩剞佶佴脔墼芨芰萁蒺蕺掎叽咭哜唧岌嵴洎彐屐骥畿玑楫殛戟戢赍觊犄齑矶羁嵇稷瘠瘵虮笈笄暨跻跽霁鲚鲫髻麂",jia:"嘉枷夹佳家加荚颊贾甲钾假稼价架驾嫁伽郏拮岬浃迦珈戛胛恝铗镓痂蛱笳袈跏",jian:"歼监坚尖笺间煎兼肩艰奸缄茧检柬碱硷拣捡简俭剪减荐槛鉴践贱见键箭件健舰剑饯渐溅涧建僭谏谫菅蒹搛囝湔蹇謇缣枧柙楗戋戬牮犍毽腱睑锏鹣裥笕箴翦趼踺鲣鞯",jiang:"僵姜将浆江疆蒋桨奖讲匠酱降茳洚绛缰犟礓耩糨豇",jiao:"蕉椒礁焦胶交郊浇骄娇嚼搅铰矫侥脚狡角饺缴绞剿教酵轿较叫佼僬茭挢噍峤徼姣纟敫皎鹪蛟醮跤鲛",jie:"窖揭接皆秸街阶截劫节桔杰捷睫竭洁结解姐戒藉芥界借介疥诫届偈讦诘喈嗟獬婕孑桀獒碣锴疖袷颉蚧羯鲒骱髫",jin:"巾筋斤金今津襟紧锦仅谨进靳晋禁近烬浸尽卺荩堇噤馑廑妗缙瑾槿赆觐钅锓衿矜",jing:"劲荆兢茎睛晶鲸京惊精粳经井警景颈静境敬镜径痉靖竟竞净刭儆阱菁獍憬泾迳弪婧肼胫腈旌",jiong:"炯窘冂迥扃",jiu:"揪究纠玖韭久灸九酒厩救旧臼舅咎就疚僦啾阄柩桕鹫赳鬏",ju:"鞠拘狙疽驹菊局咀矩举沮聚拒据巨具距踞锯俱句惧炬剧倨讵苣苴莒掬遽屦琚枸椐榘榉橘犋飓钜锔窭裾趄醵踽龃雎鞫",juan:"捐鹃娟倦眷卷绢鄄狷涓桊蠲锩镌隽",jue:"撅攫抉掘倔爵觉决诀绝厥劂谲矍蕨噘崛獗孓珏桷橛爝镢蹶觖",jun:"均菌钧军君峻俊竣浚郡骏捃狻皲筠麇",ka:"喀咖卡佧咔胩",ke:"咯坷苛柯棵磕颗科壳咳可渴克刻客课岢恪溘骒缂珂轲氪瞌钶疴窠蝌髁",kai:"开揩楷凯慨剀垲蒈忾恺铠锎",kan:"刊堪勘坎砍看侃凵莰莶戡龛瞰",kang:"康慷糠扛抗亢炕坑伉闶钪",kao:"考拷烤靠尻栲犒铐",ken:"肯啃垦恳垠裉颀",keng:"吭忐铿",kong:"空恐孔控倥崆箜",kou:"抠口扣寇芤蔻叩眍筘",ku:"枯哭窟苦酷库裤刳堀喾绔骷",kua:"夸垮挎跨胯侉",kuai:"块筷侩快蒯郐蒉狯脍",kuan:"宽款髋",kuang:"匡筐狂框矿眶旷况诓诳邝圹夼哐纩贶",kui:"亏盔岿窥葵奎魁傀馈愧溃馗匮夔隗揆喹喟悝愦阕逵暌睽聩蝰篑臾跬",kun:"坤昆捆困悃阃琨锟醌鲲髡",kuo:"括扩廓阔蛞",la:"垃拉喇蜡腊辣啦剌摺邋旯砬瘌",lai:"莱来赖崃徕涞濑赉睐铼癞籁",lan:"蓝婪栏拦篮阑兰澜谰揽览懒缆烂滥啉岚懔漤榄斓罱镧褴",lang:"琅榔狼廊郎朗浪莨蒗啷阆锒稂螂",lao:"捞劳牢老佬姥酪烙涝唠崂栳铑铹痨醪",le:"勒乐肋仂叻嘞泐鳓",lei:"雷镭蕾磊累儡垒擂类泪羸诔荽咧漯嫘缧檑耒酹",ling:"棱冷拎玲菱零龄铃伶羚凌灵陵岭领另令酃塄苓呤囹泠绫柃棂瓴聆蛉翎鲮",leng:"楞愣",li:"厘梨犁黎篱狸离漓理李里鲤礼莉荔吏栗丽厉励砾历利傈例俐痢立粒沥隶力璃哩俪俚郦坜苈莅蓠藜捩呖唳喱猁溧澧逦娌嫠骊缡珞枥栎轹戾砺詈罹锂鹂疠疬蛎蜊蠡笠篥粝醴跞雳鲡鳢黧",lian:"俩联莲连镰廉怜涟帘敛脸链恋炼练挛蔹奁潋濂娈琏楝殓臁膦裢蠊鲢",liang:"粮凉梁粱良两辆量晾亮谅墚椋踉靓魉",liao:"撩聊僚疗燎寥辽潦了撂镣廖料蓼尥嘹獠寮缭钌鹩耢",lie:"列裂烈劣猎冽埒洌趔躐鬣",lin:"琳林磷霖临邻鳞淋凛赁吝蔺嶙廪遴檩辚瞵粼躏麟",liu:"溜琉榴硫馏留刘瘤流柳六抡偻蒌泖浏遛骝绺旒熘锍镏鹨鎏",long:"龙聋咙笼窿隆垄拢陇弄垅茏泷珑栊胧砻癃",lou:"楼娄搂篓漏陋喽嵝镂瘘耧蝼髅",lu:"芦卢颅庐炉掳卤虏鲁麓碌露路赂鹿潞禄录陆戮垆摅撸噜泸渌漉璐栌橹轳辂辘氇胪镥鸬鹭簏舻鲈",lv:"驴吕铝侣旅履屡缕虑氯律率滤绿捋闾榈膂稆褛",luan:"峦孪滦卵乱栾鸾銮",lue:"掠略锊",lun:"轮伦仑沦纶论囵",luo:"萝螺罗逻锣箩骡裸落洛骆络倮荦摞猡泺椤脶镙瘰雒",ma:"妈麻玛码蚂马骂嘛吗唛犸嬷杩麽",mai:"埋买麦卖迈脉劢荬咪霾",man:"瞒馒蛮满蔓曼慢漫谩墁幔缦熳镘颟螨鳗鞔",mang:"芒茫盲忙莽邙漭朦硭蟒",meng:"氓萌蒙檬盟锰猛梦孟勐甍瞢懵礞虻蜢蠓艋艨黾",miao:"猫苗描瞄藐秒渺庙妙喵邈缈缪杪淼眇鹋蜱",mao:"茅锚毛矛铆卯茂冒帽貌贸侔袤勖茆峁瑁昴牦耄旄懋瞀蛑蝥蟊髦",me:"么",mei:"玫枚梅酶霉煤没眉媒镁每美昧寐妹媚坶莓嵋猸浼湄楣镅鹛袂魅",men:"门闷们扪玟焖懑钔",mi:"眯醚靡糜迷谜弥米秘觅泌蜜密幂芈冖谧蘼嘧猕獯汨宓弭脒敉糸縻麋",mian:"棉眠绵冕免勉娩缅面沔湎腼眄",mie:"蔑灭咩蠛篾",min:"民抿皿敏悯闽苠岷闵泯珉",ming:"明螟鸣铭名命冥茗溟暝瞑酩",miu:"谬",mo:"摸摹蘑模膜磨摩魔抹末莫墨默沫漠寞陌谟茉蓦馍嫫镆秣瘼耱蟆貊貘",mou:"谋牟某厶哞婺眸鍪",mu:"拇牡亩姆母墓暮幕募慕木目睦牧穆仫苜呒沐毪钼",na:"拿哪呐钠那娜纳内捺肭镎衲箬",nai:"氖乃奶耐奈鼐艿萘柰",nan:"南男难囊喃囡楠腩蝻赧",nao:"挠脑恼闹孬垴猱瑙硇铙蛲",ne:"淖呢讷",nei:"馁",nen:"嫩能枘恁",ni:"妮霓倪泥尼拟你匿腻逆溺伲坭猊怩滠昵旎祢慝睨铌鲵",nian:"蔫拈年碾撵捻念廿辇黏鲇鲶",niang:"娘酿",niao:"鸟尿茑嬲脲袅",nie:"捏聂孽啮镊镍涅乜陧蘖嗫肀颞臬蹑",nin:"您柠",ning:"狞凝宁拧泞佞蓥咛甯聍",niu:"牛扭钮纽狃忸妞蚴",nong:"脓浓农侬",nu:"奴努怒呶帑弩胬孥驽",nv:"女恧钕衄",nuan:"暖",nuenue:"虐",nue:"疟谑",nuo:"挪懦糯诺傩搦喏锘",ou:"哦欧鸥殴藕呕偶沤怄瓯耦",pa:"啪趴爬帕怕琶葩筢",pai:"拍排牌徘湃派俳蒎",pan:"攀潘盘磐盼畔判叛爿泮袢襻蟠蹒",pang:"乓庞旁耪胖滂逄",pao:"抛咆刨炮袍跑泡匏狍庖脬疱",pei:"呸胚培裴赔陪配佩沛掊辔帔淠旆锫醅霈",pen:"喷盆湓",peng:"砰抨烹澎彭蓬棚硼篷膨朋鹏捧碰坯堋嘭怦蟛",pi:"砒霹批披劈琵毗啤脾疲皮匹痞僻屁譬丕陴邳郫圮鼙擗噼庀媲纰枇甓睥罴铍痦癖疋蚍貔",pian:"篇偏片骗谝骈犏胼褊翩蹁",piao:"飘漂瓢票剽嘌嫖缥殍瞟螵",pie:"撇瞥丿苤氕",pin:"拼频贫品聘拚姘嫔榀牝颦",ping:"乒坪苹萍平凭瓶评屏俜娉枰鲆",po:"坡泼颇婆破魄迫粕叵鄱溥珀钋钷皤笸",pou:"剖裒踣",pu:"扑铺仆莆葡菩蒲埔朴圃普浦谱曝瀑匍噗濮璞氆镤镨蹼",qi:"期欺栖戚妻七凄漆柒沏其棋奇歧畦崎脐齐旗祈祁骑起岂乞企启契砌器气迄弃汽泣讫亟亓圻芑萋葺嘁屺岐汔淇骐绮琪琦杞桤槭欹祺憩碛蛴蜞綦綮趿蹊鳍麒",qia:"掐恰洽葜",qian:"牵扦钎铅千迁签仟谦乾黔钱钳前潜遣浅谴堑嵌欠歉佥阡芊芡荨掮岍悭慊骞搴褰缱椠肷愆钤虔箝",qiang:"枪呛腔羌墙蔷强抢嫱樯戗炝锖锵镪襁蜣羟跫跄",qiao:"橇锹敲悄桥瞧乔侨巧鞘撬翘峭俏窍劁诮谯荞愀憔缲樵毳硗跷鞒",qie:"切茄且怯窃郄唼惬妾挈锲箧",qin:"钦侵亲秦琴勤芹擒禽寝沁芩蓁蕲揿吣嗪噙溱檎螓衾",qing:"青轻氢倾卿清擎晴氰情顷请庆倩苘圊檠磬蜻罄箐謦鲭黥",qiong:"琼穷邛茕穹筇銎",qiu:"秋丘邱球求囚酋泅俅氽巯艽犰湫逑遒楸赇鸠虬蚯蝤裘糗鳅鼽",qu:"趋区蛆曲躯屈驱渠取娶龋趣去诎劬蕖蘧岖衢阒璩觑氍祛磲癯蛐蠼麴瞿黢",quan:"圈颧权醛泉全痊拳犬券劝诠荃獾悛绻辁畎铨蜷筌鬈",que:"缺炔瘸却鹊榷确雀阙悫",qun:"裙群逡",ran:"然燃冉染苒髯",rang:"瓤壤攘嚷让禳穰",rao:"饶扰绕荛娆桡",ruo:"惹若弱",re:"热偌",ren:"壬仁人忍韧任认刃妊纫仞荏葚饪轫稔衽",reng:"扔仍",ri:"日",rong:"戎茸蓉荣融熔溶容绒冗嵘狨缛榕蝾",rou:"揉柔肉糅蹂鞣",ru:"茹蠕儒孺如辱乳汝入褥蓐薷嚅洳溽濡铷襦颥",ruan:"软阮朊",rui:"蕊瑞锐芮蕤睿蚋",run:"闰润",sa:"撒洒萨卅仨挲飒",sai:"腮鳃塞赛噻",san:"三叁伞散彡馓氵毵糁霰",sang:"桑嗓丧搡磉颡",sao:"搔骚扫嫂埽臊瘙鳋",se:"瑟色涩啬铩铯穑",sen:"森",seng:"僧",sha:"莎砂杀刹沙纱傻啥煞脎歃痧裟霎鲨",shai:"筛晒酾",shan:"珊苫杉山删煽衫闪陕擅赡膳善汕扇缮剡讪鄯埏芟潸姗骟膻钐疝蟮舢跚鳝",shang:"墒伤商赏晌上尚裳垧绱殇熵觞",shao:"梢捎稍烧芍勺韶少哨邵绍劭苕潲蛸笤筲艄",she:"奢赊蛇舌舍赦摄射慑涉社设厍佘猞畲麝",shen:"砷申呻伸身深娠绅神沈审婶甚肾慎渗诜谂吲哂渖椹矧蜃",sheng:"声生甥牲升绳省盛剩胜圣丞渑媵眚笙",shi:"师失狮施湿诗尸虱十石拾时什食蚀实识史矢使屎驶始式示士世柿事拭誓逝势是嗜噬适仕侍释饰氏市恃室视试谥埘莳蓍弑唑饣轼耆贳炻礻铈铊螫舐筮豕鲥鲺",shou:"收手首守寿授售受瘦兽扌狩绶艏",shu:"蔬枢梳殊抒输叔舒淑疏书赎孰熟薯暑曙署蜀黍鼠属术述树束戍竖墅庶数漱恕倏塾菽忄沭涑澍姝纾毹腧殳镯秫鹬",shua:"刷耍唰涮",shuai:"摔衰甩帅蟀",shuan:"栓拴闩",shuang:"霜双爽孀",shui:"谁水睡税",shun:"吮瞬顺舜恂",shuo:"说硕朔烁蒴搠嗍濯妁槊铄",si:"斯撕嘶思私司丝死肆寺嗣四伺似饲巳厮俟兕菥咝汜泗澌姒驷缌祀祠锶鸶耜蛳笥",song:"松耸怂颂送宋讼诵凇菘崧嵩忪悚淞竦",sou:"搜艘擞嗽叟嗖嗾馊溲飕瞍锼螋",su:"苏酥俗素速粟僳塑溯宿诉肃夙谡蔌嗉愫簌觫稣",suan:"酸蒜算",sui:"虽隋随绥髓碎岁穗遂隧祟蓑冫谇濉邃燧眭睢",sun:"孙损笋荪狲飧榫跣隼",suo:"梭唆缩琐索锁所唢嗦娑桫睃羧",ta:"塌他它她塔獭挞蹋踏闼溻遢榻沓",tai:"胎苔抬台泰酞太态汰邰薹肽炱钛跆鲐",tan:"坍摊贪瘫滩坛檀痰潭谭谈坦毯袒碳探叹炭郯蕈昙钽锬覃",tang:"汤塘搪堂棠膛唐糖傥饧溏瑭铴镗耥螗螳羰醣",thang:"倘躺淌",theng:"趟烫",tao:"掏涛滔绦萄桃逃淘陶讨套挑鼗啕韬饕",te:"特",teng:"藤腾疼誊滕",ti:"梯剔踢锑提题蹄啼体替嚏惕涕剃屉荑悌逖绨缇鹈裼醍",tian:"天添填田甜恬舔腆掭忝阗殄畋钿蚺",tiao:"条迢眺跳佻祧铫窕龆鲦",tie:"贴铁帖萜餮",ting:"厅听烃汀廷停亭庭挺艇莛葶婷梃蜓霆",tong:"通桐酮瞳同铜彤童桶捅筒统痛佟僮仝茼嗵恸潼砼",tou:"偷投头透亠",tu:"凸秃突图徒途涂屠土吐兔堍荼菟钍酴",tuan:"湍团疃",tui:"推颓腿蜕褪退忒煺",tun:"吞屯臀饨暾豚窀",tuo:"拖托脱鸵陀驮驼椭妥拓唾乇佗坨庹沱柝砣箨舄跎鼍",wa:"挖哇蛙洼娃瓦袜佤娲腽",wai:"歪外",wan:"豌弯湾玩顽丸烷完碗挽晚皖惋宛婉万腕剜芄苋菀纨绾琬脘畹蜿箢",wang:"汪王亡枉网往旺望忘妄罔尢惘辋魍",wei:"威巍微危韦违桅围唯惟为潍维苇萎委伟伪尾纬未蔚味畏胃喂魏位渭谓尉慰卫倭偎诿隈葳薇帏帷崴嵬猥猬闱沩洧涠逶娓玮韪軎炜煨熨痿艉鲔",wen:"瘟温蚊文闻纹吻稳紊问刎愠阌汶璺韫殁雯",weng:"嗡翁瓮蓊蕹",wo:"挝蜗涡窝我斡卧握沃莴幄渥杌肟龌",wu:"巫呜钨乌污诬屋无芜梧吾吴毋武五捂午舞伍侮坞戊雾晤物勿务悟误兀仵阢邬圬芴庑怃忤浯寤迕妩骛牾焐鹉鹜蜈鋈鼯",xi:"昔熙析西硒矽晰嘻吸锡牺稀息希悉膝夕惜熄烯溪汐犀檄袭席习媳喜铣洗系隙戏细僖兮隰郗茜葸蓰奚唏徙饩阋浠淅屣嬉玺樨曦觋欷熹禊禧钸皙穸蜥蟋舾羲粞翕醯鼷",xia:"瞎虾匣霞辖暇峡侠狭下厦夏吓掀葭嗄狎遐瑕硖瘕罅黠",xian:"锨先仙鲜纤咸贤衔舷闲涎弦嫌显险现献县腺馅羡宪陷限线冼藓岘猃暹娴氙祆鹇痫蚬筅籼酰跹",xiang:"相厢镶香箱襄湘乡翔祥详想响享项巷橡像向象芗葙饷庠骧缃蟓鲞飨",xiao:"萧硝霄削哮嚣销消宵淆晓小孝校肖啸笑效哓咻崤潇逍骁绡枭枵筱箫魈",xie:"楔些歇蝎鞋协挟携邪斜胁谐写械卸蟹懈泄泻谢屑偕亵勰燮薤撷廨瀣邂绁缬榭榍歙躞",xin:"薪芯锌欣辛新忻心信衅囟馨莘歆铽鑫",xing:"星腥猩惺兴刑型形邢行醒幸杏性姓陉荇荥擤悻硎",xiong:"兄凶胸匈汹雄熊芎",xiu:"休修羞朽嗅锈秀袖绣莠岫馐庥鸺貅髹",xu:"墟戌需虚嘘须徐许蓄酗叙旭序畜恤絮婿绪续讴诩圩蓿怵洫溆顼栩煦砉盱胥糈醑",xuan:"轩喧宣悬旋玄选癣眩绚儇谖萱揎馔泫洵渲漩璇楦暄炫煊碹铉镟痃",xue:"靴薛学穴雪血噱泶鳕",xun:"勋熏循旬询寻驯巡殉汛训讯逊迅巽埙荀薰峋徇浔曛窨醺鲟",ya:"压押鸦鸭呀丫芽牙蚜崖衙涯雅哑亚讶伢揠吖岈迓娅琊桠氩砑睚痖",yan:"焉咽阉烟淹盐严研蜒岩延言颜阎炎沿奄掩眼衍演艳堰燕厌砚雁唁彦焰宴谚验厣靥赝俨偃兖讠谳郾鄢芫菸崦恹闫阏洇湮滟妍嫣琰晏胭腌焱罨筵酽魇餍鼹",yang:"殃央鸯秧杨扬佯疡羊洋阳氧仰痒养样漾徉怏泱炀烊恙蛘鞅",yao:"邀腰妖瑶摇尧遥窑谣姚咬舀药要耀夭爻吆崾徭瀹幺珧杳曜肴鹞窈繇鳐",ye:"椰噎耶爷野冶也页掖业叶曳腋夜液谒邺揶馀晔烨铘",yi:"一壹医揖铱依伊衣颐夷遗移仪胰疑沂宜姨彝椅蚁倚已乙矣以艺抑易邑屹亿役臆逸肄疫亦裔意毅忆义益溢诣议谊译异翼翌绎刈劓佾诒圪圯埸懿苡薏弈奕挹弋呓咦咿噫峄嶷猗饴怿怡悒漪迤驿缢殪贻旖熠钇镒镱痍瘗癔翊衤蜴舣羿翳酏黟",yin:"茵荫因殷音阴姻吟银淫寅饮尹引隐印胤鄞堙茚喑狺夤氤铟瘾蚓霪龈",ying:"英樱婴鹰应缨莹萤营荧蝇迎赢盈影颖硬映嬴郢茔莺萦撄嘤膺滢潆瀛瑛璎楹鹦瘿颍罂",yo:"哟唷",yong:"拥佣臃痈庸雍踊蛹咏泳涌永恿勇用俑壅墉慵邕镛甬鳙饔",you:"幽优悠忧尤由邮铀犹油游酉有友右佑釉诱又幼卣攸侑莸呦囿宥柚猷牖铕疣蝣鱿黝鼬",yu:"迂淤于盂榆虞愚舆余俞逾鱼愉渝渔隅予娱雨与屿禹宇语羽玉域芋郁吁遇喻峪御愈欲狱育誉浴寓裕预豫驭禺毓伛俣谀谕萸蓣揄喁圄圉嵛狳饫庾阈妪妤纡瑜昱觎腴欤於煜燠聿钰鹆瘐瘀窳蝓竽舁雩龉",yuan:"鸳渊冤元垣袁原援辕园员圆猿源缘远苑愿怨院塬沅媛瑗橼爰眢鸢螈鼋",yue:"曰约越跃钥岳粤月悦阅龠樾刖钺",yun:"耘云郧匀陨允运蕴酝晕韵孕郓芸狁恽纭殒昀氲",za:"匝砸杂拶咂",zai:"栽哉灾宰载再在咱崽甾",zan:"攒暂赞瓒昝簪糌趱錾",zang:"赃脏葬奘戕臧",zao:"遭糟凿藻枣早澡蚤躁噪造皂灶燥唣缫",ze:"责择则泽仄赜啧迮昃笮箦舴",zei:"贼",zen:"怎谮",zeng:"增憎曾赠缯甑罾锃",zha:"扎喳渣札轧铡闸眨栅榨咋乍炸诈揸吒咤哳怍砟痄蚱齄",zhai:"摘斋宅窄债寨砦",zhan:"瞻毡詹粘沾盏斩辗崭展蘸栈占战站湛绽谵搌旃",zhang:"樟章彰漳张掌涨杖丈帐账仗胀瘴障仉鄣幛嶂獐嫜璋蟑",zhao:"招昭找沼赵照罩兆肇召爪诏棹钊笊",zhe:"遮折哲蛰辙者锗蔗这浙谪陬柘辄磔鹧褚蜇赭",zhen:"珍斟真甄砧臻贞针侦枕疹诊震振镇阵缜桢榛轸赈胗朕祯畛鸩",zheng:"蒸挣睁征狰争怔整拯正政帧症郑证诤峥钲铮筝",zhi:"芝枝支吱蜘知肢脂汁之织职直植殖执值侄址指止趾只旨纸志挚掷至致置帜峙制智秩稚质炙痔滞治窒卮陟郅埴芷摭帙忮彘咫骘栉枳栀桎轵轾攴贽膣祉祗黹雉鸷痣蛭絷酯跖踬踯豸觯",zhong:"中盅忠钟衷终种肿重仲众冢锺螽舂舯踵",zhou:"舟周州洲诌粥轴肘帚咒皱宙昼骤啄着倜诹荮鬻纣胄碡籀舳酎鲷",zhu:"珠株蛛朱猪诸诛逐竹烛煮拄瞩嘱主著柱助蛀贮铸筑住注祝驻伫侏邾苎茱洙渚潴驺杼槠橥炷铢疰瘃蚰竺箸翥躅麈",zhua:"抓",zhuai:"拽",zhuan:"专砖转撰赚篆抟啭颛",zhuang:"桩庄装妆撞壮状丬",zhui:"椎锥追赘坠缀萑骓缒",zhun:"谆准",zhuo:"捉拙卓桌琢茁酌灼浊倬诼廴蕞擢啜浞涿杓焯禚斫",zi:"兹咨资姿滋淄孜紫仔籽滓子自渍字谘嵫姊孳缁梓辎赀恣眦锱秭耔笫粢觜訾鲻髭",zong:"鬃棕踪宗综总纵腙粽",zou:"邹走奏揍鄹鲰",zu:"租足卒族祖诅阻组俎菹啐徂驵蹴",zuan:"钻纂攥缵",zui:"嘴醉最罪",zun:"尊遵撙樽鳟",zuo:"昨左佐柞做作坐座阝阼胙祚酢",cou:"薮楱辏腠",nang:"攮哝囔馕曩",o:"喔",dia:"嗲",chuai:"嘬膪踹",cen:"岑涔",diu:"铥",nou:"耨",fou:"缶",bia:"髟"},a=e.length,o="",r=new RegExp("[a-zA-Z0-9- ]"),l=0;l<a;l++){var i,n=e.substr(l,1);for(var s in t)if(-1!==t[s].indexOf(n)){if(s.length>0){var c=s.substr(0,1).toLowerCase();i=c}break}r.test(n)?o+=n:!1!==i&&(o+=i)}o=o.replace(/ /g,"-");while(o.indexOf("--")>0)o=o.replace("--","-");return o}}),p=a("c958"),m={name:"",components:{ImageUpload:s["a"],selectUser:p["a"]},props:{deptId:{type:String,default:""},empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){var e=this,t=function(e,t,a){var o=/^[1][3,4,5,6,7,8,9][0-9]{9}$/;return t?o.test(t)?void a():a(new Error("请输入正确的手机")):a(new Error("请输入手机"))},a=function(e,t,a){var o=/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;if(!t)return a();setTimeout((function(){o.test(t)?a():a(new Error("请输入正确的邮箱格式"))}),100)},o=function(t,a,o){if(!a)return o(new Error("请输入工号"));setTimeout((function(){n["a"].isEmpCodeExists(e.tempData.tempFormModel).then((function(e){e.data?o(new Error("工号不允许重复")):o()})).catch((function(e){console.log(e)}))}),100)},r=function(t,a,o){if(!a)return o();var r=e.tempData.tempFormModel.documentTypeId,i=e.documentTypeOptions.filter((function(e,t,a){if(e.id===r)return e}));if(a.length>17){var n=a.substr(16,1);n%=2,e.tempData.tempFormModel.enumGender=0===n?2:1}i.length>0&&"1"===i[0].code?l(o):o()},l=function(t){n["a"].checkIdentityNumber(e.tempData.tempFormModel).then((function(e){e.data?t(new Error("身份证号不允许重复")):t()}))},i=function(t,a,o){var r=/^\d{4}-\d{2}-\d{2}$/;return a?r.test(a)?void(!1===e.checkDate(a)?o(new Error("日期不合法")):o()):o(new Error("日期格式不正确! 例如: 2022-09-04")):o(new Error("日期不可为空"))};return{rules:{displayName:[{required:!0,message:"请输入姓名",trigger:"blur"},{max:50,message:"姓名不允许超过50个字符",trigger:"blur"}],pinyin:[{required:!0,message:"请输入拼音",trigger:"blur"},{max:50,message:"拼音简写不允许超过50个字符",trigger:"blur"}],empCode:[{required:!0,message:"请输入工号",trigger:"blur"},{max:50,message:"工号不允许超过50个字符",trigger:"blur"},{validator:o,trigger:"blur"}],birthday:[{required:!0,message:"请输入生日",trigger:"blur"},{validator:i,trigger:"blur"}],age:[{required:!1,type:"number",min:0,max:9999,message:"年龄必须在0到9999之间",transform:function(e){return e?Number(e):null}}],nativePlace:[{max:50,message:"籍贯不允许超过50位字符",trigger:"blur"}],documentTypeId:[{required:!0,message:"请选择证件类型",trigger:"blur"}],identityNumber:[{required:!0,message:"请输入证件编号",trigger:"blur"},{validator:r,trigger:"blur"}],email:[{max:100,message:"电子邮件不允许超过100位字符",trigger:"blur"},{validator:a,trigger:"blur"}],phone:[{max:30,message:"家庭电话不允许超过30位字符",trigger:"blur"}],mobile:[{required:!0,message:"请输入手机",trigger:"blur"},{max:30,message:"手机不允许超过30位字符",trigger:"blur"},{validator:t,trigger:"blur"}],address:[{max:50,message:"家庭地址不允许超过50位字符",trigger:"blur"}],zipCode:[{max:30,message:"家庭地址邮编不允许超过30位字符",trigger:"blur"}],deptId:[{required:!0,message:"请输选择部门",trigger:"blur"}]},addBaseInfoDialogVisible:!1,tempData:{tempFormModel:{}},departmentOptions:[],departmentOptions2:[],datePickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()-864e4}},marryOptions:[],nationalityOptions:[],registerTypeOptions:[],birthday:null,checkDisplayNameDialogVisible:!1,listQuery:{pageIndex:1,pageSize:5},total:0,pageEmpList:[],listLoading:!1,attachmentsUploadUrl:"",documentTypeOptions:[],deptPrincipa:{id:null,name:""},empDeptId:""}},watch:{},created:function(){this.loadDepartment(),this.loadDepartment2(),this.loadMarryOptions(),this.loadNationalityOptions(),this.loadRegisterTypeOptions(),this.loadDocumentTypeOptions(),this.init(),this.queryDeptPrincipalMethod()},methods:{checkDate:function(e){var t=[0,31,28,31,30,31,30,31,31,30,31,30,31],a=e.split("-"),o=parseInt(a[0],10),r=parseInt(a[1],10),l=parseInt(a[2],10);return!(r>12||r<1)&&((o%4===0&&o%100!==0||o%400===0)&&(t[2]=29),!(l>t[r])&&void 0)},downloadexceltemplate:function(){n["a"].downlodaImportExcelTemplate({type:"importemp"}).then((function(e){var t=a("19de"),o="EmployeeTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},birthdaychange:function(e){if(e){var t=new Date(e.replace(/-/g,"/")),a=new Date,o=a.getFullYear()-t.getFullYear();(a.getMonth()<t.getMonth()||a.getMonth()===t.getMonth()&&a.getDate()<t.getDate())&&(o-=1),this.tempData.tempFormModel.birthday=e,this.tempData.tempFormModel.age=o}},importExcel:function(e){var t=this,a=e.file;n["a"].importExcel(a,{type:"importemp"}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a)}})).catch((function(e){}))},loadDepartment:function(){var e=this;i["a"].QueryOrganization({}).then((function(t){e.departmentOptions=t.data})).catch((function(e){console.log(e)}))},loadDepartment2:function(){var e=this;i["a"].QueryOrganization({}).then((function(t){e.departmentOptions2=t.data})).catch((function(e){console.log(e)}))},loadMarryOptions:function(){var e=this;n["a"].queryMarryList().then((function(t){e.marryOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadNationalityOptions:function(){var e=this;n["a"].queryNationality().then((function(t){e.nationalityOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadRegisterTypeOptions:function(){var e=this;n["a"].queryRegisterType().then((function(t){e.registerTypeOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadDocumentTypeOptions:function(){var e=this;n["a"].queryDocumentType().then((function(t){e.documentTypeOptions=t.data.datas,console.log(e.documentTypeOptions)})).catch((function(e){console.log(e)}))},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.checkpartTimeDeptId(),""===e.deptPrincipa.name&&(e.deptPrincipa.id=null),e.tempData.tempFormModel.deptPrincipalID=e.deptPrincipa.id,e.tempData.tempFormModel.id?e.update():e.addNew())}))},selectEmployeeDialog:function(){this.$refs.selectempc.showEmp=!0},setEmp:function(e){this.deptPrincipa.name=e.uid+"-"+e.displayName,this.deptPrincipa.id=e.id},queryDeptPrincipalMethod:function(){var e=this;n["a"].queryDeptPrincipal({deptid:this.tempData.tempFormModel.deptId}).then((function(t){t.data&&(e.deptPrincipa.name=t.data.uid+"-"+t.data.displayName,e.deptPrincipa.id=t.data.id)}))},checkpartTimeDeptId:function(){var e=this.tempData.tempFormModel.partTimeDeptId1;"string"!==typeof e&&(void 0!==e&&""!==e&&null!==e&&"object"===Object(l["a"])(e)&&e.length>0?this.tempData.tempFormModel.partTimeDeptId1=e[e.length-1]:this.tempData.tempFormModel.partTimeDeptId1="");var t=this.tempData.tempFormModel.partTimeDeptId2;"string"!==typeof t&&(void 0!==t&&""!==t&&null!==t&&"object"===Object(l["a"])(t)&&t.length>0?this.tempData.tempFormModel.partTimeDeptId2=t[t.length-1]:this.tempData.tempFormModel.partTimeDeptId2="")},addNew:function(){var e=this;n["a"].addEmployee(this.tempData.tempFormModel).then((function(t){e.addBaseInfoDialogVisible=!1,t.succeed?(e.tempData.tempFormModel=t.data,e.$emit("updateEmpId",e.tempData.tempFormModel.id),e.$notice.message("请继续更新人事信息，否则该信息无法推送","error"),e.uploadEmployee(e.tempData.tempFormModel.uid,1)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.addBaseInfoDialogVisible=!1,t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;n["a"].updateEmployee(this.tempData.tempFormModel).then((function(t){e.addBaseInfoDialogVisible=!1,t.succeed?(void 0===t.data.hireStyleName?e.$notice.message("请继续更新人事信息，否则该信息无法推送","error"):(e.$notice.message("修改成功。","success"),e.uploadEmployee(e.tempData.tempFormModel.uid,2)),e.uploadEmployee(e.tempData.tempFormModel.uid,2)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.addBaseInfoDialogVisible=!1,t.processed||e.$notice.message("修改失败。","error")}))},uploadEmployee:function(e,t){var a={condition:e,addnewFlag:t};n["a"].getPersonnelInformation(a).then((function(e){})).catch((function(e){console.log(e)}))},clear:function(){this.$refs["dataForm"].resetFields();var e=this.tempData.tempFormModel.id;this.tempData.tempFormModel={},this.tempData.tempFormModel.deptId=this.deptId?this.deptId:this.empDeptId,this.tempData.tempFormModel.enumGender=1,this.birthday="",e&&(this.tempData.tempFormModel.id=e)},init:function(){this.deptId&&(this.tempData.tempFormModel.deptId=this.deptId),this.empId?this.getEmployeeInfo(this.empId):this.tempData.tempFormModel.enumGender=1},getEmployeeInfo:function(e){var t=this;n["a"].getEmployee({id:e}).then((function(e){t.tempData.tempFormModel=e.data,t.birthday=t.tempData.tempFormModel.birthday,t.empDeptId=t.tempData.tempFormModel.deptId,t.tempData.tempFormModel.enumGender||(t.tempData.tempFormModel.enumGender=1),t.tempData.tempFormModel.documentTypeId||(t.tempData.tempFormModel.documentTypeId="e0d80aae-bb5d-4e1c-901e-d269723374b0")})).catch((function(e){console.log(e)}))},checkDisplayName:function(){var e=this;this.listLoading=!0,this.listQuery.displayName=this.tempData.tempFormModel.displayName,n["a"].queryEmployee(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageEmpList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex,e.checkDisplayNameDialogVisible=!0):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},getSuoxie:function(){var e=c.ConvertPinyin(this.tempData.tempFormModel.displayName);this.tempData.tempFormModel.pinyin=e},getEmpList:function(){var e=this;this.listLoading=!0,this.listQuery.displayName=this.tempData.tempFormModel.displayName,n["a"].queryEmployee(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageEmpList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getEmpList()},handleChooseEmployee:function(e){this.getEmployeeInfo(e.id),this.checkDisplayNameDialogVisible=!1},getReturnData:function(e){this.tempData.tempFormModel.attachmentId=e}}},d=m,u=(a("40ef"),a("2877")),h=Object(u["a"])(d,o,r,!1,null,"57c2c3d4",null);t["a"]=h.exports},"2cf0":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1):e._e(),e.userPermission.isShowBtnClear?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document-delete"},on:{click:e.clear}},[e._v("清除")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"进院日期",prop:"hireDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择进院日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.hireDate,callback:function(t){e.$set(e.tempData.tempFormModel,"hireDate",t)},expression:"tempData.tempFormModel.hireDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"06认定连续工龄",prop:"societyAge06"}},[a("el-input",{attrs:{placeholder:"请输入06认定连续工龄"},model:{value:e.tempData.tempFormModel.societyAge06,callback:function(t){e.$set(e.tempData.tempFormModel,"societyAge06",t)},expression:"tempData.tempFormModel.societyAge06"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"毕业院校 ",prop:"graduate"}},[a("el-input",{attrs:{disabled:!0,placeholder:"毕业院校"},model:{value:e.tempData.tempFormModel.graduate,callback:function(t){e.$set(e.tempData.tempFormModel,"graduate",t)},expression:"tempData.tempFormModel.graduate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"参加工作日期",prop:"societyDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择参加工作日期","value-format":"yyyy-MM-dd","picker-options":e.startPickerDisabled},model:{value:e.tempData.tempFormModel.societyDate,callback:function(t){e.$set(e.tempData.tempFormModel,"societyDate",t)},expression:"tempData.tempFormModel.societyDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"实际工龄",prop:"societyAge"}},[a("el-input",{attrs:{type:"number",placeholder:"实际工龄"},model:{value:e.tempData.tempFormModel.societyAge,callback:function(t){e.$set(e.tempData.tempFormModel,"societyAge",e._n(t))},expression:"tempData.tempFormModel.societyAge"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"最高学位",prop:"degreeId"}},[a("el-select",{attrs:{disabled:!0,placeholder:"最高学位"},model:{value:e.tempData.tempFormModel.degreeId,callback:function(t){e.$set(e.tempData.tempFormModel,"degreeId",t)},expression:"tempData.tempFormModel.degreeId"}},e._l(e.degreeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"报到日期",prop:"effHireDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择报到日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.effHireDate,callback:function(t){e.$set(e.tempData.tempFormModel,"effHireDate",t)},expression:"tempData.tempFormModel.effHireDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"本院工龄",prop:"companyAge"}},[a("el-input",{attrs:{type:"number",placeholder:"本院工龄"},model:{value:e.tempData.tempFormModel.companyAge,callback:function(t){e.$set(e.tempData.tempFormModel,"companyAge",e._n(t))},expression:"tempData.tempFormModel.companyAge"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"最高学历",prop:"educationId"}},[a("el-select",{attrs:{disabled:!0,placeholder:"最高学历"},model:{value:e.tempData.tempFormModel.educationId,callback:function(t){e.$set(e.tempData.tempFormModel,"educationId",t)},expression:"tempData.tempFormModel.educationId"}},e._l(e.educationOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入编日期",prop:"enterDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择入编日期","value-format":"yyyy-MM-dd","picker-options":e.datePickerOptions},model:{value:e.tempData.tempFormModel.enterDate,callback:function(t){e.$set(e.tempData.tempFormModel,"enterDate",t)},expression:"tempData.tempFormModel.enterDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"外语语种",prop:"fLanguageType"}},[a("el-input",{attrs:{placeholder:"请输入外语语种"},model:{value:e.tempData.tempFormModel.fLanguageType,callback:function(t){e.$set(e.tempData.tempFormModel,"fLanguageType",t)},expression:"tempData.tempFormModel.fLanguageType"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"外语水平",prop:"fLanguageLevel"}},[a("el-input",{attrs:{placeholder:"请输入外语水平"},model:{value:e.tempData.tempFormModel.fLanguageLevel,callback:function(t){e.$set(e.tempData.tempFormModel,"fLanguageLevel",t)},expression:"tempData.tempFormModel.fLanguageLevel"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"招募类别",prop:"recruitmentCategoryId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择招募类别"},model:{value:e.tempData.tempFormModel.recruitmentCategoryId,callback:function(t){e.$set(e.tempData.tempFormModel,"recruitmentCategoryId",t)},expression:"tempData.tempFormModel.recruitmentCategoryId"}},e._l(e.recruitmentCategoryOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"招募公司",prop:"recruitmentCompanyId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择招募公司"},model:{value:e.tempData.tempFormModel.recruitmentCompanyId,callback:function(t){e.$set(e.tempData.tempFormModel,"recruitmentCompanyId",t)},expression:"tempData.tempFormModel.recruitmentCompanyId"}},e._l(e.recruitmentCompanyOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"员工状态",prop:"empStatusId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择员工状态"},model:{value:e.tempData.tempFormModel.empStatusId,callback:function(t){e.$set(e.tempData.tempFormModel,"empStatusId",t)},expression:"tempData.tempFormModel.empStatusId"}},e._l(e.empStatusOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"在职方式",prop:"hireStyleId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择在职方式"},model:{value:e.tempData.tempFormModel.hireStyleId,callback:function(t){e.$set(e.tempData.tempFormModel,"hireStyleId",t)},expression:"tempData.tempFormModel.hireStyleId"}},e._l(e.hireStyleOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"离职方式",prop:"leaveStyleId"}},[a("el-select",{attrs:{clearable:"",placeholder:"离职方式"},model:{value:e.tempData.tempFormModel.leaveStyleId,callback:function(t){e.$set(e.tempData.tempFormModel,"leaveStyleId",t)},expression:"tempData.tempFormModel.leaveStyleId"}},e._l(e.leaveStyleOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"离职日期",prop:"leaveDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择离职日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.leaveDate,callback:function(t){e.$set(e.tempData.tempFormModel,"leaveDate",t)},expression:"tempData.tempFormModel.leaveDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"离职原因",prop:"leaveReason"}},[a("el-input",{attrs:{placeholder:"请输入离职原因"},model:{value:e.tempData.tempFormModel.leaveReason,callback:function(t){e.$set(e.tempData.tempFormModel,"leaveReason",t)},expression:"tempData.tempFormModel.leaveReason"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"离退休证号",prop:"retireNo"}},[a("el-input",{attrs:{placeholder:"请输入离退休证号"},model:{value:e.tempData.tempFormModel.retireNo,callback:function(t){e.$set(e.tempData.tempFormModel,"retireNo",t)},expression:"tempData.tempFormModel.retireNo"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"岗位名称",prop:"positionName"}},[a("el-input",{attrs:{disabled:!0,placeholder:"岗位名称"},model:{value:e.tempData.tempFormModel.positionName,callback:function(t){e.$set(e.tempData.tempFormModel,"positionName",t)},expression:"tempData.tempFormModel.positionName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"岗位级别",prop:"positionLevel"}},[a("el-input",{attrs:{disabled:!0,placeholder:"岗位级别"},model:{value:e.tempData.tempFormModel.positionLevel,callback:function(t){e.$set(e.tempData.tempFormModel,"positionLevel",t)},expression:"tempData.tempFormModel.positionLevel"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职称资格",prop:"titleOfTechnical"}},[a("el-input",{attrs:{disabled:!0,placeholder:"职称资格"},model:{value:e.tempData.tempFormModel.titleOfTechnical,callback:function(t){e.$set(e.tempData.tempFormModel,"titleOfTechnical",t)},expression:"tempData.tempFormModel.titleOfTechnical"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职别",prop:"officialRankId"}},[a("el-select",{attrs:{clearable:"",placeholder:"职别"},model:{value:e.tempData.tempFormModel.officialRankId,callback:function(t){e.$set(e.tempData.tempFormModel,"officialRankId",t)},expression:"tempData.tempFormModel.officialRankId"}},e._l(e.rankOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"拟退休日期",prop:"retireDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择拟退休日期","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormModel.proposedRetireDate,callback:function(t){e.$set(e.tempData.tempFormModel,"proposedRetireDate",t)},expression:"tempData.tempFormModel.proposedRetireDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"退休日期",prop:"retireDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择退休日期","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormModel.retireDate,callback:function(t){e.$set(e.tempData.tempFormModel,"retireDate",t)},expression:"tempData.tempFormModel.retireDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"已故日期",prop:"deadDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择已故日期","value-format":"yyyy-MM-dd","picker-options":e.datePickerOptions},model:{value:e.tempData.tempFormModel.deadDate,callback:function(t){e.$set(e.tempData.tempFormModel,"deadDate",t)},expression:"tempData.tempFormModel.deadDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"档案编号",prop:"dossierNumber"}},[a("el-input",{attrs:{placeholder:"请输入档案编号"},model:{value:e.tempData.tempFormModel.dossierNumber,callback:function(t){e.$set(e.tempData.tempFormModel,"dossierNumber",t)},expression:"tempData.tempFormModel.dossierNumber"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注",prop:"memo"}},[a("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.tempData.tempFormModel.memo,callback:function(t){e.$set(e.tempData.tempFormModel,"memo",t)},expression:"tempData.tempFormModel.memo"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"附加文件",prop:"file"}},[a("file-upload",{ref:"upload",model:{value:e.tempData.tempFormModel.attachment,callback:function(t){e.$set(e.tempData.tempFormModel,"attachment",t)},expression:"tempData.tempFormModel.attachment"}})],1)],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i=a("3462"),n={name:"",components:{FileUpload:i["a"]},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.retireDate){var t=new Date(e.tempData.tempFormModel.retireDate),r=new Date(a);r<t?o():o(new Error("参加工作日期不得晚于退休日期。"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.societyDate){var t=new Date(e.tempData.tempFormModel.societyDate),r=new Date(a);r>t?o():o(new Error("退休日期不得早于参加工作日期。"))}else o()}),100)};return{rules:{hireDate:[{required:!0,message:"请输入进院日期",trigger:"blur"}],effHireDate:[{required:!0,message:"请输入报到日期",trigger:"blur"}],empStatusId:[{required:!0,message:"请选择员工状态",trigger:"blur"}],hireStyleId:[{required:!0,message:"请选择在职方式",trigger:"blur"}],officialRankId:[{required:!0,message:"请选择职别",trigger:"blur"}],societyAge06:[{required:!1,type:"number",min:0,max:999,message:"06认定连续工龄必须在0到999之间",transform:function(e){return e?Number(e):null}},{pattern:/^[1-9][0-9]*$/,message:"仅支持整数"}],graduate:[{required:!1,message:"请输入毕业院校",trigger:"blur"},{max:50,message:"毕业院校不允许超过50个字符",trigger:"blur"}],societyDate:[{validator:t,trigger:"blur"}],retireDate:[{validator:a,trigger:"blur"}],fLanguageType:[{max:50,message:"外语语种不允许超过50个字符",trigger:"blur"}],fLanguageLevel:[{max:50,message:"外语水平不允许超过50个字符",trigger:"blur"}],leaveReason:[{max:200,message:"离职原因不允许超过200位字符",trigger:"blur"}],retireNo:[{max:50,message:"离退休证号不允许超过50位字符",trigger:"blur"}],dossierNumber:[{max:50,message:"档案编号不允许超过50位字符",trigger:"blur"}]},tempData:{tempFormModel:{}},startPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.retireDate){var a=new Date(e.tempData.tempFormModel.retireDate);return t.getTime()>a}}},endPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.societyDate){var a=new Date(e.tempData.tempFormModel.societyDate);return t.getTime()<a}}},datePickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()-864e4}},degreeOptions:[],educationOptions:[],partyOptions:[],empStatusOptions:[],recruitmentCategoryOptions:[],recruitmentCompanyOptions:[],hireStyleOptions:[],leaveStyleOptions:[],rankOptions:[]}},computed:{societyAge:function(){var e=parseInt(this.tempData.tempFormModel.societyAge06,10);e||(e=0);var t=0;if(0!==e)t=(new Date).getFullYear()-2006+e-this.tempData.tempFormModel.countOfDeductWorkingAge;else if(this.tempData.tempFormModel.societyDate){var a=new Date(this.tempData.tempFormModel.societyDate);t=(new Date).getFullYear()-a.getFullYear()+1-this.tempData.tempFormModel.countOfDeductWorkingAge}else t=0;return t},companyAge:function(){var e=parseInt(this.tempData.tempFormModel.societyAge06,10);e||(e=0);var t=0;if(0!==e)if(this.tempData.tempFormModel.hireDate&&this.tempData.tempFormModel.societyDate){var a=new Date(this.tempData.tempFormModel.hireDate),o=new Date(this.tempData.tempFormModel.societyDate);t=(new Date).getFullYear()-2006+e-this.tempData.tempFormModel.countOfDeductWorkingAge-(a.getFullYear()-o.getFullYear())}else t=0;else if(this.tempData.tempFormModel.hireDate){var r=new Date(this.tempData.tempFormModel.hireDate);t=(new Date).getFullYear()-r.getFullYear()+1-this.tempData.tempFormModel.countOfDeductWorkingAge}else t=0;return t}},created:function(){this.loadDegreeOptions(),this.loadEducationOptions(),this.loadEmployeeStatus(),this.loadRecruitmentCategory(),this.loadRecruitmentCompany(),this.loadEmployeeRank(),this.loadEmployeeHireStyle(),this.loadEmployeeLeaveStyle(),this.init()},methods:{downloadexceltemplate:function(){l["a"].downlodaImportExcelTemplate({type:"importemphr"}).then((function(e){var t=a("19de"),o="EmployeeHRTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file;l["a"].importExcel(a,{type:"importemphr"}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a)}})).catch((function(e){}))},init:function(){this.empId&&this.getEmployeeHR(this.empId)},getEmployeeHR:function(e){var t=this;l["a"].getEmployeeHR({id:e}).then((function(e){if(e.data&&(t.tempData.tempFormModel=e.data,t.tempData.tempFormModel.attachmentId)){var a={id:t.tempData.tempFormModel.attachmentId,fileName:t.tempData.tempFormModel.attachmentName};t.tempData.tempFormModel.attachment=a}})).catch((function(e){console.log(e)}))},loadDegreeOptions:function(){var e=this;l["a"].queryDegrees().then((function(t){e.degreeOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEducationOptions:function(){var e=this;l["a"].queryEducation().then((function(t){e.educationOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadPartyOptions:function(){var e=this;l["a"].queryParty().then((function(t){e.partyOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeStatus:function(){var e=this;l["a"].queryEmployeeStatus().then((function(t){e.empStatusOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadRecruitmentCategory:function(){var e=this;l["a"].queryRecruitmentCategory().then((function(t){e.recruitmentCategoryOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadRecruitmentCompany:function(){var e=this;l["a"].queryRecruitmentCompany().then((function(t){e.recruitmentCompanyOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeRank:function(){var e=this;l["a"].queryOfficialRank().then((function(t){e.rankOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeHireStyle:function(){var e=this;l["a"].queryHireStyle().then((function(t){e.hireStyleOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEmployeeLeaveStyle:function(){var e=this;l["a"].queryLeaveStyle().then((function(t){e.leaveStyleOptions=t.data.datas})).catch((function(e){console.log(e)}))},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.societyAge06&&(e.tempData.tempFormModel.societyAge06=parseInt(e.tempData.tempFormModel.societyAge06,10)),e.tempData.tempFormModel.attachment?(e.tempData.tempFormModel.attachmentId=e.tempData.tempFormModel.attachment.id,e.tempData.tempFormModel.attachmentName=e.tempData.tempFormModel.attachment.fileName):(delete e.tempData.tempFormModel.attachmentId,delete e.tempData.tempFormModel.attachmentName),e.empId?e.update():e.$notice.message("请先新增基本信息。","info"))}))},update:function(){var e=this;this.tempData.tempFormModel.id=this.empId,l["a"].updateEmployeeHR(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel=t.data,e.$notice.message("修改成功。","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},uploadEmployee:function(e){var t={condition:e};l["a"].getPersonnelInformation(t).then((function(e){})).catch((function(e){console.log(e)}))},clear:function(){this.$refs["dataForm"].resetFields(),this.$refs.upload.clear()}}},s=n,c=(a("34cc"),a("2877")),p=Object(c["a"])(s,o,r,!1,null,"0e8a78d8",null);t["a"]=p.exports},"2e19":function(e,t,a){"use strict";var o=a("a282"),r=a.n(o);r.a},"31ce":function(e,t,a){},3220:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所去国家",prop:"toCountry"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入所去国家"},model:{value:e.tempData.tempFormModel.toCountry,callback:function(t){e.$set(e.tempData.tempFormModel,"toCountry",t)},expression:"tempData.tempFormModel.toCountry"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所去机构",prop:"toInstitute"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入所去机构"},model:{value:e.tempData.tempFormModel.toInstitute,callback:function(t){e.$set(e.tempData.tempFormModel,"toInstitute",t)},expression:"tempData.tempFormModel.toInstitute"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出国类别",prop:"typeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择出国类别"},model:{value:e.tempData.tempFormModel.typeId,callback:function(t){e.$set(e.tempData.tempFormModel,"typeId",t)},expression:"tempData.tempFormModel.typeId"}},e._l(e.typeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出国时间",prop:"startDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择出国时间","value-format":"yyyy-MM-dd","picker-options":e.startPickerDisabled},model:{value:e.tempData.tempFormModel.startDate,callback:function(t){e.$set(e.tempData.tempFormModel,"startDate",t)},expression:"tempData.tempFormModel.startDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划回国时间",prop:"planEndDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择计划回国时间","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.planEndDate,callback:function(t){e.$set(e.tempData.tempFormModel,"planEndDate",t)},expression:"tempData.tempFormModel.planEndDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际回国时间",prop:"endDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择实际回国时间","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormModel.endDate,callback:function(t){e.$set(e.tempData.tempFormModel,"endDate",t)},expression:"tempData.tempFormModel.endDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"出国原因",prop:"reason"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入出国原因"},model:{value:e.tempData.tempFormModel.reason,callback:function(t){e.$set(e.tempData.tempFormModel,"reason",t)},expression:"tempData.tempFormModel.reason"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"派出机构",prop:"dispatchInstitute"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入派出机构"},model:{value:e.tempData.tempFormModel.dispatchInstitute,callback:function(t){e.$set(e.tempData.tempFormModel,"dispatchInstitute",t)},expression:"tempData.tempFormModel.dispatchInstitute"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"出国经费",prop:"outlay"}},[a("el-input",{attrs:{type:"number",clearable:"",placeholder:"请输入出国经费"},model:{value:e.tempData.tempFormModel.outlay,callback:function(t){e.$set(e.tempData.tempFormModel,"outlay",e._n(t))},expression:"tempData.tempFormModel.outlay"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"经费来源",prop:"financialSource"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入经费来源"},model:{value:e.tempData.tempFormModel.financialSource,callback:function(t){e.$set(e.tempData.tempFormModel,"financialSource",t)},expression:"tempData.tempFormModel.financialSource"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"出国情况",prop:"instance"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入出国情况"},model:{value:e.tempData.tempFormModel.instance,callback:function(t){e.$set(e.tempData.tempFormModel,"instance",t)},expression:"tempData.tempFormModel.instance"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageAbroadList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"所去国家","min-width":"100px",sortable:"custom",prop:"ToCountry"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.toCountry))])]}}])}),a("el-table-column",{attrs:{label:"所去机构","min-width":"80px",sortable:"custom",prop:"ToInstitute"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.toInstitute))])]}}])}),a("el-table-column",{attrs:{label:"出国类别","min-width":"80px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.typeName))])]}}])}),a("el-table-column",{attrs:{label:"派出机构","min-width":"80px",sortable:"custom",prop:"DispatchInstitute"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dispatchInstitute))])]}}])}),a("el-table-column",{attrs:{label:"出国时间","min-width":"100px",sortable:"custom",prop:"StartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.startDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"实际回国时间","min-width":"100px",sortable:"custom",prop:"EndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.endDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteAbroad(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getAbroadList}})],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.endDate){var t=new Date(e.tempData.tempFormModel.endDate),r=new Date(a);r<t?o():o(new Error("出国时间不得晚于实际回国时间。"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.startDate){var t=new Date(e.tempData.tempFormModel.startDate),r=new Date(a);r>t?o():o(new Error("实际回国时间不得早于出国时间。"))}else o()}),100)};return{rules:{toCountry:[{required:!0,message:"请输入所去国家",trigger:"blur"},{max:100,message:"所去国家不允许超过100个字符",trigger:"blur"}],toInstitute:[{max:100,message:"所去机构不允许超过100个字符",trigger:"blur"}],startDate:[{validator:t,trigger:"blur"}],endDate:[{validator:a,trigger:"blur"}],typeId:[{required:!0,message:"请选择出国类别",trigger:"blur"}],reason:[{max:200,message:"出国原因不允许超过200个字符",trigger:"blur"}],dispatchInstitute:[{max:200,message:"派出机构不允许超过200个字符",trigger:"blur"}],outlay:[{required:!1,type:"number",min:0,max:99999999,message:"出国经费必须在0到99999999之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],financialSource:[{max:100,message:"经费来源不允许超过100个字符",trigger:"blur"}],instance:[{max:200,message:"出国情况不允许超过200个字符",trigger:"blur"}]},total:1,listLoading:!1,tempData:{tempFormModel:{}},startPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.endDate){var a=new Date(e.tempData.tempFormModel.endDate);return t.getTime()>a}}},endPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.startDate){var a=new Date(e.tempData.tempFormModel.startDate);return t.getTime()<a}}},datePickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()-864e4}},pageAbroadList:[],listQuery:{total:3,pageIndex:1,pageSize:5},typeOptions:[]}},created:function(){this.loadType(),this.getAbroadList()},methods:{loadType:function(){var e=this;l["a"].queryAbroadType().then((function(t){e.typeOptions=t.data.datas})).catch((function(e){console.log(e)}))},getAbroadList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeAbroad(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageAbroadList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getAbroadList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeAbroad(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getAbroadList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeAbroad(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getAbroadList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={}},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteAbroad:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeAbroad(e).then((function(e){e.succeed?(t.getAbroadList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("3fe5"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"467bde31",null);t["a"]=c.exports},3238:function(e,t,a){"use strict";var o=a("31ce"),r=a.n(o);r.a},"34cc":function(e,t,a){"use strict";var o=a("49fd"),r=a.n(o);r.a},"3acc":function(e,t,a){"use strict";var o=a("4fde"),r=a.n(o);r.a},"3b29":function(e,t,a){"use strict";var o=a("5cbe"),r=a.n(o);r.a},"3f5e":function(e,t,a){"use strict";a("4160"),a("b64b"),a("159b");var o=a("cfe3"),r="file",l=new o["a"](r);t["a"]={upload:function(e,t){var a=new FormData;return t&&Object.keys(t).forEach((function(e){return a.append(e,t[e])})),a.append("file",e),l.postForm("upload",a)},download:function(e){return l.get("download/".concat(e))}}},"3fe5":function(e,t,a){"use strict";var o=a("40bc"),r=a.n(o);r.a},"40bc":function(e,t,a){},"40ef":function(e,t,a){"use strict";var o=a("4953"),r=a.n(o);r.a},4497:function(e,t,a){"use strict";var o=a("cf48"),r=a.n(o);r.a},"470e":function(e,t,a){"use strict";var o=a("7d4d"),r=a.n(o);r.a},4953:function(e,t,a){},"49fd":function(e,t,a){},"4c5f":function(e,t,a){"use strict";var o=a("60b3"),r=a.n(o);r.a},"4c96":function(e,t,a){},"4e5d":function(e,t,a){},"4fad":function(e,t,a){var o=a("23e7"),r=a("6f53").entries;o({target:"Object",stat:!0},{entries:function(e){return r(e)}})},"4fde":function(e,t,a){},"51fa":function(e,t,a){},"5cbe":function(e,t,a){},"5f06":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"auto"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"理论大课授课时数",prop:"hours"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入理论大课授课时数"},model:{value:e.tempData.tempFormModel.hours,callback:function(t){e.$set(e.tempData.tempFormModel,"hours",t)},expression:"tempData.tempFormModel.hours"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"理论大课授课年份",prop:"year"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入理论大课授课年份"},model:{value:e.tempData.tempFormModel.year,callback:function(t){e.$set(e.tempData.tempFormModel,"year",t)},expression:"tempData.tempFormModel.year"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"现任教研室职务",prop:"duties"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入现任教研室职务"},model:{value:e.tempData.tempFormModel.duties,callback:function(t){e.$set(e.tempData.tempFormModel,"duties",t)},expression:"tempData.tempFormModel.duties"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageTeachList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"理论大课授课年份","min-width":"80px",sortable:"custom",prop:"Year"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.year))])]}}])}),a("el-table-column",{attrs:{label:"理论大课授课时数","min-width":"100px",sortable:"custom",prop:"Hours"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.hours))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteTeach(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getTeachList}})],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{rules:{hours:[{required:!0,message:"请输入理论大课授课时数",trigger:"blur"},{required:!0,type:"number",min:1,max:1e3,message:"理论大课授课时数必须在1到1000之间",transform:function(e){return e?Number(e):null}},{pattern:/^[1-9][0-9]*$/,message:"仅支持整数"}],year:[{required:!0,message:"请输入理论大课授课年份",trigger:"blur"},{min:4,max:4,message:"理论大课授课年份只允许输入4个字符",trigger:"blur"},{pattern:/^[1-9][0-9]*$/,message:"仅支持4位年份数字"}],duties:[{required:!0,message:"请输入现任教研室职务",trigger:"blur"},{max:10,message:"现任教研室职务不允许超过10个字符",trigger:"blur"}]},listLoading:!1,tempData:{tempFormModel:{revoked:!1}},pageTeachList:[],listQuery:{total:3,pageIndex:1,pageSize:5}}},created:function(){this.getTeachList()},methods:{getTeachList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeTeach(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageTeachList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getTeachList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.hours&&(e.tempData.tempFormModel.hours=parseInt(e.tempData.tempFormModel.hours,10)),e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeTeach(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getTeachList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeTeach(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getTeachList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={}},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteTeach:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeTeach(e).then((function(e){e.succeed?(t.getTeachList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("b00f"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"661de5bd",null);t["a"]=c.exports},"60b3":function(e,t,a){},"63fc":function(e,t,a){"use strict";var o=a("12b0"),r=a.n(o);r.a},"67de":function(e,t,a){},"6aee":function(e,t,a){"use strict";var o=a("51fa"),r=a.n(o);r.a},"6d4b":function(e,t,a){},"6f53":function(e,t,a){var o=a("83ab"),r=a("df75"),l=a("fc6a"),i=a("d1e7").f,n=function(e){return function(t){var a,n=l(t),s=r(n),c=s.length,p=0,m=[];while(c>p)a=s[p++],o&&!i.call(n,a)||m.push(e?[a,n[a]]:n[a]);return m}};e.exports={entries:n(!0),values:n(!1)}},"73f2":function(e,t,a){"use strict";var o=a("4c96"),r=a.n(o);r.a},"74ca":function(e,t,a){"use strict";var o=a("ac3c"),r=a.n(o);r.a},"7d4d":function(e,t,a){},8084:function(e,t,a){},"8b28":function(e,t,a){"use strict";var o=a("8ba4"),r=a.n(o);r.a},"8ba4":function(e,t,a){},"8fc6":function(e,t,a){"use strict";var o=a("f82b"),r=a.n(o);r.a},"95bc":function(e,t,a){"use strict";var o=a("b931"),r=a.n(o);r.a},"9c64":function(e,t,a){"use strict";var o=a("67de"),r=a.n(o);r.a},a282:function(e,t,a){},a4b1:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"奖惩名称",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入奖惩名称"},model:{value:e.tempData.tempFormModel.name,callback:function(t){e.$set(e.tempData.tempFormModel,"name",t)},expression:"tempData.tempFormModel.name"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"奖惩类别",prop:"typeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择奖惩类别"},model:{value:e.tempData.tempFormModel.typeId,callback:function(t){e.$set(e.tempData.tempFormModel,"typeId",t)},expression:"tempData.tempFormModel.typeId"}},e._l(e.typeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"奖惩级别",prop:"level"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入奖惩级别"},model:{value:e.tempData.tempFormModel.level,callback:function(t){e.$set(e.tempData.tempFormModel,"level",t)},expression:"tempData.tempFormModel.level"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"奖惩原因",prop:"reason"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入奖惩原因"},model:{value:e.tempData.tempFormModel.reason,callback:function(t){e.$set(e.tempData.tempFormModel,"reason",t)},expression:"tempData.tempFormModel.reason"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"奖惩日期",prop:"date"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择奖惩日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.date,callback:function(t){e.$set(e.tempData.tempFormModel,"date",t)},expression:"tempData.tempFormModel.date"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"奖励/处罚金额",prop:"amount"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入奖励/处罚金额",max:999999999,min:0,precision:2,controls:!1},model:{value:e.tempData.tempFormModel.amount,callback:function(t){e.$set(e.tempData.tempFormModel,"amount",t)},expression:"tempData.tempFormModel.amount"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否撤消",prop:"revoked"}},[a("el-checkbox",{attrs:{placeholder:"请选择是否撤消"},model:{value:e.tempData.tempFormModel.revoked,callback:function(t){e.$set(e.tempData.tempFormModel,"revoked",t)},expression:"tempData.tempFormModel.revoked"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"上报者/信息来源",prop:"submitter"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入上报者/信息来源"},model:{value:e.tempData.tempFormModel.submitter,callback:function(t){e.$set(e.tempData.tempFormModel,"submitter",t)},expression:"tempData.tempFormModel.submitter"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"授奖/处分单位",prop:"approver"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入授奖/处分单位"},model:{value:e.tempData.tempFormModel.approver,callback:function(t){e.$set(e.tempData.tempFormModel,"approver",t)},expression:"tempData.tempFormModel.approver"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"处理结果/其他备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入处理结果/其他备注"},model:{value:e.tempData.tempFormModel.remark,callback:function(t){e.$set(e.tempData.tempFormModel,"remark",t)},expression:"tempData.tempFormModel.remark"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageIncentiveList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"奖惩类别","min-width":"100px",sortable:"custom",prop:"Type.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.typeName))])]}}])}),a("el-table-column",{attrs:{label:"奖惩级别","min-width":"80px",sortable:"custom",prop:"Level"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.level))])]}}])}),a("el-table-column",{attrs:{label:"奖惩名称","min-width":"80px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"奖惩日期","min-width":"80px",sortable:"custom",prop:"Date"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteIncentive(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getIncentiveList}})],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{rules:{name:[{required:!0,message:"请输入奖惩名称",trigger:"blur"},{max:200,message:"奖惩名称不允许超过200个字符",trigger:"blur"}],typeId:[{required:!0,message:"请选择奖惩类别",trigger:"change"}],reason:[{max:200,message:"奖惩原因不允许超过200个字符",trigger:"blur"}],amount:[{required:!1,type:"number",min:0,max:999999999,message:"奖励/处罚金额必须在0到999999999之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],submitter:[{max:50,message:"上报者/信息来源不允许超过50个字符",trigger:"blur"}],approver:[{max:50,message:"授奖/处分单位不允许超过50个字符",trigger:"blur"}],remark:[{max:500,message:"处理结果/其他备注不允许超过500个字符",trigger:"blur"}]},listLoading:!1,tempData:{tempFormModel:{revoked:!1}},pageIncentiveList:[],listQuery:{total:3,pageIndex:1,pageSize:5},typeOptions:[]}},created:function(){this.loadType(),this.getIncentiveList()},methods:{loadType:function(){var e=this;l["a"].queryIncentType().then((function(t){e.typeOptions=t.data.datas})).catch((function(e){console.log(e)}))},getIncentiveList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeIncentive(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageIncentiveList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getIncentiveList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeIncentive(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getIncentiveList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeIncentive(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getIncentiveList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel.revoked=!1},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteIncentive:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeIncentive(e).then((function(e){e.succeed?(t.getIncentiveList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("8b28"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"0682d08b",null);t["a"]=c.exports},a8a1:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"培养计划名称",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入培养计划名称"},model:{value:e.tempData.tempFormModel.name,callback:function(t){e.$set(e.tempData.tempFormModel,"name",t)},expression:"tempData.tempFormModel.name"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"培养计划课题",prop:"subject"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入培养计划课题"},model:{value:e.tempData.tempFormModel.subject,callback:function(t){e.$set(e.tempData.tempFormModel,"subject",t)},expression:"tempData.tempFormModel.subject"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"培养计划级别",prop:"levelId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择培养计划级别"},model:{value:e.tempData.tempFormModel.levelId,callback:function(t){e.$set(e.tempData.tempFormModel,"levelId",t)},expression:"tempData.tempFormModel.levelId"}},e._l(e.levelOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"培养计划开始日期",prop:"startDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择培养计划开始日期","value-format":"yyyy-MM-dd","picker-options":e.startPickerDisabled},model:{value:e.tempData.tempFormModel.startDate,callback:function(t){e.$set(e.tempData.tempFormModel,"startDate",t)},expression:"tempData.tempFormModel.startDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"培养计划结束日期",prop:"endDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择培养计划结束日期","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormModel.endDate,callback:function(t){e.$set(e.tempData.tempFormModel,"endDate",t)},expression:"tempData.tempFormModel.endDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"考核成绩",prop:"result"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入考核成绩"},model:{value:e.tempData.tempFormModel.result,callback:function(t){e.$set(e.tempData.tempFormModel,"result",t)},expression:"tempData.tempFormModel.result"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"培养计划机构",prop:"institute"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入培养计划机构"},model:{value:e.tempData.tempFormModel.institute,callback:function(t){e.$set(e.tempData.tempFormModel,"institute",t)},expression:"tempData.tempFormModel.institute"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"培养经费",prop:"outlay"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入培养经费"},model:{value:e.tempData.tempFormModel.outlay,callback:function(t){e.$set(e.tempData.tempFormModel,"outlay",t)},expression:"tempData.tempFormModel.outlay"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"完成情况",prop:"completion"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入完成情况"},model:{value:e.tempData.tempFormModel.completion,callback:function(t){e.$set(e.tempData.tempFormModel,"completion",t)},expression:"tempData.tempFormModel.completion"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageTrainList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"培养计划名称","min-width":"100px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"培养计划级别","min-width":"80px",sortable:"custom",prop:"typeName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.typeName))])]}}])}),a("el-table-column",{attrs:{label:"培养计划机构","min-width":"80px",sortable:"custom",prop:"Institute"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.institute))])]}}])}),a("el-table-column",{attrs:{label:"培养计划开始日期","min-width":"80px",sortable:"custom",prop:"StartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.startDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"培养计划结束日期","min-width":"100px",sortable:"custom",prop:"EndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.endDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteTrain(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getTrainList}})],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.endDate){var t=new Date(e.tempData.tempFormModel.endDate),r=new Date(a);r<t?o():o(new Error("开始日期不得晚于结束日期。"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.startDate){var t=new Date(e.tempData.tempFormModel.startDate),r=new Date(a);r>t?o():o(new Error("结束日期不得早于开始日期。"))}else o()}),100)};return{rules:{name:[{required:!0,message:"请输入培养计划名称",trigger:"blur"},{max:100,message:"培养计划名称不允许超过100个字符",trigger:"blur"}],subject:[{required:!0,message:"请输入培养计划课题",trigger:"blur"},{max:200,message:"培养计划课题不允许超过200个字符",trigger:"blur"}],startDate:[{validator:t,trigger:"blur"}],endDate:[{validator:a,trigger:"blur"}],result:[{max:200,message:"考核成绩不允许超过200个字符",trigger:"blur"}],institute:[{max:100,message:"培养计划机构不允许超过100个字符",trigger:"blur"}],outlay:[{required:!1,type:"number",min:0,max:999999999,message:"培养经费必须在0到999999999之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],completion:[{max:200,message:"完成情况不允许超过200个字符",trigger:"blur"}]},total:1,listLoading:!1,tempData:{tempFormModel:{isTheLatest:!1}},startPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.endDate){var a=new Date(e.tempData.tempFormModel.endDate);return t.getTime()>a}}},endPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.startDate){var a=new Date(e.tempData.tempFormModel.startDate);return t.getTime()<a}}},pageTrainList:[],listQuery:{total:3,pageIndex:1,pageSize:5},levelOptions:[]}},created:function(){this.loadLevel(),this.getTrainList()},methods:{loadLevel:function(){var e=this;l["a"].queryTrainLevel().then((function(t){e.levelOptions=t.data.datas})).catch((function(e){console.log(e)}))},getTrainList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeTrain(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageTrainList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getTrainList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeTrain(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getTrainList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeTrain(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getTrainList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={}},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteTrain:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeTrain(e).then((function(e){e.succeed?(t.getTrainList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("3b29"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"f8bc0ad8",null);t["a"]=c.exports},ac3c:function(e,t,a){},aec3:function(e,t,a){},b00f:function(e,t,a){"use strict";var o=a("8084"),r=a.n(o);r.a},b8f4:function(e,t,a){"use strict";var o=a("4e5d"),r=a.n(o);r.a},b931:function(e,t,a){},b96e:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[e.userPermission.isShowBtnNew?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1):e._e(),e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"起始时间",prop:"startDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择起始时间","value-format":"yyyy-MM-dd","picker-options":e.startPickerDisabled},model:{value:e.tempData.tempFormModel.startDate,callback:function(t){e.$set(e.tempData.tempFormModel,"startDate",t)},expression:"tempData.tempFormModel.startDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"终止时间",prop:"endDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择终止时间","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormModel.endDate,callback:function(t){e.$set(e.tempData.tempFormModel,"endDate",t)},expression:"tempData.tempFormModel.endDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工作单位",prop:"company"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入工作单位"},model:{value:e.tempData.tempFormModel.company,callback:function(t){e.$set(e.tempData.tempFormModel,"company",t)},expression:"tempData.tempFormModel.company"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"部门",prop:"department"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入部门"},model:{value:e.tempData.tempFormModel.department,callback:function(t){e.$set(e.tempData.tempFormModel,"department",t)},expression:"tempData.tempFormModel.department"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职务",prop:"positionStation"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入职务"},model:{value:e.tempData.tempFormModel.positionStation,callback:function(t){e.$set(e.tempData.tempFormModel,"positionStation",t)},expression:"tempData.tempFormModel.positionStation"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageWorkList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"起始时间","min-width":"100px",sortable:"custom",prop:"StartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.startDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"终止时间","min-width":"80px",sortable:"custom",prop:"EndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.endDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"工作单位","min-width":"80px",sortable:"custom",prop:"Company"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.company))])]}}])}),a("el-table-column",{attrs:{label:"部门","min-width":"80px",sortable:"custom",prop:"Department"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.department))])]}}])}),a("el-table-column",{attrs:{label:"职务","min-width":"100px",sortable:"custom",prop:"PositionStation"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.positionStation))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteWork(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getWorkList}})],1)],1)],1)],1)},r=[],l=a("e44c"),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.endDate){var t=new Date(e.tempData.tempFormModel.endDate),r=new Date(a);r<t?o():o(new Error("起始时间不得晚于终止时间。"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.startDate){var t=new Date(e.tempData.tempFormModel.startDate),r=new Date(a);r>t?o():o(new Error("终止时间不得早于起始时间。"))}else o()}),100)};return{rules:{startDate:[{validator:t,trigger:"blur"}],endDate:[{validator:a,trigger:"blur"}],company:[{required:!0,message:"请输入工作单位",trigger:"blur"},{max:200,message:"工作单位不允许超过200个字符",trigger:"blur"}],department:[{max:50,message:"部门不允许超过50个字符",trigger:"blur"}],positionStation:[{max:100,message:"职务不允许超过100个字符",trigger:"blur"}]},total:1,listLoading:!1,tempData:{tempFormModel:{}},startPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.endDate){var a=new Date(e.tempData.tempFormModel.endDate);return t.getTime()>a}}},endPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.startDate){var a=new Date(e.tempData.tempFormModel.startDate);return t.getTime()<a}}},datePickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()-864e4}},pageWorkList:[],listQuery:{total:3,pageIndex:1,pageSize:5}}},created:function(){this.getWorkList()},methods:{downloadexceltemplate:function(){l["a"].downlodaImportExcelTemplate({type:"importempword"}).then((function(e){var t=a("19de"),o="EmployeeWorkTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file;l["a"].importExcel(a,{type:"importempword"}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a)}})).catch((function(e){}))},getWorkList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeWork(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageWorkList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getWorkList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeWork(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getWorkList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeWork(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getWorkList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={}},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteWork:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeWork(e).then((function(e){e.succeed?(t.getWorkList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("f6a1"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"4ede9d4e",null);t["a"]=c.exports},ba3d:function(e,t,a){},baa5:function(e,t,a){var o=a("23e7"),r=a("e58c");o({target:"Array",proto:!0,forced:r!==[].lastIndexOf},{lastIndexOf:r})},c2f3:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[e.userPermission.isShowBtnNew?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1):e._e(),e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"职称资格名称",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入职称资格名称"},model:{value:e.tempData.tempFormModel.name,callback:function(t){e.$set(e.tempData.tempFormModel,"name",t)},expression:"tempData.tempFormModel.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"获得日期",prop:"effectDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择获得日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.effectDate,callback:function(t){e.$set(e.tempData.tempFormModel,"effectDate",t)},expression:"tempData.tempFormModel.effectDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"职称资格级别",prop:"levelId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择职称资格级别"},model:{value:e.tempData.tempFormModel.levelId,callback:function(t){e.$set(e.tempData.tempFormModel,"levelId",t)},expression:"tempData.tempFormModel.levelId"}},e._l(e.levelOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"发文编号",prop:"documentNo"}},[a("el-input",{attrs:{placeholder:"请输入发文编号"},model:{value:e.tempData.tempFormModel.documentNo,callback:function(t){e.$set(e.tempData.tempFormModel,"documentNo",t)},expression:"tempData.tempFormModel.documentNo"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageCertifyList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"职称资格名称","min-width":"100px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"职称资格级别","min-width":"80px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.level))])]}}])}),a("el-table-column",{attrs:{label:"发文编号","min-width":"80px",sortable:"custom",prop:"DocumentNo"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.documentNo))])]}}])}),a("el-table-column",{attrs:{label:"获得日期","min-width":"80px",sortable:"custom",prop:"EffectDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.effectDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteCertify(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getCertifyList}})],1)],1)],1)],1)},r=[],l=a("e44c"),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{rules:{name:[{required:!0,message:"请输入职称资格名称",trigger:"blur"},{max:200,message:"职称资格名称不允许超过200个字符",trigger:"blur"}],levelId:[{required:!0,message:"请选择职称资格级别",trigger:"change"}],documentNo:[{max:50,message:"发文编号不允许超过50个字符",trigger:"blur"}]},total:1,listLoading:!1,tempData:{tempFormModel:{}},pageCertifyList:[],listQuery:{total:3,pageIndex:1,pageSize:5},levelOptions:[]}},created:function(){this.loadLevel(),this.getCertifyList()},methods:{downloadexceltemplate:function(){l["a"].downlodaImportExcelTemplate({type:"importempcertify"}).then((function(e){var t=a("19de"),o="EmployeeCertifyTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file;l["a"].importExcel(a,{type:"importempcertify"}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a)}})).catch((function(e){}))},loadLevel:function(){var e=this;l["a"].queryLevel().then((function(t){e.levelOptions=t.data.datas})).catch((function(e){console.log(e)}))},getCertifyList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeCertify(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageCertifyList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getCertifyList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeCertify(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getCertifyList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeCertify(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getCertifyList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={}},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteCertify:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeCertify(e).then((function(e){e.succeed?(t.getCertifyList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("b8f4"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"60f804e0",null);t["a"]=c.exports},c69e:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入姓名"},model:{value:e.tempData.tempFormModel.name,callback:function(t){e.$set(e.tempData.tempFormModel,"name",t)},expression:"tempData.tempFormModel.name"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"关系",prop:"relationType"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入关系"},model:{value:e.tempData.tempFormModel.relationType,callback:function(t){e.$set(e.tempData.tempFormModel,"relationType",t)},expression:"tempData.tempFormModel.relationType"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否紧急联系人",prop:"urgency"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择是否紧急联系人"},model:{value:e.tempData.tempFormModel.urgency,callback:function(t){e.$set(e.tempData.tempFormModel,"urgency",t)},expression:"tempData.tempFormModel.urgency"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单位名称",prop:"company"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单位名称"},model:{value:e.tempData.tempFormModel.company,callback:function(t){e.$set(e.tempData.tempFormModel,"company",t)},expression:"tempData.tempFormModel.company"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职务",prop:"position"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入职务"},model:{value:e.tempData.tempFormModel.position,callback:function(t){e.$set(e.tempData.tempFormModel,"position",t)},expression:"tempData.tempFormModel.position"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单位电话",prop:"companyPhone"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单位电话"},model:{value:e.tempData.tempFormModel.companyPhone,callback:function(t){e.$set(e.tempData.tempFormModel,"companyPhone",t)},expression:"tempData.tempFormModel.companyPhone"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"家庭电话",prop:"phone"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入家庭电话"},model:{value:e.tempData.tempFormModel.phone,callback:function(t){e.$set(e.tempData.tempFormModel,"phone",t)},expression:"tempData.tempFormModel.phone"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"手机",prop:"mobile"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入手机"},model:{value:e.tempData.tempFormModel.mobile,callback:function(t){e.$set(e.tempData.tempFormModel,"mobile",t)},expression:"tempData.tempFormModel.mobile"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"邮政编码",prop:"zip"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入邮政编码"},model:{value:e.tempData.tempFormModel.zip,callback:function(t){e.$set(e.tempData.tempFormModel,"zip",t)},expression:"tempData.tempFormModel.zip"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"地址一",prop:"address1"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入地址一"},model:{value:e.tempData.tempFormModel.address1,callback:function(t){e.$set(e.tempData.tempFormModel,"address1",t)},expression:"tempData.tempFormModel.address1"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"地址二",prop:"address2"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入地址二"},model:{value:e.tempData.tempFormModel.address2,callback:function(t){e.$set(e.tempData.tempFormModel,"address2",t)},expression:"tempData.tempFormModel.address2"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageRelationList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"姓名","min-width":"80px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"关系","min-width":"100px",sortable:"custom",prop:"RelationType"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.relationType))])]}}])}),a("el-table-column",{attrs:{label:"职务","min-width":"80px",sortable:"custom",prop:"Position"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.position))])]}}])}),a("el-table-column",{attrs:{label:"单位名称","min-width":"80px",sortable:"custom",prop:"Company"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.company))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteRelation(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getRelationList}})],1)],1)],1)],1)},r=[],l=a("e44c"),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{rules:{name:[{max:50,message:"姓名不允许超过50个字符",trigger:"blur"}],relationType:[{max:50,message:"关系不允许超过50个字符",trigger:"blur"}],company:[{max:50,message:"单位名称不允许超过50个字符",trigger:"blur"}],position:[{max:50,message:"职务不允许超过50个字符",trigger:"blur"}],companyPhone:[{max:50,message:"单位电话不允许超过50个字符",trigger:"blur"}],phone:[{max:50,message:"家庭电话不允许超过50个字符",trigger:"blur"}],mobile:[{max:50,message:"手机不允许超过50个字符",trigger:"blur"}],zip:[{max:50,message:"邮政编码不允许超过50个字符",trigger:"blur"}],address1:[{max:100,message:"地址一不允许超过100个字符",trigger:"blur"}],address2:[{max:100,message:"地址二不允许超过100个字符",trigger:"blur"}]},listLoading:!1,tempData:{tempFormModel:{urgency:!1}},pageRelationList:[],listQuery:{total:3,pageIndex:1,pageSize:5}}},created:function(){this.getRelationList()},methods:{getRelationList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeRelation(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageRelationList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getRelationList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeRelation(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getRelationList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeRelation(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getRelationList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields()},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteRelation:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeRelation(e).then((function(e){e.succeed?(t.getRelationList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("63fc"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"5dcd6035",null);t["a"]=c.exports},cbd9:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("论文信息")])]),a("el-form",{ref:"dataFormForArticle",attrs:{rules:e.rulesForArticle,model:e.tempData.tempFormForArticle,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addArticle}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.saveArticle}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"论文标题",prop:"title"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入论文标题"},model:{value:e.tempData.tempFormForArticle.title,callback:function(t){e.$set(e.tempData.tempFormForArticle,"title",t)},expression:"tempData.tempFormForArticle.title"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发表日期",prop:"publishDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择发表日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormForArticle.publishDate,callback:function(t){e.$set(e.tempData.tempFormForArticle,"publishDate",t)},expression:"tempData.tempFormForArticle.publishDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"发表刊物 ",prop:"publication"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入发表刊物"},model:{value:e.tempData.tempFormForArticle.publication,callback:function(t){e.$set(e.tempData.tempFormForArticle,"publication",t)},expression:"tempData.tempFormForArticle.publication"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"收录情况",prop:"incomeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择收录情况"},model:{value:e.tempData.tempFormForArticle.incomeId,callback:function(t){e.$set(e.tempData.tempFormForArticle,"incomeId",t)},expression:"tempData.tempFormForArticle.incomeId"}},e._l(e.incomeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"影响因子 ",prop:"impactFactors"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入影响因子"},model:{value:e.tempData.tempFormForArticle.impactFactors,callback:function(t){e.$set(e.tempData.tempFormForArticle,"impactFactors",e._n(t))},expression:"tempData.tempFormForArticle.impactFactors"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入备注"},model:{value:e.tempData.tempFormForArticle.remark,callback:function(t){e.$set(e.tempData.tempFormForArticle,"remark",t)},expression:"tempData.tempFormForArticle.remark"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoadingForArticle,expression:"listLoadingForArticle"}],staticStyle:{width:"100%"},attrs:{data:e.pageArticleList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortArticle,"row-click":e.rowShowArticleInfo}},[a("el-table-column",{attrs:{label:"论文标题","min-width":"100px",sortable:"custom",prop:"Title"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.title))])]}}])}),a("el-table-column",{attrs:{label:"收录情况","min-width":"80px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.incomeName))])]}}])}),a("el-table-column",{attrs:{label:"发表刊物","min-width":"80px",sortable:"custom",prop:"Publication"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.publication))])]}}])}),a("el-table-column",{attrs:{label:"发表日期","min-width":"80px",sortable:"custom",prop:"PublishDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.publishDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteArticle(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForArticle.total>0,expression:"listQueryForArticle.total > 0"}],attrs:{total:e.listQueryForArticle.total,"page-sizes":[5,10,20],page:e.listQueryForArticle.pageIndex,limit:e.listQueryForArticle.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForArticle,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForArticle,"pageSize",t)},pagination:e.getArticleList}})],1)],1)],1)],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("课题信息")])]),a("el-form",{ref:"dataFormForClass",attrs:{rules:e.rulesForClass,model:e.tempData.tempFormForClass,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addClass}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.saveClass}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"课题名称",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入课题名称"},model:{value:e.tempData.tempFormForClass.name,callback:function(t){e.$set(e.tempData.tempFormForClass,"name",t)},expression:"tempData.tempFormForClass.name"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"课题级别",prop:"levelId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择课题级别"},model:{value:e.tempData.tempFormForClass.levelId,callback:function(t){e.$set(e.tempData.tempFormForClass,"levelId",t)},expression:"tempData.tempFormForClass.levelId"}},e._l(e.classLevelOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"立项单位",prop:"org"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入立项单位"},model:{value:e.tempData.tempFormForClass.org,callback:function(t){e.$set(e.tempData.tempFormForClass,"org",t)},expression:"tempData.tempFormForClass.org"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"立项时间",prop:"date"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择立项时间","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormForClass.date,callback:function(t){e.$set(e.tempData.tempFormForClass,"date",t)},expression:"tempData.tempFormForClass.date"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"担任角色 ",prop:"role"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入担任角色"},model:{value:e.tempData.tempFormForClass.role,callback:function(t){e.$set(e.tempData.tempFormForClass,"role",t)},expression:"tempData.tempFormForClass.role"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"经费额度 ",prop:"amount"}},[a("el-input",{attrs:{type:"number",clearable:"",placeholder:"请输入经费额度"},model:{value:e.tempData.tempFormForClass.amount,callback:function(t){e.$set(e.tempData.tempFormForClass,"amount",e._n(t))},expression:"tempData.tempFormForClass.amount"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"认证时间",prop:"authenticationDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择认证时间","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormForClass.authenticationDate,callback:function(t){e.$set(e.tempData.tempFormForClass,"authenticationDate",t)},expression:"tempData.tempFormForClass.authenticationDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"认证结论 ",prop:"result"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入认证结论"},model:{value:e.tempData.tempFormForClass.result,callback:function(t){e.$set(e.tempData.tempFormForClass,"result",t)},expression:"tempData.tempFormForClass.result"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入备注"},model:{value:e.tempData.tempFormForClass.remark,callback:function(t){e.$set(e.tempData.tempFormForClass,"remark",t)},expression:"tempData.tempFormForClass.remark"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoadingForClass,expression:"listLoadingForClass"}],staticStyle:{width:"100%"},attrs:{data:e.pageClassList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortClass,"row-click":e.rowShowClassInfo}},[a("el-table-column",{attrs:{label:"课题名称","min-width":"100px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"课题级别","min-width":"80px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.classLevelName))])]}}])}),a("el-table-column",{attrs:{label:"立项单位","min-width":"80px",sortable:"custom",prop:"Org"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.org))])]}}])}),a("el-table-column",{attrs:{label:"经费额度","min-width":"80px",sortable:"custom",prop:"Amount"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.amount))])]}}])}),a("el-table-column",{attrs:{label:"立项时间","min-width":"80px",sortable:"custom",prop:"Date"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteClass(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForClass.total>0,expression:"listQueryForClass.total > 0"}],attrs:{total:e.listQueryForClass.total,"page-sizes":[5,10,20],page:e.listQueryForClass.pageIndex,limit:e.listQueryForClass.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForClass,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForClass,"pageSize",t)},pagination:e.getClassList}})],1)],1)],1)],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("专利信息")])]),a("el-form",{ref:"dataFormForPatent",attrs:{rules:e.rulesForPatent,model:e.tempData.tempFormForPatent,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addPatent}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.savePatent}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"专利名称",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入专利名称"},model:{value:e.tempData.tempFormForPatent.name,callback:function(t){e.$set(e.tempData.tempFormForPatent,"name",t)},expression:"tempData.tempFormForPatent.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"获专利日期",prop:"date"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择获专利日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormForPatent.date,callback:function(t){e.$set(e.tempData.tempFormForPatent,"date",t)},expression:"tempData.tempFormForPatent.date"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"专利号",prop:"number"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入专利号"},model:{value:e.tempData.tempFormForPatent.number,callback:function(t){e.$set(e.tempData.tempFormForPatent,"number",t)},expression:"tempData.tempFormForPatent.number"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"专利批准部门 ",prop:"org"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入专利批准部门"},model:{value:e.tempData.tempFormForPatent.org,callback:function(t){e.$set(e.tempData.tempFormForPatent,"org",t)},expression:"tempData.tempFormForPatent.org"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入备注"},model:{value:e.tempData.tempFormForPatent.remark,callback:function(t){e.$set(e.tempData.tempFormForPatent,"remark",t)},expression:"tempData.tempFormForPatent.remark"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoadingForPatent,expression:"listLoadingForPatent"}],staticStyle:{width:"100%"},attrs:{data:e.pagePatentList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortPatent,"row-click":e.rowShowPatentInfo}},[a("el-table-column",{attrs:{label:"专利名称","min-width":"100px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"专利号","min-width":"80px",sortable:"custom",prop:"Number"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.number))])]}}])}),a("el-table-column",{attrs:{label:"获专利日期","min-width":"80px",sortable:"custom",prop:"Date"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deletePatent(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForPatent.total>0,expression:"listQueryForPatent.total > 0"}],attrs:{total:e.listQueryForPatent.total,"page-sizes":[5,10,20],page:e.listQueryForPatent.pageIndex,limit:e.listQueryForPatent.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForPatent,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForPatent,"pageSize",t)},pagination:e.getPatentList}})],1)],1)],1)],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("导师信息")])]),a("el-form",{ref:"dataFormForTeacher",attrs:{rules:e.rulesForTeacher,model:e.tempData.tempFormForTeacher,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addTeacher}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.saveTeacher}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"博硕导",prop:"typeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择博硕导"},model:{value:e.tempData.tempFormForTeacher.typeId,callback:function(t){e.$set(e.tempData.tempFormForTeacher,"typeId",t)},expression:"tempData.tempFormForTeacher.typeId"}},e._l(e.typeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"批准年月",prop:"date"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择批准年月","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormForTeacher.date,callback:function(t){e.$set(e.tempData.tempFormForTeacher,"date",t)},expression:"tempData.tempFormForTeacher.date"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"批准机构",prop:"org"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入批准机构"},model:{value:e.tempData.tempFormForTeacher.org,callback:function(t){e.$set(e.tempData.tempFormForTeacher,"org",t)},expression:"tempData.tempFormForTeacher.org"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"带教起始日期",prop:"startDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择带教起始日期","value-format":"yyyy-MM-dd","picker-options":e.startPickerDisabled},model:{value:e.tempData.tempFormForTeacher.startDate,callback:function(t){e.$set(e.tempData.tempFormForTeacher,"startDate",t)},expression:"tempData.tempFormForTeacher.startDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"带教截至日期",prop:"endDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择带教截至日期","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormForTeacher.endDate,callback:function(t){e.$set(e.tempData.tempFormForTeacher,"endDate",t)},expression:"tempData.tempFormForTeacher.endDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入备注"},model:{value:e.tempData.tempFormForTeacher.remark,callback:function(t){e.$set(e.tempData.tempFormForTeacher,"remark",t)},expression:"tempData.tempFormForTeacher.remark"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoadingForTeacher,expression:"listLoadingForTeacher"}],staticStyle:{width:"100%"},attrs:{data:e.pageTeacherList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortTeacher,"row-click":e.rowShowTeacherInfo}},[a("el-table-column",{attrs:{label:"批准机构","min-width":"100px",sortable:"custom",prop:"Org"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.org))])]}}])}),a("el-table-column",{attrs:{label:"博硕导","min-width":"100px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.typeName))])]}}])}),a("el-table-column",{attrs:{label:"批准年月","min-width":"80px",sortable:"custom",prop:"Date"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dateFormat))])]}}])}),a("el-table-column",{attrs:{label:"带教起始日期","min-width":"80px",sortable:"custom",prop:"StartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.startDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"带教截至日期","min-width":"80px",sortable:"custom",prop:"EndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.endDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteTeacher(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForTeacher.total>0,expression:"listQueryForTeacher.total > 0"}],attrs:{total:e.listQueryForTeacher.total,"page-sizes":[5,10,20],page:e.listQueryForTeacher.pageIndex,limit:e.listQueryForTeacher.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForTeacher,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForTeacher,"pageSize",t)},pagination:e.getTeacherList}})],1)],1)],1)],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("获奖信息")])]),a("el-form",{ref:"dataFormForAward",attrs:{rules:e.rulesForAward,model:e.tempData.tempFormForAward,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addAward}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.saveAward}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"奖项名称",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入奖项名称"},model:{value:e.tempData.tempFormForAward.name,callback:function(t){e.$set(e.tempData.tempFormForAward,"name",t)},expression:"tempData.tempFormForAward.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"获奖时间",prop:"date"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择获奖时间","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormForAward.date,callback:function(t){e.$set(e.tempData.tempFormForAward,"date",t)},expression:"tempData.tempFormForAward.date"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"获奖级别",prop:"levelId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择获奖级别"},model:{value:e.tempData.tempFormForAward.levelId,callback:function(t){e.$set(e.tempData.tempFormForAward,"levelId",t)},expression:"tempData.tempFormForAward.levelId"}},e._l(e.awardLevelOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"授奖部门",prop:"org"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入授奖部门"},model:{value:e.tempData.tempFormForAward.org,callback:function(t){e.$set(e.tempData.tempFormForAward,"org",t)},expression:"tempData.tempFormForAward.org"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入备注"},model:{value:e.tempData.tempFormForAward.remark,callback:function(t){e.$set(e.tempData.tempFormForAward,"remark",t)},expression:"tempData.tempFormForAward.remark"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoadingForAward,expression:"listLoadingForAward"}],staticStyle:{width:"100%"},attrs:{data:e.pageAwardList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortAward,"row-click":e.rowShowAwardInfo}},[a("el-table-column",{attrs:{label:"奖项名称","min-width":"100px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"获奖时间","min-width":"80px",sortable:"custom",prop:"Date"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dateFormat))])]}}])}),a("el-table-column",{attrs:{label:"授奖部门","min-width":"80px",sortable:"custom",prop:"Org"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.org))])]}}])}),a("el-table-column",{attrs:{label:"获奖级别","min-width":"80px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.levelName))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteAward(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForAward.total>0,expression:"listQueryForAward.total > 0"}],attrs:{total:e.listQueryForAward.total,"page-sizes":[5,10,20],page:e.listQueryForAward.pageIndex,limit:e.listQueryForAward.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForAward,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForAward,"pageSize",t)},pagination:e.getAwardList}})],1)],1)],1)],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("高层次人才信息")])]),a("el-form",{ref:"dataFormForHighTalent",attrs:{rules:e.rulesForHighTalent,model:e.tempData.tempFormForHighTalent,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addHighTalent}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.saveHighTalent}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"高层次人才入选日期",prop:"inclusionDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择入选日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormForHighTalent.inclusionDate,callback:function(t){e.$set(e.tempData.tempFormForHighTalent,"inclusionDate",t)},expression:"tempData.tempFormForHighTalent.inclusionDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"高层次人才代码",prop:"highTalentId"}},[a("el-select",{attrs:{clearable:""},model:{value:e.tempData.tempFormForHighTalent.highTalentId,callback:function(t){e.$set(e.tempData.tempFormForHighTalent,"highTalentId",t)},expression:"tempData.tempFormForHighTalent.highTalentId"}},e._l(e.highTalentOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoadingForHighTalent,expression:"listLoadingForHighTalent"}],staticStyle:{width:"100%"},attrs:{data:e.pageHighTalentList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortHighTalent,"row-click":e.rowShowHighTalentInfo}},[a("el-table-column",{attrs:{label:"高层次人才入选日期","min-width":"100px",sortable:"custom",prop:"InclusionDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dateFormat))])]}}])}),a("el-table-column",{attrs:{label:"高层次人才代码","min-width":"80px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.highTalentName))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteHighTalent(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForHighTalent.total>0,expression:"listQueryForHighTalent.total > 0"}],attrs:{total:e.listQueryForHighTalent.total,"page-sizes":[5,10,20],page:e.listQueryForHighTalent.pageIndex,limit:e.listQueryForHighTalent.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForHighTalent,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForHighTalent,"pageSize",t)},pagination:e.getHighTalentList}})],1)],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormForTeacher.endDate){var t=new Date(e.tempData.tempFormForTeacher.endDate),r=new Date(a);r<t?o():o(new Error("带教起始日期不得晚于带教截至日期。"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormForTeacher.startDate){var t=new Date(e.tempData.tempFormForTeacher.startDate),r=new Date(a);r>t?o():o(new Error("带教截至日期不得早于带教起始日期。"))}else o()}),100)};return{rulesForArticle:{title:[{required:!0,message:"请输入论文标题",trigger:"blur"},{max:50,message:"论文标题不允许超过50个字符",trigger:"blur"}],publication:[{max:100,message:"发表刊物不允许超过100个字符",trigger:"blur"}],impactFactors:[{required:!1,type:"number",min:0,max:**************,message:"影响因子必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],remark:[{max:200,message:"备注不允许超过200个字符",trigger:"blur"}]},listLoadingForArticle:!1,pageArticleList:[],listQueryForArticle:{total:3,pageIndex:1,pageSize:5},incomeOptions:[],tempData:{tempFormForArticle:{},tempFormForClass:{},tempFormForPatent:{},tempFormForTeacher:{},tempFormForAward:{},tempFormForHighTalent:{}},rulesForClass:{name:[{required:!0,message:"请输入课题名称",trigger:"blur"},{max:200,message:"课题名称不允许超过200个字符",trigger:"blur"}],org:[{max:50,message:"立项单位不允许超过50个字符",trigger:"blur"}],role:[{max:50,message:"担任角色不允许超过50个字符",trigger:"blur"}],amount:[{required:!1,type:"number",min:0,max:**************,message:"经费额度必须在0到**************之间",transform:function(e){return e?Number(e):null},trigger:"blur"},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}]},listLoadingForClass:!1,pageClassList:[],classLevelOptions:[],listQueryForClass:{total:3,pageIndex:1,pageSize:5},rulesForPatent:{name:[{required:!0,message:"请输入专利名称",trigger:"blur"},{max:200,message:"专利名称不允许超过200个字符",trigger:"blur"}],number:[{required:!0,message:"请输入专利号",trigger:"blur"},{max:50,message:"专利号不允许超过50个字符",trigger:"blur"}],org:[{required:!0,message:"请输入专利批准部门",trigger:"blur"},{max:50,message:"专利批准部门不允许超过50个字符",trigger:"blur"}]},listLoadingForPatent:!1,pagePatentList:[],listQueryForPatent:{total:3,pageIndex:1,pageSize:5},rulesForTeacher:{org:[{required:!0,message:"请输入批准机构",trigger:"blur"},{max:50,message:"批准机构不允许超过50个字符",trigger:"blur"}],startDate:[{validator:t,trigger:"blur"}],endDate:[{validator:a,trigger:"blur"}]},startPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormForTeacher.endDate){var a=new Date(e.tempData.tempFormForTeacher.endDate);return t.getTime()>a}}},endPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormForTeacher.startDate){var a=new Date(e.tempData.tempFormForTeacher.startDate);return t.getTime()<a}}},listLoadingForTeacher:!1,pageTeacherList:[],typeOptions:[],listQueryForTeacher:{total:3,pageIndex:1,pageSize:5},rulesForAward:{name:[{required:!0,message:"请输入奖项名称",trigger:"blur"},{max:100,message:"奖项名称不允许超过100个字符",trigger:"blur"}],org:[{required:!0,message:"请输入授奖部门",trigger:"blur"},{max:50,message:"授奖部门不允许超过50个字符",trigger:"blur"}]},listLoadingForAward:!1,awardLevelOptions:[],pageAwardList:[],listQueryForAward:{total:3,pageIndex:1,pageSize:5},rulesForHighTalent:{},highTalentOptions:[],listLoadingForHighTalent:!1,pageHighTalentList:[],listQueryForHighTalent:{total:3,pageIndex:1,pageSize:5}}},created:function(){this.loadIncome(),this.loadClassLevel(),this.loadType(),this.loadAwardLevel(),this.loadHighTalent(),this.getArticleList(),this.getClassList(),this.getPatentList(),this.getTeacherList(),this.getAwardList(),this.getHighTalentList()},methods:{loadIncome:function(){var e=this;l["a"].queryIncomeType().then((function(t){e.incomeOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadClassLevel:function(){var e=this;l["a"].queryClassLevel().then((function(t){e.classLevelOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadType:function(){var e=this;l["a"].queryTeacherType().then((function(t){e.typeOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadAwardLevel:function(){var e=this;l["a"].queryAwardLevel().then((function(t){e.awardLevelOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadHighTalent:function(){var e=this;l["a"].queryHighTalent().then((function(t){e.highTalentOptions=t.data.datas})).catch((function(e){console.log(e)}))},uploadEmployee:function(e,t){var a={condition:e};l["a"].getPersonnelInformation(a).then((function(e){})).catch((function(e){console.log(e)}))},getArticleList:function(){var e=this;this.listLoadingForArticle=!0,this.listQueryForArticle.employeeId=this.empId,l["a"].queryEmployeeArticle(this.listQueryForArticle).then((function(t){e.listLoadingForArticle=!1,t.succeed?(e.pageArticleList=t.data.datas,e.listQueryForArticle.total=t.data.recordCount,e.listQueryForArticle.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoadingForArticle=!1}))},sortArticle:function(e,t,a){this.listQueryForArticle.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQueryForArticle.order=e.prop+" "+o,this.getArticleList()},addArticle:function(){this.clearArticle()},saveArticle:function(){var e=this;this.$refs["dataFormForArticle"].validate((function(t){t&&(e.tempData.tempFormForArticle.id?e.updateArticle():e.addNewArticle())}))},addNewArticle:function(){var e=this;this.tempData.tempFormForArticle.employeeId=this.empId,l["a"].addEmployeeArticle(this.tempData.tempFormForArticle).then((function(t){t.succeed?(e.tempData.tempFormForArticle.id=t.data.id,e.getArticleList(),e.clearArticle(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},updateArticle:function(){var e=this;l["a"].updateEmployeeArticle(this.tempData.tempFormForArticle).then((function(t){t.succeed?(e.getArticleList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clearArticle:function(){delete this.tempData.tempFormForArticle.id,this.$refs["dataFormForArticle"].resetFields(),this.tempData.tempFormForArticle={}},showArticleInfo:function(e){this.tempData.tempFormForArticle=JSON.parse(JSON.stringify(e))},rowShowArticleInfo:function(e,t,a){this.showArticleInfo(e)},deleteArticle:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeArticle(e).then((function(e){e.succeed?(t.getArticleList(),t.clearArticle(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoadingForArticle=!1,console.log(e)}))})).catch((function(e){t.listLoadingForArticle=!1,e.succeed||t.$notice.message("取消删除","info")}))},getClassList:function(){var e=this;this.listLoadingForClass=!0,this.listQueryForClass.employeeId=this.empId,l["a"].queryEmployeeClass(this.listQueryForClass).then((function(t){e.listLoadingForClass=!1,t.succeed?(e.pageClassList=t.data.datas,e.listQueryForClass.total=t.data.recordCount,e.listQueryForClass.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoadingForClass=!1}))},sortClass:function(e,t,a){this.listQueryForClass.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQueryForClass.order=e.prop+" "+o,this.getClassList()},addClass:function(){this.clearClass()},saveClass:function(){var e=this;this.$refs["dataFormForClass"].validate((function(t){t&&(e.tempData.tempFormForClass.id?e.updateClass():e.addNewClass())}))},addNewClass:function(){var e=this;this.tempData.tempFormForClass.employeeId=this.empId,l["a"].addEmployeeClass(this.tempData.tempFormForClass).then((function(t){t.succeed?(e.tempData.tempFormForClass.id=t.data.id,e.getClassList(),e.clearClass(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},updateClass:function(){var e=this;l["a"].updateEmployeeClass(this.tempData.tempFormForClass).then((function(t){t.succeed?(e.getClassList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clearClass:function(){delete this.tempData.tempFormForClass.id,this.$refs["dataFormForClass"].resetFields(),this.tempData.tempFormForClass={}},showClassInfo:function(e){this.tempData.tempFormForClass=JSON.parse(JSON.stringify(e))},rowShowClassInfo:function(e,t,a){this.showClassInfo(e)},deleteClass:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeClass(e).then((function(e){e.succeed?(t.getClassList(),t.clearClass(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoadingForClass=!1,console.log(e)}))})).catch((function(e){t.listLoadingForClass=!1,e.succeed||t.$notice.message("取消删除","info")}))},getPatentList:function(){var e=this;this.listLoadingForPatent=!0,this.listQueryForPatent.employeeId=this.empId,l["a"].queryEmployeePatent(this.listQueryForPatent).then((function(t){e.listLoadingForPatent=!1,t.succeed?(e.pagePatentList=t.data.datas,e.listQueryForPatent.total=t.data.recordCount,e.listQueryForPatent.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoadingForPatent=!1}))},sortPatent:function(e,t,a){this.listQueryForPatent.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQueryForPatent.order=e.prop+" "+o,this.getPatentList()},addPatent:function(){this.clearPatent()},savePatent:function(){var e=this;this.$refs["dataFormForPatent"].validate((function(t){t&&(e.tempData.tempFormForPatent.id?e.updatePatent():e.addNewPatent())}))},addNewPatent:function(){var e=this;this.tempData.tempFormForPatent.employeeId=this.empId,l["a"].addEmployeePatent(this.tempData.tempFormForPatent).then((function(t){t.succeed?(e.tempData.tempFormForPatent.id=t.data.id,e.getPatentList(),e.clearPatent(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},updatePatent:function(){var e=this;l["a"].updateEmployeePatent(this.tempData.tempFormForPatent).then((function(t){t.succeed?(e.getPatentList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clearPatent:function(){delete this.tempData.tempFormForPatent.id,this.$refs["dataFormForPatent"].resetFields(),this.tempData.tempFormForPatent={}},showPatentInfo:function(e){this.tempData.tempFormForPatent=JSON.parse(JSON.stringify(e))},rowShowPatentInfo:function(e,t,a){this.showPatentInfo(e)},deletePatent:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeePatent(e).then((function(e){e.succeed?(t.getPatentList(),t.clearPatent(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoadingForPatent=!1,console.log(e)}))})).catch((function(e){t.listLoadingForPatent=!1,e.succeed||t.$notice.message("取消删除","info")}))},getTeacherList:function(){var e=this;this.listLoadingForTeacher=!0,this.listQueryForTeacher.employeeId=this.empId,l["a"].queryEmployeeTeacher(this.listQueryForTeacher).then((function(t){e.listLoadingForTeacher=!1,t.succeed?(e.pageTeacherList=t.data.datas,e.listQueryForTeacher.total=t.data.recordCount,e.listQueryForTeacher.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoadingForTeacher=!1}))},sortTeacher:function(e,t,a){this.listQueryForTeacher.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQueryForTeacher.order=e.prop+" "+o,this.getTeacherList()},addTeacher:function(){this.clearTeacher()},saveTeacher:function(){var e=this;this.$refs["dataFormForTeacher"].validate((function(t){t&&(e.tempData.tempFormForTeacher.id?e.updateTeacher():e.addNewTeacher())}))},addNewTeacher:function(){var e=this;this.tempData.tempFormForTeacher.employeeId=this.empId,l["a"].addEmployeeTeacher(this.tempData.tempFormForTeacher).then((function(t){t.succeed?(e.tempData.tempFormForTeacher.id=t.data.id,e.getTeacherList(),e.clearTeacher(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},updateTeacher:function(){var e=this;l["a"].updateEmployeeTeacher(this.tempData.tempFormForTeacher).then((function(t){t.succeed?(e.getTeacherList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clearTeacher:function(){delete this.tempData.tempFormForTeacher.id,this.$refs["dataFormForTeacher"].resetFields(),this.tempData.tempFormForTeacher={}},showTeacherInfo:function(e){this.tempData.tempFormForTeacher=JSON.parse(JSON.stringify(e))},rowShowTeacherInfo:function(e,t,a){this.showTeacherInfo(e)},deleteTeacher:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeTeacher(e).then((function(e){e.succeed?(t.getTeacherList(),t.clearTeacher(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoadingForTeacher=!1,console.log(e)}))})).catch((function(e){t.listLoadingForTeacher=!1,e.succeed||t.$notice.message("取消删除","info")}))},getAwardList:function(){var e=this;this.listLoadingForAward=!0,this.listQueryForAward.employeeId=this.empId,l["a"].queryEmployeeAward(this.listQueryForAward).then((function(t){e.listLoadingForAward=!1,t.succeed?(e.listQueryForAward.total=t.data.recordCount,e.listQueryForAward.pageIndex=t.data.pageIndex,e.pageAwardList=t.data.datas):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoadingForAward=!1}))},sortAward:function(e,t,a){this.listQueryForAward.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQueryForAward.order=e.prop+" "+o,this.getAwardList()},addAward:function(){this.clearAward()},saveAward:function(){var e=this;this.$refs["dataFormForAward"].validate((function(t){t&&(e.tempData.tempFormForAward.id?e.updateAward():e.addNewAward())}))},addNewAward:function(){var e=this;this.tempData.tempFormForAward.employeeId=this.empId,l["a"].addEmployeeAward(this.tempData.tempFormForAward).then((function(t){t.succeed?(e.tempData.tempFormForAward.id=t.data.id,e.getAwardList(),e.clearAward(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},updateAward:function(){var e=this;l["a"].updateEmployeeAward(this.tempData.tempFormForAward).then((function(t){t.succeed?(e.getAwardList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clearAward:function(){delete this.tempData.tempFormForAward.id,this.$refs["dataFormForAward"].resetFields(),this.tempData.tempFormForAward={}},showAwardInfo:function(e){this.tempData.tempFormForAward=JSON.parse(JSON.stringify(e))},rowShowAwardInfo:function(e,t,a){this.showAwardInfo(e)},deleteAward:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeAward(e).then((function(e){e.succeed?(t.getAwardList(),t.clearAward(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoadingForAward=!1,console.log(e)}))})).catch((function(e){t.listLoadingForAward=!1,e.succeed||t.$notice.message("取消删除","info")}))},clear:function(){this.clearArticle(),this.clearClass(),this.clearPatent(),this.clearTeacher(),this.clearAward()},addHighTalent:function(){delete this.tempData.tempFormForHighTalent.id,this.$refs["dataFormForHighTalent"].resetFields(),this.tempData.tempFormForHighTalent={}},saveHighTalent:function(){var e=this;this.$refs["dataFormForHighTalent"].validate((function(t){t&&(e.tempData.tempFormForHighTalent.id?e.updateHighTalent():e.addNewHighTalent())}))},addNewHighTalent:function(){var e=this;this.tempData.tempFormForHighTalent.employeeId=this.empId,l["a"].addEmployeeHighTalent(this.tempData.tempFormForHighTalent).then((function(t){t.succeed&&(e.tempData.tempFormForHighTalent.id=t.data.id,e.getHighTalentList(),e.$notice.message("新增成功。","success"),e.uploadEmployee(t.data.uid,1))})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},updateHighTalent:function(){var e=this;l["a"].updateEmployeeHighTalent(this.tempData.tempFormForHighTalent).then((function(t){t.succeed?(e.getHighTalentList(),e.$notice.message("修改成功","success"),e.uploadEmployee(t.data.uid,2)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},getHighTalentList:function(){var e=this;this.listLoadingForHighTalent=!0,this.listQueryForHighTalent.employeeId=this.empId,l["a"].queryEmployeeHighTalent(this.listQueryForHighTalent).then((function(t){e.listLoadingForHighTalent=!1,t.succeed?(e.listQueryForHighTalent.total=t.data.recordCount,e.listQueryForHighTalent.pageIndex=t.data.pageIndex,e.pageHighTalentList=t.data.datas):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoadingForHighTalent=!1}))},deleteHighTalent:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeHighTalent(e).then((function(e){e.succeed?(t.getHighTalentList(),t.addHighTalent(),t.$$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoadingForHighTalent=!1,console.log(e)}))})).catch((function(e){t.listLoadingForHighTalent=!1,e.succeed||t.$notice.message("取消删除","info")}))},sortHighTalent:function(e,t,a){this.listQueryForHighTalent.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQueryForHighTalent.order=e.prop+" "+o,this.getHighTalentList()},rowShowHighTalentInfo:function(e){this.tempData.tempFormForHighTalent=JSON.parse(JSON.stringify(e))}}},n=i,s=(a("470e"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"5ecdb8cb",null);t["a"]=c.exports},cf48:function(e,t,a){},d336:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"医师用章",prop:"physicianSign"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入医师用章"},model:{value:e.tempData.tempFormModel.physicianSign,callback:function(t){e.$set(e.tempData.tempFormModel,"physicianSign",t)},expression:"tempData.tempFormModel.physicianSign"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"医疗事故名称",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入医疗事故名称"},model:{value:e.tempData.tempFormModel.name,callback:function(t){e.$set(e.tempData.tempFormModel,"name",t)},expression:"tempData.tempFormModel.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"发生时间",prop:"date"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择发生时间","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.date,callback:function(t){e.$set(e.tempData.tempFormModel,"date",t)},expression:"tempData.tempFormModel.date"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"医疗事故性质",prop:"typeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择医疗事故性质"},model:{value:e.tempData.tempFormModel.typeId,callback:function(t){e.$set(e.tempData.tempFormModel,"typeId",t)},expression:"tempData.tempFormModel.typeId"}},e._l(e.typeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"赔偿金额",prop:"compensation"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入赔偿金额"},model:{value:e.tempData.tempFormModel.compensation,callback:function(t){e.$set(e.tempData.tempFormModel,"compensation",e._n(t))},expression:"tempData.tempFormModel.compensation"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"医疗事故概况",prop:"situation"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入医疗事故概况"},model:{value:e.tempData.tempFormModel.situation,callback:function(t){e.$set(e.tempData.tempFormModel,"situation",t)},expression:"tempData.tempFormModel.situation"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"医疗事故处理结果",prop:"result"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入医疗事故处理结果"},model:{value:e.tempData.tempFormModel.result,callback:function(t){e.$set(e.tempData.tempFormModel,"result",t)},expression:"tempData.tempFormModel.result"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageAccidentList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"医疗事故名称","min-width":"80px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"医疗事故性质","min-width":"100px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.typeName))])]}}])}),a("el-table-column",{attrs:{label:"赔偿金额","min-width":"80px",sortable:"custom",prop:"Compensation"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.compensation))])]}}])}),a("el-table-column",{attrs:{label:"发生时间","min-width":"80px",sortable:"custom",prop:"Date"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.dateFormat))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteAccident(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getAccidentList}})],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{rules:{physicianSign:[{required:!0,message:"请输入医师用章",trigger:"blur"},{max:50,message:"医师用章不允许超过50个字符",trigger:"blur"}],name:[{required:!0,message:"请输入医疗事故名称",trigger:"blur"},{max:200,message:"医疗事故名称不允许超过200个字符",trigger:"blur"}],compensation:[{required:!1,type:"number",min:0,max:9999999999,message:"赔偿金额必须在0到9999999999之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}]},listLoading:!1,tempData:{tempFormModel:{}},pageAccidentList:[],listQuery:{total:3,pageIndex:1,pageSize:5},typeOptions:[]}},created:function(){this.loadType(),this.getAccidentList()},methods:{loadType:function(){var e=this;l["a"].queryAccidentType().then((function(t){e.typeOptions=t.data.datas})).catch((function(e){console.log(e)}))},getAccidentList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeAccident(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageAccidentList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getAccidentList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeAccident(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getAccidentList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeAccident(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getAccidentList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={}},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteAccident:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeAccident(e).then((function(e){e.succeed?(t.getAccidentList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("4497"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"21c546b5",null);t["a"]=c.exports},d368:function(e,t,a){"use strict";var o=a("cfe3"),r="Organization",l=new o["a"](r);t["a"]={QueryOrganizationHiddenTop:function(e){return l.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return l.get("QueryOrganization",e)},QueryDepartment:function(e){return l.get("QueryDepartment",e)},GetDepartment:function(e){return l.get("GetDepartment",e)},AddDepartment:function(e){return l.post("AddDepartment",e)},UpdateDepartment:function(e){return l.post("UpdateDepartment",e)},MoveDepartment:function(e){return l.post("MoveDepartment",e)},MergeDepartment:function(e){return l.post("MergeDepartment",e)},DeleteDepartment:function(e){return l.post("DeleteDepartment",e)},queryPosition:function(e){return l.post("QueryPosition",e)},getPosition:function(e){return l.get("GetPosition",e)},addPosition:function(e){return l.post("AddPosition",e)},updatePosition:function(e){return l.post("UpdatePosition",e)},deletePosition:function(e){return l.post("DeletePosition",e)},GetStation:function(e){return l.get("GetStation",e)},AddStation:function(e){return l.post("AddStation",e)},UpdateStation:function(e){return l.post("UpdateStation",e)},DeleteStation:function(e){return l.post("DeleteStation",e)},QueryPositionStationTree:function(e){return l.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return l.post("AllocatePosition",e)},DeletePositionStation:function(e){return l.post("DeletePositionStation",e)},queryDeptByUser:function(e){return l.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return l.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return l.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return l.get("QuerySenioritySelect")},queryStationAllowance:function(e){return l.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return l.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),l.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return l.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return l.get("GetStationAllowance",e)},addStationAllowance:function(e){return l.post("AddStationAllowance",e)},updateStationAllowance:function(e){return l.post("UpdateStationAllowance",e)},querySeniority:function(e){return l.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),l.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return l.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return l.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return l.get("GetSeniority",e)},addSeniority:function(e){return l.post("AddSeniority",e)},updateSeniority:function(e){return l.post("UpdateSeniority",e)},querySalaryScale:function(e){return l.get("QuerySalaryScale",e)},getSalaryScale:function(e){return l.get("GetSalaryScale",e)},addSalaryScale:function(e){return l.post("AddSalaryScale",e)},updateSalaryScale:function(e){return l.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return l.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),l.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return l.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return l.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return l.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return l.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return l.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return l.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return l.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return l.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return l.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return l.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return l.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return l.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return l.post("DeleteTelephoneFee",e)}}},d6e3:function(e,t,a){"use strict";var o=a("e6a9"),r=a.n(o);r.a},d8b5:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[e.userPermission.isShowBtnNew?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1):e._e(),e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"学校名称",prop:"schoolName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入学校名称"},model:{value:e.tempData.tempFormModel.schoolName,callback:function(t){e.$set(e.tempData.tempFormModel,"schoolName",t)},expression:"tempData.tempFormModel.schoolName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"系部",prop:"sdept"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入系部"},model:{value:e.tempData.tempFormModel.sdept,callback:function(t){e.$set(e.tempData.tempFormModel,"sdept",t)},expression:"tempData.tempFormModel.sdept"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"专业",prop:"specialty"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入专业"},model:{value:e.tempData.tempFormModel.specialty,callback:function(t){e.$set(e.tempData.tempFormModel,"specialty",t)},expression:"tempData.tempFormModel.specialty"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"入学日期",prop:"beginDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择入学日期","value-format":"yyyy-MM-dd","picker-options":e.startPickerDisabled},model:{value:e.tempData.tempFormModel.beginDate,callback:function(t){e.$set(e.tempData.tempFormModel,"beginDate",t)},expression:"tempData.tempFormModel.beginDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"截止日期",prop:"finishDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择截止日期","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormModel.finishDate,callback:function(t){e.$set(e.tempData.tempFormModel,"finishDate",t)},expression:"tempData.tempFormModel.finishDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"学制",prop:"schoolAge"}},[a("el-input",{attrs:{type:"number",clearable:"",placeholder:"请输入学制"},model:{value:e.tempData.tempFormModel.schoolAge,callback:function(t){e.$set(e.tempData.tempFormModel,"schoolAge",e._n(t))},expression:"tempData.tempFormModel.schoolAge"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"学历",prop:"eduLevelId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择学历"},model:{value:e.tempData.tempFormModel.eduLevelId,callback:function(t){e.$set(e.tempData.tempFormModel,"eduLevelId",t)},expression:"tempData.tempFormModel.eduLevelId"}},e._l(e.eduLevelOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否最高学历",prop:"isTopEduLevel"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择是否最高学历"},model:{value:e.tempData.tempFormModel.isTopEduLevel,callback:function(t){e.$set(e.tempData.tempFormModel,"isTopEduLevel",t)},expression:"tempData.tempFormModel.isTopEduLevel"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"毕业情况",prop:"graduationId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择毕业情况"},model:{value:e.tempData.tempFormModel.graduationId,callback:function(t){e.$set(e.tempData.tempFormModel,"graduationId",t)},expression:"tempData.tempFormModel.graduationId"}},e._l(e.graduationOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"学位",prop:"degreeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择学位"},model:{value:e.tempData.tempFormModel.degreeId,callback:function(t){e.$set(e.tempData.tempFormModel,"degreeId",t)},expression:"tempData.tempFormModel.degreeId"}},e._l(e.degreeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否最高学位",prop:"isTopDegree"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择是否最高学位"},model:{value:e.tempData.tempFormModel.isTopDegree,callback:function(t){e.$set(e.tempData.tempFormModel,"isTopDegree",t)},expression:"tempData.tempFormModel.isTopDegree"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"学习形式",prop:"learnWayId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择学习形式"},model:{value:e.tempData.tempFormModel.learnWayId,callback:function(t){e.$set(e.tempData.tempFormModel,"learnWayId",t)},expression:"tempData.tempFormModel.learnWayId"}},e._l(e.learnWayOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageEducationList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"学校名称","min-width":"100px",sortable:"custom",prop:"SchoolName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.schoolName))])]}}])}),a("el-table-column",{attrs:{label:"系部","min-width":"80px",sortable:"custom",prop:"Sdept"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.sdept))])]}}])}),a("el-table-column",{attrs:{label:"专业","min-width":"80px",sortable:"custom",prop:"Specialty"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.specialty))])]}}])}),a("el-table-column",{attrs:{label:"学历","min-width":"80px",sortable:"custom",prop:"EduLevel.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.eduLevel))])]}}])}),a("el-table-column",{attrs:{label:"是否最高学历","min-width":"100px",sortable:"custom",prop:"IsTopEduLevel"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-checkbox",{attrs:{disabled:""},model:{value:o.isTopEduLevel,callback:function(t){e.$set(o,"isTopEduLevel",t)},expression:"row.isTopEduLevel"}})]}}])}),a("el-table-column",{attrs:{label:"学位","min-width":"80px",sortable:"custom",prop:"Degree.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.degree))])]}}])}),a("el-table-column",{attrs:{label:"是否最高学位","min-width":"100px",sortable:"custom",prop:"IsTopDegree"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-checkbox",{attrs:{disabled:""},model:{value:o.isTopDegree,callback:function(t){e.$set(o,"isTopDegree",t)},expression:"row.isTopDegree"}})]}}])}),a("el-table-column",{attrs:{label:"截止日期","min-width":"80px",sortable:"custom",prop:"FinishDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.finishDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"学习形式","min-width":"80px",sortable:"custom",prop:"LearnWay.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.learnWay))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteEducation(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getEducationList}})],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.finishDate){var t=new Date(e.tempData.tempFormModel.finishDate),r=new Date(a);r<t?o():o(new Error("入学日期不得晚于截止日期。"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.beginDate){var t=new Date(e.tempData.tempFormModel.beginDate),r=new Date(a);r>t?o():o(new Error("截止日期不得早于入学日期。"))}else o()}),100)};return{rules:{schoolName:[{required:!0,message:"请输入学校名称",trigger:"blur"},{max:100,message:"学校名称不允许超过100个字符",trigger:"blur"}],sdept:[{max:100,message:"系部不允许超过100个字符",trigger:"blur"}],specialty:[{max:100,message:"专业不允许超过100个字符",trigger:"blur"}],beginDate:[{validator:t,trigger:"blur"}],finishDate:[{validator:a,trigger:"blur"}],schoolAge:[{required:!1,type:"number",min:0,max:99,message:"学制必须在0到99之间",transform:function(e){return e?Number(e):null}},{pattern:/^[1-9][0-9]*$/,message:"仅支持整数"}]},total:1,listLoading:!1,tempData:{tempFormModel:{isTopDegree:!0,isTopEduLevel:!0}},startPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.finishDate){var a=new Date(e.tempData.tempFormModel.finishDate);return t.getTime()>a}}},endPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.beginDate){var a=new Date(e.tempData.tempFormModel.beginDate);return t.getTime()<a}}},datePickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()-864e4}},pageEducationList:[],listQuery:{total:3,pageIndex:1,pageSize:5},levelOptions:[],eduLevelOptions:[],graduationOptions:[],degreeOptions:[],learnWayOptions:[]}},created:function(){this.loadLevel(),this.loadEduLevel(),this.loadGraduation(),this.loadDegree(),this.loadLearnWay(),this.getEducationList()},methods:{downloadexceltemplate:function(){l["a"].downlodaImportExcelTemplate({type:"importempeducation"}).then((function(e){var t=a("19de"),o="EmployeeEducationTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file;l["a"].importExcel(a,{type:"importempeducation"}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a)}})).catch((function(e){}))},loadLevel:function(){var e=this;l["a"].queryLevel().then((function(t){e.levelOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadEduLevel:function(){var e=this;l["a"].queryEducation().then((function(t){e.eduLevelOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadGraduation:function(){var e=this;l["a"].queryGraduation().then((function(t){e.graduationOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadDegree:function(){var e=this;l["a"].queryDegrees().then((function(t){e.degreeOptions=t.data.datas})).catch((function(e){console.log(e)}))},loadLearnWay:function(){var e=this;l["a"].queryLearnWay().then((function(t){e.learnWayOptions=t.data.datas})).catch((function(e){console.log(e)}))},getEducationList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeEducation(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageEducationList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getEducationList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.schoolAge&&(e.tempData.tempFormModel.schoolAge=parseInt(e.tempData.tempFormModel.schoolAge,10)),e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeEducation(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getEducationList(),e.clear(),e.$notice.message("新增成功","success"),e.uploadEmployee(t.data.uid)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeEducation(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getEducationList(),e.$notice.message("修改成功","success"),console.log(t.data),e.uploadEmployee(t.data.uid)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},uploadEmployee:function(e){var t={condition:e};l["a"].getPersonnelInformation(t).then((function(e){})).catch((function(e){console.log(e)}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={},this.tempData.tempFormModel.isTopEduLevel=!1,this.tempData.tempFormModel.isTopDegree=!1},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteEducation:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeEducation(e).then((function(e){e.succeed?(t.getEducationList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("73f2"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"477a70ea",null);t["a"]=c.exports},de23:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("工资信息")])]),a("el-form",{ref:"dataFormWages",attrs:{rules:e.wagesrules,model:e.tempData.tempFormWagesModel,"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e(),e.userPermission.isShowBtnClear?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document-delete"},on:{click:e.clear}},[e._v("清除")]):e._e()],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"工资分析"}},[a("div",[e._v(" 按正式职位: "),a("font",{attrs:{color:"black"}},[e._v("管理人员工资标准")]),a("br"),e._v("岗位工资 "),a("font",{attrs:{color:"black"}},[e._v("六级850.00")]),e._v(" 薪级工资 "),a("font",{attrs:{color:"black"}},[e._v("43 级-1199.00")]),e._v(" 职务工资 "),a("font",{attrs:{color:"black"}},[e._v("三级职员副处级八级1465.00")]),e._v(" 正式职位工资为 "),a("font",{attrs:{color:"black"}},[e._v("3514")]),a("br"),a("br"),e._v("按兼职职位: "),a("br"),a("font",{attrs:{color:"black"}},[e._v("专业技术人员工资标准(高级政工师)")]),a("br"),e._v("岗位工资 "),a("font",{attrs:{color:"black"}},[e._v("七级930.00")]),e._v(" 薪级工资 "),a("font",{attrs:{color:"black"}},[e._v("48 级-1434.00")]),e._v(" 职务工资 "),a("font",{attrs:{color:"black"}},[e._v("相当副教授八级1565.00")]),e._v(" 兼职职位工资为 "),a("font",{attrs:{color:"black"}},[e._v("3929")])],1)])],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工资单编号",prop:"paySlipNumber"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入工资单编号"},model:{value:e.tempData.tempFormWagesModel.paySlipNumber,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"paySlipNumber",t)},expression:"tempData.tempFormWagesModel.paySlipNumber"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工资标准类型",prop:"orgClassId"}},[a("el-select",{staticStyle:{width:"210px"},attrs:{clearable:"",placeholder:"请选择工资标准类型"},on:{change:e.orgClassIdChange},model:{value:e.tempData.tempFormWagesModel.orgClassId,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"orgClassId",t)},expression:"tempData.tempFormWagesModel.orgClassId"}},e._l(e.PayRollOrgClass,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工资组",prop:"compGroupId"}},[a("el-select",{staticStyle:{width:"210px"},attrs:{clearable:"",placeholder:"请选择工资组"},model:{value:e.tempData.tempFormWagesModel.compGroupId,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"compGroupId",t)},expression:"tempData.tempFormWagesModel.compGroupId"}},e._l(e.PayRollCompGroup,(function(e){return a("el-option",{key:e.id,attrs:{label:e.compGroupName,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工资用学龄",prop:"schoolAge"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.schoolAge,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"schoolAge",e._n(t))},expression:"tempData.tempFormWagesModel.schoolAge"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"实际工龄",prop:"societyAge"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.societyAge,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"societyAge",e._n(t))},expression:"tempData.tempFormWagesModel.societyAge"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"岗位名称",prop:"positionName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.tempData.tempFormWagesModel.positionName,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"positionName",t)},expression:"tempData.tempFormWagesModel.positionName"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"岗位工资级别",prop:"orgSalaryId"}},[a("el-select",{staticStyle:{width:"210px"},model:{value:e.tempData.tempFormWagesModel.orgSalaryId,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"orgSalaryId",t)},expression:"tempData.tempFormWagesModel.orgSalaryId"}},e._l(e.PayRollOrgSalary,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name+"-"+e.value,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"薪级工资级别",prop:"orgSalaryLevelId"}},[a("el-select",{staticStyle:{width:"210px"},model:{value:e.tempData.tempFormWagesModel.orgSalaryLevelId,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"orgSalaryLevelId",t)},expression:"tempData.tempFormWagesModel.orgSalaryLevelId"}},e._l(e.PayRollOrgSalaryLevel,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name+"-"+e.value,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"职务工资级别",prop:"orgPositionSalaryId"}},[a("el-select",{staticStyle:{width:"210px"},model:{value:e.tempData.tempFormWagesModel.orgPositionSalaryId,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"orgPositionSalaryId",t)},expression:"tempData.tempFormWagesModel.orgPositionSalaryId"}},e._l(e.PayRollOrgPositionSalarys,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name+"-"+e.value,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",[a("span",{staticStyle:{color:"red"}},[e._v("总计："+e._s(e.getCumulative||0))])])],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"各项津贴-绩效",prop:"performance"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.performance,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"performance",e._n(t))},expression:"tempData.tempFormWagesModel.performance"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"医保补贴",prop:"medicalSubsidy"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.medicalSubsidy,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"medicalSubsidy",e._n(t))},expression:"tempData.tempFormWagesModel.medicalSubsidy"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"区差",prop:"districtFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.districtFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"districtFee",e._n(t))},expression:"tempData.tempFormWagesModel.districtFee"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"房2%",prop:"houseFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.houseFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"houseFee",e._n(t))},expression:"tempData.tempFormWagesModel.houseFee"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"独子费",prop:"singleChildFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.singleChildFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"singleChildFee",e._n(t))},expression:"tempData.tempFormWagesModel.singleChildFee"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"托费",prop:"trusteeshipFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.trusteeshipFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"trusteeshipFee",e._n(t))},expression:"tempData.tempFormWagesModel.trusteeshipFee"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"会费",prop:"memberFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.memberFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"memberFee",e._n(t))},expression:"tempData.tempFormWagesModel.memberFee"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"车贴",prop:"carFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.carFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"carFee",e._n(t))},expression:"tempData.tempFormWagesModel.carFee"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"饭贴",prop:"riceFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.riceFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"riceFee",e._n(t))},expression:"tempData.tempFormWagesModel.riceFee"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"个人奖金",prop:"personalAward"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.personalAward,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"personalAward",e._n(t))},expression:"tempData.tempFormWagesModel.personalAward"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"书报费",prop:"bookFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.bookFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"bookFee",e._n(t))},expression:"tempData.tempFormWagesModel.bookFee"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"护龄工资",prop:"nurseAgeWages"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.nurseAgeWages,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"nurseAgeWages",e._n(t))},expression:"tempData.tempFormWagesModel.nurseAgeWages"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"其他基本工资",prop:"otherWages"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.otherWages,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"otherWages",e._n(t))},expression:"tempData.tempFormWagesModel.otherWages"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"物补",prop:"materialFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.materialFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"materialFee",e._n(t))},expression:"tempData.tempFormWagesModel.materialFee"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"出勤状态",prop:"workStateId"}},[a("el-select",{staticStyle:{width:"210px"},model:{value:e.tempData.tempFormWagesModel.workStateId,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"workStateId",t)},expression:"tempData.tempFormWagesModel.workStateId"}},e._l(e.workStateOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"电话费",prop:"mobileFee"}},[a("el-input",{model:{value:e.tempData.tempFormWagesModel.mobileFee,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"mobileFee",e._n(t))},expression:"tempData.tempFormWagesModel.mobileFee"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"允许工资及职务晋升批量处理",prop:"isAddSalary"}},[a("el-checkbox",{model:{value:e.tempData.tempFormWagesModel.isAddSalary,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"isAddSalary",t)},expression:"tempData.tempFormWagesModel.isAddSalary"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"停薪",prop:"isStopPay"}},[a("el-checkbox",{model:{value:e.tempData.tempFormWagesModel.isStopPay,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"isStopPay",t)},expression:"tempData.tempFormWagesModel.isStopPay"}}),a("label",{class:{salarySuspensionColor:e.checksalarySuspensionColor}},[e._v(e._s(e.salarySuspension))])],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"前两工资月是否长病假",prop:"isLongSickLeave"}},[a("el-checkbox",{model:{value:e.tempData.tempFormWagesModel.isLongSickLeave,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"isLongSickLeave",t)},expression:"tempData.tempFormWagesModel.isLongSickLeave"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"工资调整日期",prop:"changeDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择工资调整日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormWagesModel.changeDate,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"changeDate",t)},expression:"tempData.tempFormWagesModel.changeDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"停薪起始日期",prop:"stopPayDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择停薪起始日期","value-format":"yyyy-MM-dd"},on:{change:e.monthWithoutPay},model:{value:e.tempData.tempFormWagesModel.stopPayDate,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"stopPayDate",t)},expression:"tempData.tempFormWagesModel.stopPayDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入备注"},model:{value:e.tempData.tempFormWagesModel.remark,callback:function(t){e.$set(e.tempData.tempFormWagesModel,"remark",t)},expression:"tempData.tempFormWagesModel.remark"}})],1)],1)],1)],1)],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("工资调整历史")])]),a("el-form",{ref:"dataFormWagesHistory",attrs:{"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"显示过程记录"}},[a("el-checkbox",{on:{change:e.getWagesHistoryList},model:{value:e.IsViewAll,callback:function(t){e.IsViewAll=t},expression:"IsViewAll"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.WagesHistoryList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"过程标记","min-width":"60",sortable:"custom",prop:"tag"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[a("el-checkbox",{model:{value:o.tag,callback:function(t){e.$set(o,"tag",t)},expression:"row.tag"}})],1)]}}])}),a("el-table-column",{attrs:{label:"变动项目","min-width":"80",sortable:"custom",prop:"changeItem"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.changeItem))])]}}])}),a("el-table-column",{attrs:{label:"变动前值","min-width":"80",sortable:"custom",prop:"before"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.before))])]}}])}),a("el-table-column",{attrs:{label:"变动后值","min-width":"80",sortable:"custom",prop:"after"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.after))])]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"80",sortable:"custom",prop:"remark"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.remark))])]}}])}),a("el-table-column",{attrs:{label:"调整日期","min-width":"80",sortable:"custom",prop:"changeDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.changeDate?new Date(o.changeDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"修改时间","min-width":"80",sortable:"custom",prop:"lastEditTime"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.lastEditTime))])]}}])}),a("el-table-column",{attrs:{label:"修改人","min-width":"80",sortable:"custom",prop:"lastEditor"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.lastEditor))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-button",{staticClass:"el-icon-print",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.printReport(o)}}},[e._v(" 打印报表 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getWagesHistoryList}})],1)],1)],1)],1),e.dialogAppInfoVisible?a("el-dialog",{staticClass:"empManager",attrs:{"append-to-body":"",visible:e.dialogAppInfoVisible,width:"90%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogAppInfoVisible=t}}},[a("modelList",{ref:"modelList",attrs:{"print-template":e.printTemplate}})],1):e._e()],1)},r=[],l=(a("4de4"),a("a9e3"),a("e44c")),i=(a("cfa9"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-card",[a("el-form",{attrs:{"label-position":"right","label-width":"120px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"选择打印模板："}},[a("el-select",{model:{value:e.printTemplateType,callback:function(t){e.printTemplateType=t},expression:"printTemplateType"}},[a("el-option",{key:"1",attrs:{label:"职帖调整",value:"1"}}),a("el-option",{key:"2",attrs:{label:"岗资调整",value:"2"}}),a("el-option",{key:"3",attrs:{label:"薪级调整",value:"3"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"选择参数模板："}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.print}},[e._v("打印")])],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.employeePayrollPrintList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"总日期","min-width":"60",sortable:"custom",prop:"headDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.headDate))])]}}])}),a("el-table-column",{attrs:{label:"填表日期","min-width":"80",sortable:"custom",prop:"fillDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.fillDate?new Date(o.fillDate).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"日期一","min-width":"80",sortable:"custom",prop:"date1"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.date1?new Date(o.date1).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"日期二","min-width":"80",sortable:"custom",prop:"date2"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.date2?new Date(o.date2).Format("yyyy-MM-dd"):""))])]}}])}),a("el-table-column",{attrs:{label:"经办人","min-width":"80",sortable:"custom",prop:"operator"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.operator))])]}}])}),a("el-table-column",{attrs:{label:"基础部分一","min-width":"80",sortable:"custom",prop:"money1"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.money1))])]}}])}),a("el-table-column",{attrs:{label:"基础部分二","min-width":"80",sortable:"custom",prop:"money2"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.money2))])]}}])}),a("el-table-column",{attrs:{label:"部门意见","min-width":"80",sortable:"custom",prop:"advice"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.advice))])]}}])}),a("el-table-column",{attrs:{label:"执行日期","min-width":"80",sortable:"custom",prop:"effectDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.effectDate?new Date(o.effectDate).Format("yyyy-MM-dd"):""))])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getEmployeePayrollPrint}})],1)],1)],1),a("el-form",{attrs:{"label-position":"right","label-width":"120px",model:e.employeePayrollPrintModel,rules:e.rules}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.newly}},[e._v("新建")]),a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]),a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.deleted}},[e._v("删除")])],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"总日期",prop:"headDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"year","value-format":"yyyy"},model:{value:e.employeePayrollPrintModel.headDate,callback:function(t){e.$set(e.employeePayrollPrintModel,"headDate",t)},expression:"employeePayrollPrintModel.headDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"填表日期",prop:"fillDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.employeePayrollPrintModel.fillDate,callback:function(t){e.$set(e.employeePayrollPrintModel,"fillDate",t)},expression:"employeePayrollPrintModel.fillDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"日期一",prop:"date1"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.employeePayrollPrintModel.date1,callback:function(t){e.$set(e.employeePayrollPrintModel,"date1",t)},expression:"employeePayrollPrintModel.date1"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"日期二",prop:"date2"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.employeePayrollPrintModel.date2,callback:function(t){e.$set(e.employeePayrollPrintModel,"date2",t)},expression:"employeePayrollPrintModel.date2"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"经办人",prop:"operator"}},[a("el-input",{model:{value:e.employeePayrollPrintModel.operator,callback:function(t){e.$set(e.employeePayrollPrintModel,"operator",t)},expression:"employeePayrollPrintModel.operator"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"基础部分一",prop:"money1"}},[a("el-input",{model:{value:e.employeePayrollPrintModel.money1,callback:function(t){e.$set(e.employeePayrollPrintModel,"money1",e._n(t))},expression:"employeePayrollPrintModel.money1"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"基础部分二",prop:"money2"}},[a("el-input",{model:{value:e.employeePayrollPrintModel.money2,callback:function(t){e.$set(e.employeePayrollPrintModel,"money2",e._n(t))},expression:"employeePayrollPrintModel.money2"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"部门意见",prop:"advice"}},[a("el-input",{model:{value:e.employeePayrollPrintModel.advice,callback:function(t){e.$set(e.employeePayrollPrintModel,"advice",t)},expression:"employeePayrollPrintModel.advice"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"执行日期",prop:"effectDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.employeePayrollPrintModel.effectDate,callback:function(t){e.$set(e.employeePayrollPrintModel,"effectDate",t)},expression:"employeePayrollPrintModel.effectDate"}})],1)],1)],1)],1)],1),a("zhiwu",{ref:"childzhiwu"}),a("xinji",{ref:"childxinji"}),a("gangwei",{ref:"childgangwei"})],1)}),n=[],s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"none"}},[a("div",{attrs:{id:"zhiwuPrintContent",align:"center"}},[a("table",{staticStyle:{"border-collapse":"collapse","table-layout":"fixed",width:"413pt"},attrs:{cellpadding:"0",cellspacing:"0",width:"550"}},[a("col",{staticStyle:{width:"47pt"},attrs:{width:"62"}}),a("col",{staticStyle:{width:"75pt"},attrs:{width:"100"}}),a("col",{staticStyle:{width:"71pt"},attrs:{width:"95",span:"2"}}),a("col",{staticStyle:{width:"80pt"},attrs:{width:"106"}}),a("col",{staticStyle:{width:"69pt"},attrs:{width:"92"}}),a("tr",{staticStyle:{height:"18.75pt"},attrs:{height:"25"}},[a("td",{staticClass:"xl3626579",staticStyle:{height:"18.75pt"},attrs:{colspan:"6",height:"25"}},[a("label",{attrs:{id:"lbHeadDate"}}),a("span"),a("font",{staticClass:"font526579"},[e._v("年上海市事业单位工作人员")])],1)]),e._m(0),a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}},[e._m(1),a("td",{staticClass:"xl6326579",staticStyle:{height:"14pt"},attrs:{colspan:"5"}},[e._v(" 填表日期： "),a("label",{attrs:{id:"lbTdate"}},[e._v(e._s(e.rowEntity.fillDate?new Date(e.rowEntity.fillDate).Format("yyyy-MM-dd"):""))]),a("font",{staticClass:"font726579"},[a("span",{staticStyle:{"mso-spacerun":"yes"}})])],1)]),e._m(2),a("tr",{staticStyle:{"mso-height-source":"userset",height:"13.5pt"},attrs:{height:"18"}},[a("td",{staticClass:"xl2626579",staticStyle:{height:"41.25pt",width:"47pt"},attrs:{rowspan:"3",height:"55",width:"62"}},[e._v(" 姓名 ")]),a("td",{staticClass:"xl3426579",staticStyle:{"border-bottom":"0.5pt solid black",width:"75pt"},attrs:{rowspan:"3",width:"100","x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,3,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbName"}},[e._v(e._s(e.zhiwuEntity.name))])]),a("td",{staticClass:"xl2626579",staticStyle:{width:"71pt"},attrs:{rowspan:"3",width:"95"}},[e._v(" 性别 ")]),a("td",{staticClass:"xl2626579",staticStyle:{width:"71pt"},attrs:{rowspan:"3",width:"95","x:err":"#REF!"}},[a("label",{attrs:{id:"lbSex"}},[e._v(e._s(e.zhiwuEntity.enumGender))])]),a("td",{staticClass:"xl2626579",staticStyle:{width:"80pt"},attrs:{rowspan:"3",width:"106"}},[e._v(" 出生年月 ")]),a("td",{staticClass:"xl2726579",staticStyle:{width:"69pt"},attrs:{rowspan:"3",width:"92","x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,5,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbBirthday"}},[e._v(e._s(e.zhiwuEntity.birthday?new Date(e.zhiwuEntity.birthday).Format("yyyy-MM"):""))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"14.25pt"},attrs:{height:"19"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"13.5pt"},attrs:{height:"18"}}),e._m(3),e._m(4),e._m(5),a("tr",{staticStyle:{"mso-height-source":"userset",height:"15.75pt"},attrs:{height:"21"}},[e._m(6),a("td",{staticClass:"xl2726579",staticStyle:{"border-top":"none",width:"75pt"},attrs:{rowspan:"3",width:"100","x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,6,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbCan"}},[e._v(e._s(e.zhiwuEntity.societyDate?new Date(e.zhiwuEntity.societyDate).Format("yyyy-MM"):""))])]),e._m(7),a("td",{staticClass:"xl2726579",staticStyle:{"border-top":"none",width:"71pt"},attrs:{rowspan:"3",width:"95","x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,7,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbRenzhi"}},[e._v(e._s(e.zhiwuEntity.societyAge))])]),e._m(8),a("td",{staticClass:"xl2626579",staticStyle:{"border-top":"none",width:"69pt"},attrs:{rowspan:"3",width:"92","x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,19,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbXianRen"}},[e._v(e._s(e.zhiwuEntity.positionName))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"14.25pt"},attrs:{height:"19"}}),a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"41.25pt"},attrs:{height:"55"}},[e._m(9),a("td",{staticClass:"xl2626579",staticStyle:{"border-top":"none","border-left":"none",width:"75pt"},attrs:{width:"100"}},[e._v(" 基础部分 ")]),a("td",{staticClass:"xl2726579",staticStyle:{"border-top":"none","border-left":"none",width:"71pt"},attrs:{width:"95","x:num":""}},[a("label",{attrs:{id:"lbJichu1"}},[e._v(e._s(e.rowEntity.money1))])]),a("td",{staticClass:"xl3926579",staticStyle:{"border-top":"none",width:"71pt"},attrs:{rowspan:"3",width:"95"}},[e._v(" 调整后"),a("br"),e._v(" 金额"),a("font",{staticClass:"font726579"},[e._v("<")]),a("font",{staticClass:"font826579"},[e._v("元")]),a("font",{staticClass:"font726579"},[e._v(">")])],1),a("td",{staticClass:"xl2626579",staticStyle:{"border-top":"none","border-left":"none",width:"80pt"},attrs:{width:"106"}},[e._v(" 基础部分 ")]),a("td",{staticClass:"xl2726579",staticStyle:{"border-top":"none","border-left":"none",width:"69pt"},attrs:{width:"92","x:num":""}},[a("label",{attrs:{id:"lbJiChu2"}},[e._v(e._s(e.rowEntity.money2))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"38.25pt"},attrs:{height:"51"}},[a("td",{staticClass:"xl2626579",staticStyle:{height:"38.25pt","border-top":"none","border-left":"none",width:"75pt"},attrs:{height:"51",width:"100"}},[e._v(" 职务部分 ")]),a("td",{staticClass:"xl2726579",staticStyle:{"border-top":"none","border-left":"none",width:"71pt"},attrs:{width:"95","x:err":"#REF!","x:fmla":"=C18-C16"}},[a("label",{attrs:{id:"lbZhi1"}},[e._v(e._s(e.zhiwuEntity.beforeValue))])]),a("td",{staticClass:"xl2626579",staticStyle:{"border-top":"none","border-left":"none",width:"80pt"},attrs:{width:"106"}},[e._v(" 职务部分 ")]),a("td",{staticClass:"xl2726579",staticStyle:{"border-top":"none","border-left":"none",width:"69pt"},attrs:{width:"92","x:err":"#REF!","x:fmla":"=F18-F16"}},[a("label",{attrs:{id:"lbZhi2"}},[e._v(e._s(e.zhiwuEntity.afterValue))])])]),e._m(10),e._m(11),a("tr",{staticStyle:{"mso-height-source":"userset",height:"15.75pt"},attrs:{height:"21"}},[a("td",{staticClass:"xl5526579",staticStyle:{height:"15.75pt",width:"193pt"},attrs:{colspan:"3",height:"21",width:"257"}},[e._v(" 实际增资金额 ")]),a("td",{staticClass:"xl5626579",staticStyle:{"border-left":"none",width:"220pt"},attrs:{colspan:"3",width:"293","x:err":"#REF!","x:fmla":'=F18-C18&"  元"'}},[a("label",{attrs:{id:"lbCha"}},[e._v(e._s(e.increaseAmount)+"元")])])]),e._m(12),e._m(13),a("tr",{staticStyle:{"mso-height-source":"userset",height:"17.25pt"},attrs:{height:"23"}},[a("td",{staticClass:"xl4926579",staticStyle:{"border-right":"0.5pt solid black",height:"69pt",width:"366pt"},attrs:{colspan:"5",rowspan:"4",height:"92",width:"488"}},[a("span",{staticStyle:{"mso-spacerun":"yes"}},[e._v(" "),a("label",{attrs:{id:"lbadvice"}},[e._v(e._s(e.rowEntity.advice))])])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"15.75pt"},attrs:{height:"21"}}),a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21.75pt"},attrs:{height:"29"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"12pt"},attrs:{height:"16"}},[a("td",{staticClass:"xl5226579",staticStyle:{"border-right":"0.5pt solid black",height:"12pt","border-left":"none",width:"366pt","text-align":"right"},attrs:{colspan:"5",height:"16",width:"488"}},[a("span",{staticStyle:{"mso-spacerun":"yes"}}),a("font",{staticClass:"font826579"},[e._v("批准单位盖章:")]),a("font",{staticClass:"font726579"},[a("label",{attrs:{id:"lbDate1"}},[e._v(e._s(e.rowEntity.date1?new Date(e.rowEntity.date1).Format("yyyy-MM-dd"):""))])])],1)]),e._m(14),e._m(15),a("tr",{staticStyle:{"mso-height-source":"userset",height:"15.75pt"},attrs:{height:"21"}}),a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21.75pt"},attrs:{height:"29"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"12pt"},attrs:{height:"16"}},[a("td",{staticClass:"xl5226579",staticStyle:{"border-right":"0.5pt solid black",height:"12pt","border-left":"none",width:"366pt","text-align":"right"},attrs:{colspan:"5",height:"16",width:"488"}},[a("span",{staticStyle:{"mso-spacerun":"yes"}},[e._v(" 批准单位盖章: "),a("label",{attrs:{id:"lbDate2"}},[e._v(e._s(e.rowEntity.date2?new Date(e.rowEntity.date2).Format("yyyy-MM-dd"):""))])])])]),e._m(16),e._m(17),e._m(18),e._m(19),a("tr",{staticStyle:{height:"14.25pt","font-family":"宋体"},attrs:{height:"19"}},[a("td",{staticClass:"xl2326579",staticStyle:{height:"14.25pt","text-align":"left"},attrs:{colspan:"2",height:"19"}},[e._v(" 上海市人事局"),a("font",{staticClass:"font726579"},[a("span",{staticStyle:{"mso-spacerun":"yes"}})])],1),a("td",{staticClass:"xl2326579",staticStyle:{height:"14.25pt","text-align":"right"},attrs:{colspan:"6",height:"19"}},[a("font",{staticClass:"font726579"},[a("span",{staticStyle:{"mso-spacerun":"yes"}})]),a("font",{staticClass:"font826579"},[e._v(" 经办人： "),a("label",{attrs:{id:"lbHandle"}},[e._v(e._s(e.rowEntity.operator))])])],1)])])]),a("el-button",{directives:[{name:"print",rawName:"v-print",value:e.zhiwuprintObj,expression:"zhiwuprintObj"}],ref:"btnzhiwu"})],1)},c=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"18.75pt"},attrs:{height:"25"}},[a("td",{staticClass:"xl2226579",staticStyle:{height:"18.75pt"},attrs:{colspan:"6",height:"25"}},[e._v(" 调整职务（岗位）津贴标准审批表 ")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl2326579",staticStyle:{height:"14pt"},attrs:{"x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,20,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbdang"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"13.5pt"},attrs:{height:"18"}},[a("td",{staticClass:"xl2426579",staticStyle:{height:"13.5pt"},attrs:{height:"18","x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,1,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbGongzidan"}})]),a("td",{staticClass:"xl2426579",attrs:{"x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,2,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbEmpCode"}})]),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl2526579",attrs:{"x:num":""}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}},[a("td",{staticClass:"xl3426579",staticStyle:{height:"14.25pt",width:"122pt"},attrs:{colspan:"2",height:"19",width:"162"}}),a("td",{staticClass:"xl2626579",staticStyle:{width:"291pt"},attrs:{colspan:"4",rowspan:"3",width:"388"}},[e._v(" 上海交通大学医学院附属仁济医院 ")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"14.25pt"},attrs:{height:"19"}},[a("td",{staticClass:"xl5826579",staticStyle:{height:"14.25pt",width:"122pt"},attrs:{colspan:"2",height:"19",width:"162"}},[e._v(" 工作单位及部门 ")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}},[a("td",{staticClass:"xl3526579",staticStyle:{height:"14.25pt",width:"122pt"},attrs:{colspan:"2",height:"19",width:"162"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl5726579",staticStyle:{"border-bottom":"0.5pt solid black",height:"44.25pt","border-top":"none",width:"47pt"},attrs:{rowspan:"3",height:"59",width:"62"}},[e._v(" 参加工"),a("br"),e._v(" 作年月 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl5726579",staticStyle:{"border-bottom":"0.5pt solid black","border-top":"none",width:"71pt"},attrs:{rowspan:"3",width:"95"}},[e._v(" 任职"),a("br"),e._v(" 年限 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl5726579",staticStyle:{"border-bottom":"0.5pt solid black","border-top":"none",width:"80pt"},attrs:{rowspan:"3",width:"106"}},[e._v(" 现任"),a("br"),e._v(" 职务 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl6026579",staticStyle:{"border-bottom":"0.5pt solid black",height:"114.75pt","border-top":"none",width:"47pt"},attrs:{rowspan:"3",height:"153",width:"62"}},[e._v(" 调整前"),a("br"),e._v(" 金额<元> ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"35.25pt"},attrs:{height:"47"}},[a("td",{staticClass:"xl2626579",staticStyle:{height:"35.25pt","border-top":"none","border-left":"none",width:"75pt"},attrs:{height:"47",width:"100"}},[e._v(" 合计 ")]),a("td",{staticClass:"xl2726579",staticStyle:{"border-top":"none","border-left":"none",width:"71pt"},attrs:{width:"95","x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,12,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbHe1"}})]),a("td",{staticClass:"xl2626579",staticStyle:{"border-top":"none","border-left":"none",width:"80pt"},attrs:{width:"106"}},[e._v(" 合计 ")]),a("td",{staticClass:"xl2726579",staticStyle:{"border-top":"none","border-left":"none",width:"69pt"},attrs:{width:"92","x:err":"#REF!","x:fmla":'=INDIRECT( ADDRESS(F6,15,1,TRUE,"2005职贴调整060112") )'}},[a("label",{attrs:{id:"lbHe2"}})])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"15.75pt"},attrs:{height:"21"}},[a("td",{staticClass:"xl4526579",staticStyle:{height:"15.75pt",width:"193pt"},attrs:{colspan:"3",height:"21",width:"257"}}),a("td",{staticClass:"xl4526579",staticStyle:{"border-left":"none",width:"220pt"},attrs:{colspan:"3",width:"293","x:str":"                "}},[a("span",{staticStyle:{"mso-spacerun":"yes"}})])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}},[a("td",{staticClass:"xl4726579",staticStyle:{height:"14.25pt",width:"193pt"},attrs:{colspan:"3",height:"19",width:"257"}}),a("td",{staticClass:"xl4826579",staticStyle:{"border-left":"none",width:"220pt"},attrs:{colspan:"3",width:"293"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}},[a("td",{staticClass:"xl3926579",staticStyle:{height:"102.75pt","border-top":"none",width:"47pt"},attrs:{rowspan:"6",height:"137",width:"62",valign:"top"}},[e._v(" 单"),a("br"),e._v(" 位"),a("br"),e._v(" 或"),a("br"),e._v(" 部"),a("br"),e._v(" 门"),a("br"),e._v(" 意"),a("br"),e._v(" 见 ")]),a("td",{staticClass:"xl2826579",staticStyle:{"border-top":"none",width:"75pt"},attrs:{width:"100"}}),a("td",{staticClass:"xl2826579",staticStyle:{"border-top":"none",width:"71pt"},attrs:{width:"95"}}),a("td",{staticClass:"xl2826579",staticStyle:{"border-top":"none",width:"71pt"},attrs:{width:"95"}}),a("td",{staticClass:"xl2826579",staticStyle:{"border-top":"none",width:"80pt"},attrs:{width:"106"}}),a("td",{staticClass:"xl2926579",staticStyle:{"border-top":"none",width:"69pt"},attrs:{width:"92"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}},[a("td",{staticClass:"xl3926579",staticStyle:{height:"102.75pt","border-top":"none",width:"47pt"},attrs:{rowspan:"6",height:"137",width:"62",valign:"top"}},[e._v(" 单"),a("br"),e._v(" 位"),a("br"),e._v(" 或"),a("br"),e._v(" 部"),a("br"),e._v(" 门"),a("br"),e._v(" 意"),a("br"),e._v(" 见 ")]),a("td",{staticClass:"xl2826579",staticStyle:{"border-top":"black 1px solid",width:"75pt"},attrs:{width:"100"}}),a("td",{staticClass:"xl2826579",staticStyle:{"border-top":"black 1px solid",width:"75pt"},attrs:{width:"95"}}),a("td",{staticClass:"xl2826579",staticStyle:{"border-top":"black 1px solid",width:"75pt"},attrs:{width:"95"}}),a("td",{staticClass:"xl2826579",staticStyle:{"border-top":"black 1px solid",width:"75pt"},attrs:{width:"106"}}),a("td",{staticClass:"xl2926579",staticStyle:{"border-top":"black 1px solid",width:"75pt"},attrs:{width:"92"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"17.25pt"},attrs:{height:"23"}},[a("td",{staticClass:"xl4926579",staticStyle:{"border-right":"0.5pt solid black",height:"69pt",width:"366pt"},attrs:{colspan:"5",rowspan:"4",height:"92",width:"488"}},[a("span",{staticStyle:{"mso-spacerun":"yes"}},[e._v(" "),a("span",{staticStyle:{"font-size":"24pt","font-family":"华文宋体"}},[e._v(" 同意 ")]),a("br")])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"15.75pt"},attrs:{height:"21"}},[a("td",{staticClass:"xl3926579",staticStyle:{height:"66pt","border-top":"none",width:"47pt"},attrs:{rowspan:"4",height:"88",width:"62"}},[e._v(" 备"),a("br"),e._v(" 注 ")]),a("td",{staticClass:"xl4626579",staticStyle:{height:"15.75pt",width:"366pt","border-top":"black 1px solid","border-left-style":"none"},attrs:{colspan:"5",height:"21",width:"488"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}},[a("td",{staticClass:"xl4626579",staticStyle:{height:"14.25pt","border-left":"none",width:"366pt"},attrs:{colspan:"5",height:"19",width:"488"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"14.25pt"},attrs:{height:"19"}},[a("td",{staticClass:"xl4626579",staticStyle:{height:"14.25pt","border-left":"none",width:"366pt"},attrs:{colspan:"5",height:"19",width:"488"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"7.5pt"},attrs:{height:"10"}},[a("td",{staticClass:"xl4726579",staticStyle:{height:"7.5pt","border-left":"none",width:"366pt"},attrs:{colspan:"5",height:"10",width:"488"}})])}],p={data:function(){return{zhiwuprintObj:{id:"zhiwuPrintContent",popTitle:"",extraCss:"",extraHead:'<meta http-equiv="Content-Language" content="zh-cn"/>'},zhiwuEntity:{},rowEntity:{},increaseAmount:0}},methods:{Print:function(){this.rowEntity=this.$parent.employeePayrollPrintModel,this.zhiwuEntity=this.$parent.printInfoEntity,this.increaseAmount=parseFloat(this.zhiwuEntity.afterValue)+parseFloat(this.rowEntity.money2)-parseFloat(this.zhiwuEntity.beforeValue)-parseFloat(this.rowEntity.money1),this.$refs.btnzhiwu.$el.click()}}},m=p,d=(a("8fc6"),a("2877")),u=Object(d["a"])(m,s,c,!1,null,"3d10c6d6",null),h=u.exports,f=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"none"}},[a("div",{attrs:{id:"xinjiPrintContent",align:"center"}},[a("table",{staticStyle:{"border-collapse":"collapse","table-layout":"fixed",width:"413pt"},attrs:{"x:str":"x:str",border:"0",cellpadding:"0",cellspacing:"0",width:"550"}},[a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1056",width:"25pt"},attrs:{width:"33"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1024",width:"24pt"},attrs:{width:"32"}}),a("col",{staticStyle:{width:"54pt"},attrs:{width:"72"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1984",width:"47pt"},attrs:{width:"62"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1248",width:"29pt"},attrs:{width:"39"}}),a("col",{staticStyle:{width:"54pt"},attrs:{width:"72"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1056",width:"25pt"},attrs:{width:"33"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1120",width:"26pt"},attrs:{width:"35"}}),a("col",{staticStyle:{width:"54pt"},attrs:{width:"72"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1984",width:"47pt"},attrs:{width:"62"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1056",width:"25pt"},attrs:{width:"33"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1376",width:"54pt"},attrs:{width:"43"}}),e._m(0),a("tr",{staticStyle:{height:"21pt"},attrs:{height:"28"}},[a("td",{staticStyle:{"text-align":"right",height:"15pt"},attrs:{colspan:"12"}},[a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0",width:"550","x:str":""}},[a("tr",[e._m(1),a("td",{staticClass:"xl6326579",attrs:{colspan:"5"}},[e._v(" 填表日期： "),a("label",{attrs:{id:"lbTdate"}},[e._v(e._s(e.rowEntity.fillDate?new Date(e.rowEntity.fillDate).Format("yyyy-MM-dd"):""))]),a("font",{staticClass:"font726579"},[a("span",{staticStyle:{"mso-spacerun":"yes"}})])],1)]),e._m(2)])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{height:"26pt"},attrs:{colspan:"3"}},[e._v("姓 名")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"2","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!C2")'}},[a("label",{attrs:{id:"lbName"}},[e._v(e._s(e.xinjiEntity.name))])]),a("td",{staticClass:"xl24",staticStyle:{height:"26pt"}},[e._v("性别")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"2","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!E2")'}},[a("label",{attrs:{id:"lbSex"}},[e._v(e._s(e.xinjiEntity.enumGender))])]),a("td",{staticClass:"xl24",staticStyle:{height:"26pt"}},[e._v("出生年月")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"3","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!D2")'}},[a("label",{attrs:{id:"lbBirthday"}},[e._v(e._s(e.xinjiEntity.birthday?new Date(e.xinjiEntity.birthday).Format("yyyy-MM"):""))])])]),e._m(3),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{height:"21pt"},attrs:{colspan:"3",height:"28"}},[e._v(" 参加工作年月 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[a("label",{attrs:{id:"lbCan"}},[e._v(e._s(e.xinjiEntity.societyDate?new Date(e.xinjiEntity.societyDate).Format("yyyy-MM-dd"):""))])]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[e._v("聘任岗位")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[a("label",{attrs:{id:"lbXianRen"}},[e._v(e._s(e.xinjiEntity.positionName))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"20pt"},attrs:{height:"20"}},[e._m(4),a("td",{staticClass:"xl30",staticStyle:{"border-top":"none",width:"24pt"},attrs:{rowspan:"2",width:"32"}},[e._v(" 薪级工资 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"}},[e._v("薪级")]),a("td",{staticClass:"xl55",staticStyle:{"border-left":"none"},attrs:{colspan:"2"}},[a("label",{staticStyle:{"font-bold":"False"},attrs:{id:"lbXin1"}},[e._v(e._s(e.xinjiEntity.beforeKey))])]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"}},[e._v("级")]),a("td",{staticClass:"xl30",staticStyle:{"border-top":"none",width:"25pt"},attrs:{rowspan:"3"}},[e._v(" 增加后 ")]),e._m(5),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"}},[e._v("薪级")]),a("td",{staticClass:"xl55",staticStyle:{"border-left":"none"},attrs:{colspan:"2"}},[a("label",{staticStyle:{"font-bold":"false"},attrs:{id:"lbXin2"}},[e._v(e._s(e.xinjiEntity.afterKey))])]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none",width:"32pt"}},[e._v(" 级 ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"20pt"},attrs:{height:"20"}},[a("td",{staticClass:"xl24",staticStyle:{height:"20pt","border-top":"none","border-left":"none"},attrs:{height:"20"}},[e._v(" 金额 ")]),a("td",{staticClass:"xl55",staticStyle:{"border-left":"none"},attrs:{colspan:"2"}},[a("label",{staticStyle:{"font-bold":"false"},attrs:{id:"lbXinValue1"}},[e._v(e._s(e.xinjiEntity.beforeValue))])]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"}},[e._v("元")]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"}},[e._v("金额")]),a("td",{staticClass:"xl55",staticStyle:{"border-left":"none"},attrs:{colspan:"2"}},[a("label",{staticStyle:{"font-bold":"false"},attrs:{id:"lbXinValue2"}},[e._v(e._s(e.xinjiEntity.afterValue))])]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none",width:"32pt"}},[e._v(" 元 ")])]),e._m(6),a("tr",{staticStyle:{"mso-height-source":"userset",height:"20pt"},attrs:{height:"51"}},[a("td",{staticClass:"xl24",staticStyle:{"border-top-style":"none",height:"19px"},attrs:{colspan:"3"}},[e._v(" 实际增资额 ")]),a("td",{staticClass:"xl52",staticStyle:{width:"25pt","border-top-style":"none",height:"19px","text-align":"center"},attrs:{colspan:"9"}},[a("label",{staticStyle:{"Font-Bold":"false","Font-Size":"12pt"},attrs:{id:"lbCha"}},[e._v(e._s(e.increaseAmount)+"元")])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[e._m(7),a("td",{staticClass:"xl54",staticStyle:{"border-right":"0.5pt solid black"},attrs:{colspan:"11",rowspan:"5"}},[a("label",{attrs:{id:"lbadvice"}},[e._v(e._s(e.rowEntity.advice))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl40",staticStyle:{"border-right":"0.5pt solid black",height:"21pt","border-left":"none"},attrs:{colspan:"11",height:"28"}},[e._v(" 单位或部门盖章： "),a("label",{attrs:{id:"lbDate1"}},[e._v(e._s(e.rowEntity.date1?new Date(e.rowEntity.date1).Format("yyyy-MM-dd"):""))])])]),e._m(8),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl37",staticStyle:{"border-right":"0.5pt solid black","border-bottom":"0.5pt solid black",height:"42pt"},attrs:{colspan:"11",rowspan:"2",height:"56"}},[e._v(" 批准单位盖章： "),a("label",{attrs:{id:"lbDate2"}},[e._v(e._s(e.rowEntity.date2?new Date(e.rowEntity.date2).Format("yyyy-MM-dd"):""))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),e._m(9),e._m(10),e._m(11),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl29",staticStyle:{height:"21pt","text-align":"left"},attrs:{colspan:"3",height:"28"}},[e._v(" 上海市人事局制 ")]),a("td",{staticStyle:{"mso-ignore":"colspan"},attrs:{colspan:"6"}}),a("td",{staticClass:"xl28"},[e._v("经办人：")]),a("td",{staticClass:"xl29",staticStyle:{"text-align":"right"},attrs:{colspan:"2"}},[a("label",{attrs:{id:"lbHandle"}},[e._v(e._s(e.rowEntity.operator))])])])])]),a("el-button",{directives:[{name:"print",rawName:"v-print",value:e.xinjiprintObj,expression:"xinjiprintObj"}],ref:"btnxinji"})],1)},g=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"21pt"},attrs:{height:"28"}},[a("td",{staticStyle:{"text-align":"center",height:"21pt"},attrs:{colspan:"12"}},[a("strong",[a("br"),a("br"),a("label",{attrs:{id:"lbHeadDate"}}),a("span",{staticStyle:{"font-size":"16pt"}},[e._v("上海市事业单位工作人员正常增加薪级工资审批表")])])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl2326579",staticStyle:{height:"14.25pt","text-align":"left"},attrs:{height:"19"}},[a("label",{staticStyle:{"font-size":"10pt"},attrs:{id:"lbdang"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",[a("td",{staticClass:"xl2426579",staticStyle:{height:"13.5pt","text-align":"left"},attrs:{height:"18"}},[a("label",{staticStyle:{"font-size":"7pt"},attrs:{id:"lbGongzidan"}}),a("label",{staticStyle:{"font-size":"7pt"},attrs:{id:"lbEmpCode"}})]),a("td",{staticClass:"xl2426579",staticStyle:{"text-align":"left"}}),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl2526579",staticStyle:{width:"98px"},attrs:{"x:num":""}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{height:"21pt"},attrs:{colspan:"3",height:"28"}},[e._v(" 工作单位及部门 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"9"}},[e._v(" 上海交通大学医学院附属仁济医院 ")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl30",staticStyle:{height:"126pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"3",height:"138"}},[e._v(" 增"),a("br"),e._v(" 加"),a("br"),e._v(" 前 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl30",staticStyle:{"border-top":"none",width:"26pt"},attrs:{rowspan:"2",width:"35"}},[e._v(" 薪"),a("br"),e._v(" 级"),a("br"),e._v(" 工"),a("br"),e._v(" 资 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"20pt"},attrs:{height:"20"}},[a("td",{staticClass:"xl24",staticStyle:{"border-top-style":"none","border-left-style":"none","text-align":"center"},attrs:{colspan:"2"}},[e._v(" 保留工资 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"2"}}),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"}},[e._v("元")]),a("td",{staticClass:"xl24",staticStyle:{"border-left-style":"none"},attrs:{colspan:"2"}},[e._v(" 保留工资 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"2"}}),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 元 ")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl30",staticStyle:{height:"126pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"6",height:"168"}},[e._v(" 单"),a("br"),e._v(" 位"),a("br"),e._v(" 或"),a("br"),e._v(" 部"),a("br"),e._v(" 门"),a("br"),e._v(" 意"),a("br"),e._v(" 见"),a("br")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{height:"126pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"6",height:"168"}},[e._v(" 批"),a("br"),e._v(" 准"),a("br"),e._v(" 单"),a("br"),e._v(" 位"),a("br"),e._v(" 意"),a("br"),e._v(" 见"),a("br")]),a("td",{staticClass:"xl31",staticStyle:{"border-right":"0.5pt solid black"},attrs:{colspan:"11",rowspan:"4"}},[a("span",{staticStyle:{"font-size":"16pt","font-family":"新宋体"}},[e._v("同 意")])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{height:"80pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"3",height:"50"}},[e._v(" 备"),a("br"),e._v(" 注 ")]),a("td",{staticClass:"xl46",staticStyle:{"border-right":"0.5pt solid black",height:"21pt","border-left":"none"},attrs:{colspan:"11",height:"28"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl46",staticStyle:{"border-right":"0.5pt solid black",height:"21pt","border-left":"none"},attrs:{colspan:"11",height:"28"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl49",staticStyle:{"border-right":"0.5pt solid black",height:"21pt","border-left":"none"},attrs:{colspan:"11",height:"28"}})])}],y={data:function(){return{xinjiprintObj:{id:"xinjiPrintContent",popTitle:"",extraCss:"",extraHead:'<meta http-equiv="Content-Language" content="zh-cn"/>'},xinjiEntity:{},rowEntity:{},increaseAmount:0}},methods:{Print:function(){this.rowEntity=this.$parent.employeePayrollPrintModel,this.xinjiEntity=this.$parent.printInfoEntity,this.increaseAmount=parseFloat(this.xinjiEntity.afterValue)-parseFloat(this.xinjiEntity.beforeValue),this.$refs.btnxinji.$el.click()}}},b=y,v=(a("3acc"),Object(d["a"])(b,f,g,!1,null,"1a30246a",null)),D=v.exports,F=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"none"}},[a("div",{attrs:{id:"gangweiPrintContent",align:"center"}},[a("table",{staticStyle:{"border-collapse":"collapse","table-layout":"fixed",width:"413pt"},attrs:{"x:str":"x:str",border:"0",cellpadding:"0",cellspacing:"0",width:"590"}},[a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1056",width:"25pt"},attrs:{width:"33"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1024",width:"24pt"},attrs:{width:"32"}}),a("col",{staticStyle:{width:"54pt"},attrs:{width:"72"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1984",width:"47pt"},attrs:{width:"62"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1248",width:"29pt"},attrs:{width:"39"}}),a("col",{staticStyle:{width:"70pt"},attrs:{width:"72"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1056",width:"25pt"},attrs:{width:"33"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1120",width:"26pt"},attrs:{width:"35"}}),a("col",{staticStyle:{width:"60pt"},attrs:{width:"72"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1984",width:"47pt"},attrs:{width:"62"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1056",width:"25pt"},attrs:{width:"33"}}),a("col",{staticStyle:{"mso-width-source":"userset","mso-width-alt":"1376",width:"70pt"},attrs:{width:"53"}}),e._m(0),a("tr",{staticStyle:{height:"21pt"},attrs:{height:"28"}},[a("td",{staticStyle:{"text-align":"right",height:"15pt"},attrs:{colspan:"12"}},[a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0",width:"550","x:str":""}},[a("tr",[e._m(1),a("td",{staticClass:"xl6326579",attrs:{colspan:"5"}},[e._v(" 填表日期： "),a("label",[e._v(e._s(e.rowEntity.fillDate?new Date(e.rowEntity.fillDate).Format("yyyy-MM-dd"):""))]),a("font",{staticClass:"font726579"},[a("span",{staticStyle:{"mso-spacerun":"yes"}})])],1)]),e._m(2)])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{height:"26pt"},attrs:{colspan:"3"}},[e._v("姓 名")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"2","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!C2")'}},[a("label",[e._v(e._s(e.gangweiEntity.name))])]),a("td",{staticClass:"xl24",staticStyle:{height:"26pt"}},[e._v("性别")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"2","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!E2")'}},[a("label",[e._v(e._s(e.gangweiEntity.enumGender))])]),a("td",{staticClass:"xl24",staticStyle:{height:"26pt"}},[e._v("出生年月")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"3","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!D2")'}},[a("label",[e._v(e._s(e.gangweiEntity.birthday?new Date(e.gangweiEntity.birthday).Format("yyyy-MM"):""))])])]),e._m(3),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{height:"26pt"},attrs:{colspan:"3"}},[e._v("参加工作年月")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"2","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!C2")'}},[a("label",[e._v(e._s(e.gangweiEntity.societyDate?new Date(e.gangweiEntity.societyDate).Format("yyyy-MM"):""))])]),a("td",{staticClass:"xl24",staticStyle:{height:"26pt"}},[e._v("学历")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"2","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!E2")'}},[a("label",[e._v(e._s(e.gangweiEntity.educationName))])]),a("td",{staticClass:"xl24",staticStyle:{height:"26pt"}},[e._v("原聘任岗位")]),e._m(4)]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{height:"21pt"},attrs:{colspan:"3",height:"28"}},[e._v(" 现在聘任岗位 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[a("label",[e._v(e._s(e.gangweiEntity.positionName))])]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[e._v("聘任时间")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[a("label",[e._v(e._s(e.gangweiEntity.employeeStationStartDate?new Date(e.gangweiEntity.employeeStationStartDate).Format("yyyy-MM"):""))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"34.5pt"},attrs:{height:"46"}},[e._m(5),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 岗位工资 ")]),e._m(6),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none",padding:"0"}},[a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0","x:str":""}},[a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{"border-top":"none","border-left":"none","border-right":"none"}},[a("label",[e._v(e._s(e.gangweiEntity.beforeKey))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",[a("label",[e._v(e._s(e.gangweiEntity.beforeValue))]),e._v("元 ")])])])]),e._m(7),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 岗位工资 ")]),e._m(8),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none",padding:"0"}},[a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0","x:str":""}},[a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{"border-top":"none","border-left":"none","border-right":"none"}},[a("label",[e._v(e._s(e.gangweiEntity.afterKey))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",[a("label",[e._v(e._s(e.gangweiEntity.afterValue))]),e._v("元 ")])])])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"34.5pt"},attrs:{height:"46"}},[a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 薪级工资 ")]),e._m(9),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none",padding:"0"}},[a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0","x:str":""}},[a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{"border-top":"none","border-left":"none","border-right":"none"}},[e._v(" "+e._s(e.gangweiEntity.xinjiBeforeAfterInfo.beforeKey)+" ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",[e._v(e._s(e.gangweiEntity.xinjiBeforeAfterInfo.beforeValue)+"元")])])])]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 薪级工资 ")]),e._m(10),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none",padding:"0"}},[a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0","x:str":""}},[a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{"border-top":"none","border-left":"none","border-right":"none"}},[e._v(" "+e._s(e.gangweiEntity.xinjiBeforeAfterInfo.afterKey)+" ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",[e._v(e._s(e.gangweiEntity.xinjiBeforeAfterInfo.afterValue)+"元")])])])])]),e._m(11),a("tr",{staticStyle:{"mso-height-source":"userset",height:"20pt"},attrs:{height:"51"}},[a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 合计 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[e._v(" "+e._s(e.beforeincreaseAmount)+"元 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 合计 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"4"}},[e._v(" "+e._s(e.afterincreaseAmount)+"元 ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{height:"21pt"},attrs:{colspan:"3",height:"28"}},[e._v(" 增资额 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"9"}},[e._v(" "+e._s(e.increaseAmount)+"元 ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[e._m(12),a("td",{staticClass:"xl54",staticStyle:{"border-right":"0.5pt solid black"},attrs:{colspan:"11",rowspan:"5"}},[a("label",[e._v(e._s(e.rowEntity.advice))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),e._m(13),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[e._m(14),a("td",{staticClass:"xl54",staticStyle:{"border-right":"0.5pt solid black"},attrs:{colspan:"11",rowspan:"4"}},[a("label",[e._v(" 新确定工资从 "),a("span",[a("span",{staticStyle:{"mso-spacerun":"yes"}},[e._v(" "),a("label",[e._v(e._s(e.rowEntity.effectDate?new Date(e.rowEntity.effectDate).Format("yyyy-MM-dd"):""))])])]),e._v("起执行。 ")])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl37",staticStyle:{"border-right":"0.5pt solid black","border-bottom":"0.5pt solid black",height:"42pt"},attrs:{colspan:"11",rowspan:"2",height:"56"}},[e._v(" 单位或部门盖章： "),a("label",[e._v(e._s(e.rowEntity.date1?new Date(e.rowEntity.date1).Format("yyyy-MM-dd"):""))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),e._m(15),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}}),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt","font-size":"12pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl37",staticStyle:{"border-right":"0.5pt solid black","border-bottom":"0.5pt solid black",height:"42pt"},attrs:{colspan:"11",rowspan:"2",height:"56"}},[e._v(" 批准单位盖章： "),a("label",[e._v(e._s(e.rowEntity.date2?new Date(e.rowEntity.date2).Format("yyyy-MM-dd"):""))])])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}}),e._m(16),e._m(17),e._m(18),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl29",staticStyle:{height:"21pt","text-align":"left"},attrs:{colspan:"3",height:"28"}},[e._v(" 上海市人事局制 ")]),a("td",{staticStyle:{"mso-ignore":"colspan"},attrs:{colspan:"6"}}),a("td",{staticClass:"xl28"},[e._v("经办人：")]),a("td",{staticClass:"xl29",staticStyle:{"text-align":"right"},attrs:{colspan:"2"}},[a("label",[e._v(e._s(e.rowEntity.operator))])])])])]),a("el-button",{directives:[{name:"print",rawName:"v-print",value:e.gangweiprintObj,expression:"gangweiprintObj"}],ref:"btngangwei"})],1)},w=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{height:"21pt"},attrs:{height:"28"}},[a("td",{staticStyle:{"text-align":"center",height:"21pt"},attrs:{colspan:"12"}},[a("strong",[a("br"),a("br"),a("label"),a("span",{staticStyle:{"font-size":"16pt"}},[e._v("上海市事业单位工作人员确定工资审批表")])])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl2326579",staticStyle:{height:"14.25pt","text-align":"left"},attrs:{height:"19"}},[a("label",{staticStyle:{"font-size":"10pt"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",[a("td",{staticClass:"xl2426579",staticStyle:{height:"13.5pt","text-align":"left"},attrs:{height:"18"}},[a("label",{staticStyle:{"font-size":"7pt"}}),a("label",{staticStyle:{"font-size":"7pt"}})]),a("td",{staticClass:"xl2426579",staticStyle:{"text-align":"left"}}),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl1526579"}),a("td",{staticClass:"xl2526579",staticStyle:{width:"98px"},attrs:{"x:num":""}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{height:"21pt"},attrs:{colspan:"3",height:"28"}},[e._v(" 工作单位及部门 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"9"}},[e._v(" 上海交通大学医学院附属仁济医院 ")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl24",staticStyle:{"border-left":"none",height:"26pt"},attrs:{colspan:"3","x:err":"#REF!","x:fmla":'=INDIRECT("Sheet4!D2")'}},[a("label")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl30",staticStyle:{height:"126pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"4",height:"138"}},[e._v(" 原"),a("br"),e._v(" 工"),a("br"),e._v(" 资 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl55",staticStyle:{"border-left":"none",padding:"0"},attrs:{colspan:"2"}},[a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0","x:str":""}},[a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{"border-top":"none","border-left":"none","border-right":"none"}},[e._v(" 岗位 ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",[e._v("金额")])])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl30",staticStyle:{height:"126pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"4",height:"138"}},[e._v(" 新"),a("br"),e._v(" 确"),a("br"),e._v(" 定"),a("br"),e._v(" 工"),a("br"),e._v(" 资 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl55",staticStyle:{"border-left":"none",padding:"0"},attrs:{colspan:"2"}},[a("label",{staticStyle:{"font-bold":"false"}}),a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0","x:str":""}},[a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none","border-right":"none"}},[e._v(" 岗位 ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",[e._v("金额")])])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl55",staticStyle:{"border-left":"none",padding:"0"},attrs:{colspan:"2"}},[a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0","x:str":""}},[a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{"border-top":"none","border-left":"none","border-right":"none"}},[e._v(" 薪级 ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",[e._v("金额")])])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl55",staticStyle:{"border-left":"none",padding:"0"},attrs:{colspan:"2"}},[a("label",{staticStyle:{"font-bold":"false"}}),a("table",{staticStyle:{"table-layout":"fixed",width:"100%","border-collapse":"collapse"},attrs:{border:"0",cellpadding:"0",cellspacing:"0","x:str":""}},[a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{"border-top":"none","border-left":"none","border-right":"none"}},[e._v(" 薪级 ")])]),a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",[e._v("金额")])])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"20pt"},attrs:{height:"20"}},[a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 其他 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[e._v("元")]),a("td",{staticClass:"xl24",staticStyle:{"border-top":"none","border-left":"none"},attrs:{colspan:"2"}},[e._v(" 其他 ")]),a("td",{staticClass:"xl24",staticStyle:{"border-left":"none"},attrs:{colspan:"3"}},[e._v("元")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl30",staticStyle:{height:"126pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"6",height:"168"}},[e._v(" 确"),a("br"),e._v(" 定"),a("br"),e._v(" 工"),a("br"),e._v(" 资"),a("br"),e._v(" 理"),a("br"),e._v(" 由"),a("br")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl40",staticStyle:{"border-right":"0.5pt solid black",height:"21pt","border-left":"none"},attrs:{colspan:"11",height:"28"}},[a("label")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("td",{staticClass:"xl30",staticStyle:{height:"126pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"6",height:"168"}},[e._v(" 单"),a("br"),e._v(" 位"),a("br"),e._v(" 或"),a("br"),e._v(" 部"),a("br"),e._v(" 门"),a("br"),e._v(" 意"),a("br"),e._v(" 见 ")])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{height:"126pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"6",height:"168"}},[e._v(" 批"),a("br"),e._v(" 准"),a("br"),e._v(" 单"),a("br"),e._v(" 位"),a("br"),e._v(" 意"),a("br"),e._v(" 见"),a("br")]),a("td",{staticClass:"xl31",staticStyle:{"border-right":"0.5pt solid black"},attrs:{colspan:"11",rowspan:"4"}},[a("span",{staticStyle:{"font-size":"16pt","font-family":"新宋体"}},[e._v("同 意")])])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl30",staticStyle:{height:"80pt","border-top":"none",width:"25pt",direction:"ltr"},attrs:{rowspan:"3",height:"50"}},[e._v(" 备"),a("br"),e._v(" 注 ")]),a("td",{staticClass:"xl46",staticStyle:{"border-right":"0.5pt solid black",height:"21pt","border-left":"none"},attrs:{colspan:"11",height:"28"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl46",staticStyle:{"border-right":"0.5pt solid black",height:"21pt","border-left":"none"},attrs:{colspan:"11",height:"28"}})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",{staticStyle:{"mso-height-source":"userset",height:"21pt"},attrs:{height:"28"}},[a("td",{staticClass:"xl49",staticStyle:{"border-right":"0.5pt solid black",height:"21pt","border-left":"none"},attrs:{colspan:"11",height:"28"}})])}],S={data:function(){return{gangweiprintObj:{id:"gangweiPrintContent",popTitle:"",extraCss:"",extraHead:'<meta http-equiv="Content-Language" content="zh-cn"/>'},gangweiEntity:{xinjiBeforeAfterInfo:{}},rowEntity:{},beforeincreaseAmount:0,afterincreaseAmount:0,increaseAmount:0}},methods:{Print:function(){this.rowEntity=this.$parent.employeePayrollPrintModel,this.gangweiEntity=this.$parent.printInfoEntity,this.beforeincreaseAmount=parseFloat(this.gangweiEntity.beforeValue)+parseFloat(this.gangweiEntity.xinjiBeforeAfterInfo.beforeValue),this.afterincreaseAmount=parseFloat(this.gangweiEntity.afterValue)+parseFloat(this.gangweiEntity.xinjiBeforeAfterInfo.afterValue),this.increaseAmount=this.afterincreaseAmount-this.beforeincreaseAmount,this.$refs.btngangwei.$el.click()}}},x=S,M=(a("95bc"),Object(d["a"])(x,F,w,!1,null,"238f7f39",null)),_=M.exports,k={components:{zhiwu:h,xinji:D,gangwei:_},props:{printTemplate:{type:Object,default:function(){return{}}}},data:function(){return{rules:{money1:[{type:"number",min:0,max:**************,message:"基础部分一必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],money2:[{type:"number",min:0,max:**************,message:"基础部分二必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],advice:[{max:50,type:"string",message:"部门意见不允许超过50位字符",trigger:"blur"}]},printTemplateType:this.printTemplate.selecteditem,listLoading:!1,employeePayrollPrintList:[],employeePayrollPrintModel:{},total:1,listQuery:{total:3,pageIndex:1,pageSize:5},historyId:this.printTemplate.id,printInfoEntity:{}}},created:function(){this.getEmployeePayrollPrint(),this.getPrintDetailsInfo()},methods:{getEmployeePayrollPrint:function(){var e=this;this.listLoading=!0,l["a"].getEmployeePayrollPrint(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.employeePayrollPrintList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getEmployeePayrollPrint()},rowShowInfo:function(e,t,a){this.employeePayrollPrintModel=e},newly:function(){this.employeePayrollPrintModel={},this.$notice.message("新建成功","success")},save:function(){var e=this;"{}"!==JSON.stringify(this.employeePayrollPrintModel)?l["a"].updateEmployeePayrollPrint(this.employeePayrollPrintModel).then((function(t){t.succeed?(e.employeePayrollPrintModel={},e.getEmployeePayrollPrint(),e.$notice.message("保存成功","success")):-3!==t.type&&e.$notice.resultTip(t)})):this.newly()},deleted:function(){var e=this;void 0!==this.employeePayrollPrintModel.id?l["a"].deleteEmployeePayrollPrint(this.employeePayrollPrintModel).then((function(t){t.succeed?(e.employeePayrollPrintModel={},e.getEmployeePayrollPrint(),e.$notice.message("删除成功","success")):-3!==t.type&&e.$notice.resultTip(t)})):this.$notice.message("请选择要删除的数据","error")},print:function(){"0"!==this.printTemplateType?void 0!==this.employeePayrollPrintModel.id?"1"===this.printTemplateType?this.$refs.childzhiwu.Print():"2"===this.printTemplateType?this.$refs.childgangwei.Print():"3"===this.printTemplateType&&this.$refs.childxinji.Print():this.$notice.message("请选择要打印的数据","error"):this.$notice.message("请选择打印模板","error")},getPrintDetailsInfo:function(){var e=this;l["a"].getPrintDetailsInfo({id:this.historyId}).then((function(t){t.succeed&&(e.printInfoEntity=t.data)}))}}},E=k,I=Object(d["a"])(E,i,n,!1,null,null,null),$=I.exports,T={name:"",components:{modelList:$},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{PayRollOrgClass:[],PayRollCompGroup:[],PayRollOrgSalary:[],PayRollOrgSalaryLevel:[],PayRollOrgPositionSalarys:[],workStateOptions:[],wageshistoryrules:{},tempData:{tempFormWagesModel:{}},listLoading:!1,WagesHistoryList:[],total:1,listQuery:{total:3,pageIndex:1,pageSize:5},salarySuspension:"",checksalarySuspensionColor:!1,IsViewAll:!1,wagesrules:{paySlipNumber:[{max:50,type:"string",message:"工资单编号不允许超过50位字符",trigger:"change"}],schoolAge:[{type:"number",min:0,max:100,message:"工资用学龄必须在0到100之间",transform:function(e){return e?Number(e):null}},{pattern:/^[0-9]\d*$/,message:"仅支持正整数"}],societyAge:[{type:"number",min:0,max:100,message:"实际工龄必须在0到100之间",transform:function(e){return e?Number(e):null}},{pattern:/^[0-9]\d*$/,message:"仅支持正整数"}],performance:[{type:"number",min:0,max:**************,message:"各项津贴-绩效必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],medicalSubsidy:[{type:"number",min:0,max:**************,message:"医保补贴必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],districtFee:[{type:"number",min:0,max:**************,message:"区差必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],houseFee:[{type:"number",min:0,max:**************,message:"房2%必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],singleChildFee:[{type:"number",min:0,max:**************,message:"独子费必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],trusteeshipFee:[{type:"number",min:0,max:**************,message:"托费必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],memberFee:[{type:"number",min:0,max:**************,message:"会费必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],carFee:[{type:"number",min:0,max:**************,message:"车贴必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],riceFee:[{type:"number",min:0,max:**************,message:"饭贴必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],personalAward:[{type:"number",min:0,max:**************,message:"个人奖金必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],bookFee:[{type:"number",min:0,max:**************,message:"书报费必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],nurseAgeWages:[{type:"number",min:0,max:**************,message:"护龄工资必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],otherWages:[{type:"number",min:0,max:**************,message:"其他基本工资必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],materialFee:[{type:"number",min:0,max:**************,message:"物补必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],mobileFee:[{type:"number",min:0,max:**************,message:"电话费必须在0到**************之间",transform:function(e){return e?Number(e):null}},{pattern:/((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,message:"仅支持整数或者两位小数"}],changeDate:[{required:!0,type:"string",message:"工资调整日期必填"}],remark:[{max:200,type:"string",message:"备注不允许超过200位字符",trigger:"blur"}]},dialogAppInfoVisible:!1,printTemplate:{}}},computed:{getCumulative:function(){this.tempData.tempFormWagesModel.orgClassId;var e=this.tempData.tempFormWagesModel.orgSalaryId,t=this.tempData.tempFormWagesModel.orgSalaryLevelId,a=this.tempData.tempFormWagesModel.orgPositionSalaryId;if(this.PayRollOrgSalary.length>0){var o=this.PayRollOrgSalary.filter((function(t,a,o){if(t.id==e)return t}));o=null==o||0==o.length||void 0==o||o==[]?0:o[0].value}if(this.PayRollOrgSalaryLevel.length>0){var r=this.PayRollOrgSalaryLevel.filter((function(e,a,o){if(e.id==t)return e}));r=null==r||0==r.length||void 0==r||r==[]?0:r[0].value}if(this.PayRollOrgPositionSalarys.length>0){var l=this.PayRollOrgPositionSalarys.filter((function(e,t,o){if(e.id==a)return e}));l=null==l||0==l.length||void 0==l||l==[]?0:l[0].value}var i=0;return void 0!=o&&(i+=parseFloat(o)),void 0!=r&&(i+=parseFloat(r)),void 0!=l&&(i+=parseFloat(l)),i}},created:function(){this.getWagesInfo(),this.LoadPayRollOrgClass(),this.LoadPayRollCompGroup(),this.LoadWorkState(),this.getWagesHistoryList()},methods:{getWagesInfo:function(){var e=this;this.empId&&l["a"].getWagesInfo({id:this.empId}).then((function(t){t.data&&(e.tempData.tempFormWagesModel=t.data,e.LoadPayRollOrgSalary(e.tempData.tempFormWagesModel.orgClassId),e.LoadPayRollOrgSalaryLevel(e.tempData.tempFormWagesModel.orgClassId),e.LoadPayRollOrgPositionSalarys(e.tempData.tempFormWagesModel.orgClassId),e.monthWithoutPay())})).catch((function(e){}))},LoadPayRollOrgClass:function(){var e=this;l["a"].queryPayRollOrgClass().then((function(t){e.PayRollOrgClass=t.data})).catch((function(e){}))},LoadPayRollCompGroup:function(){var e=this;l["a"].queryPayRollCompGroup().then((function(t){e.PayRollCompGroup=t.data})).catch((function(e){}))},LoadPayRollOrgSalary:function(e){var t=this;l["a"].queryPayRollOrgSalary({condition:e}).then((function(e){t.PayRollOrgSalary=e.data})).catch((function(e){}))},LoadPayRollOrgSalaryLevel:function(e){var t=this;l["a"].queryPayRollOrgSalaryLevel({condition:e}).then((function(e){t.PayRollOrgSalaryLevel=e.data})).catch((function(e){}))},LoadPayRollOrgPositionSalarys:function(e){var t=this;l["a"].queryPayRollOrgPositionSalarys({condition:e}).then((function(e){t.PayRollOrgPositionSalarys=e.data})).catch((function(e){}))},LoadWorkState:function(){var e=this;l["a"].queryWorkState().then((function(t){e.workStateOptions=t.data.datas}))},orgClassIdChange:function(e){this.$set(this.tempData.tempFormWagesModel,"orgSalaryId",""),this.$set(this.tempData.tempFormWagesModel,"orgSalaryLevelId",""),this.$set(this.tempData.tempFormWagesModel,"orgPositionSalaryId",""),this.LoadPayRollOrgSalary(e),this.LoadPayRollOrgSalaryLevel(e),this.LoadPayRollOrgPositionSalarys(e)},getWagesHistoryList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,this.listQuery.IsViewAll=this.IsViewAll,l["a"].queryWagesHistory(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.WagesHistoryList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getWagesHistoryList()},rowShowInfo:function(e,t,a){},save:function(){var e=this;this.tempData.tempFormWagesModel.ID=this.empId,this.tempData.tempFormWagesModel.HistoryList=this.WagesHistoryList,l["a"].updateEmployeeBenefit(this.tempData.tempFormWagesModel).then((function(t){t.succeed?(e.getWagesHistoryList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){},monthWithoutPay:function(){if(""!=this.tempData.tempFormWagesModel.stopPayDate&&void 0!=this.tempData.tempFormWagesModel.stopPayDate){var e=new Date(this.tempData.tempFormWagesModel.stopPayDate),t=parseInt(e.Format("yyyy")),a=parseInt(e.Format("MM")),o=parseInt((new Date).Format("MM")),r=parseInt((new Date).Format("yyyy")),l=o+1+12*(r-t)-(a+1);if(l<0)return void(this.salarySuspension="");this.salarySuspension="已停薪"+l+"个月",this.checksalarySuspensionColor=l>3}},printReport:function(e){this.printTemplate=e;var t="0";if("职务工资"==e.changeItem)t="1";else if("岗位工资"==e.changeItem)t="2";else{if("薪级工资"!=e.changeItem)return;t="3"}this.printTemplate.selecteditem=t,this.dialogAppInfoVisible=!0},handleClose:function(){this.dialogAppInfoVisible=!1,this.clear()}}},C=T,P=(a("d6e3"),a("9c64"),Object(d["a"])(C,o,r,!1,null,"21d5841e",null));t["a"]=P.exports},e061:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[e.userPermission.isShowBtnNew?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1):e._e(),e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"岗位级别",prop:"positionLevelIDs"}},[a("el-cascader",{staticStyle:{width:"260px"},attrs:{clearable:"",options:e.positionLevelOptions,props:{expandTrigger:"hover",value:"id",label:"name"},placeholder:"请选择岗位级别"},on:{change:e.positionLevelChange},model:{value:e.tempData.tempFormModel.positionLevelIDs,callback:function(t){e.$set(e.tempData.tempFormModel,"positionLevelIDs",t)},expression:"tempData.tempFormModel.positionLevelIDs"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"岗位名称",prop:"positionStationId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择岗位名称"},model:{value:e.tempData.tempFormModel.positionStationId,callback:function(t){e.$set(e.tempData.tempFormModel,"positionStationId",t)},expression:"tempData.tempFormModel.positionStationId"}},e._l(e.positionStationOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.positionName,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"起聘日期",prop:"startDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择起聘日期","value-format":"yyyy-MM-dd","picker-options":e.startPickerDisabled},model:{value:e.tempData.tempFormModel.startDate,callback:function(t){e.$set(e.tempData.tempFormModel,"startDate",t)},expression:"tempData.tempFormModel.startDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"终止日期",prop:"endDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择终止日期","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormModel.endDate,callback:function(t){e.$set(e.tempData.tempFormModel,"endDate",t)},expression:"tempData.tempFormModel.endDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"起聘发文编号",prop:"documentNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入起聘发文编号"},model:{value:e.tempData.tempFormModel.documentNo,callback:function(t){e.$set(e.tempData.tempFormModel,"documentNo",t)},expression:"tempData.tempFormModel.documentNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"终止发文编号",prop:"endDocumentNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入终止发文编号"},model:{value:e.tempData.tempFormModel.endDocumentNo,callback:function(t){e.$set(e.tempData.tempFormModel,"endDocumentNo",t)},expression:"tempData.tempFormModel.endDocumentNo"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"职称资格",prop:"qualifications"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入职称资格"},model:{value:e.tempData.tempFormModel.qualifications,callback:function(t){e.$set(e.tempData.tempFormModel,"qualifications",t)},expression:"tempData.tempFormModel.qualifications"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"聘任部门",prop:"department"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入聘任部门"},model:{value:e.tempData.tempFormModel.department,callback:function(t){e.$set(e.tempData.tempFormModel,"department",t)},expression:"tempData.tempFormModel.department"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"聘任职别",prop:"rankId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择聘任职别"},model:{value:e.tempData.tempFormModel.rankId,callback:function(t){e.$set(e.tempData.tempFormModel,"rankId",t)},expression:"tempData.tempFormModel.rankId"}},e._l(e.officialRankOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id,disabled:e.disabled}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"最高岗位",prop:"isTopStation"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择最高岗位"},model:{value:e.tempData.tempFormModel.isTopStation,callback:function(t){e.$set(e.tempData.tempFormModel,"isTopStation",t)},expression:"tempData.tempFormModel.isTopStation"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"最高岗位等级",prop:"isTopStationRank"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择最高岗位等级"},model:{value:e.tempData.tempFormModel.isTopStationRank,callback:function(t){e.$set(e.tempData.tempFormModel,"isTopStationRank",t)},expression:"tempData.tempFormModel.isTopStationRank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否兼职",prop:"isParttime"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择是否兼职"},model:{value:e.tempData.tempFormModel.isParttime,callback:function(t){e.$set(e.tempData.tempFormModel,"isParttime",t)},expression:"tempData.tempFormModel.isParttime"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否初聘"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择是否初聘"},model:{value:e.tempData.tempFormModel.isInitial,callback:function(t){e.$set(e.tempData.tempFormModel,"isInitial",t)},expression:"tempData.tempFormModel.isInitial"}})],1)],1),1===e.estype?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"行政职务",prop:"administrativePositionId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择行政职务"},model:{value:e.tempData.tempFormModel.administrativePositionId,callback:function(t){e.$set(e.tempData.tempFormModel,"administrativePositionId",t)},expression:"tempData.tempFormModel.administrativePositionId"}},e._l(e.administrativePositionOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1):e._e(),2===e.estype?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"专业技术职称",prop:"majorTechnicalId"}},[a("el-cascader",{ref:"cascaderMajorTechnical",staticStyle:{width:"260px"},attrs:{clearable:"",options:e.majorTechnicalOptions,props:{expandTrigger:"hover",value:"id",label:"title"},placeholder:"请选择专业技术职称"},on:{change:e.majorTechnicalChange},model:{value:e.majorTechnicalId,callback:function(t){e.majorTechnicalId=t},expression:"majorTechnicalId"}})],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[2===e.estype&&e.showMajorTechnicalInput?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"专业技术职称",prop:"majorTechnicalInput"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入专业技术职称"},model:{value:e.tempData.tempFormModel.majorTechnicalInput,callback:function(t){e.$set(e.tempData.tempFormModel,"majorTechnicalInput",t)},expression:"tempData.tempFormModel.majorTechnicalInput"}})],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageStationList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"聘任部门","min-width":"90px",sortable:"custom",prop:"Department"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.department))])]}}])}),a("el-table-column",{attrs:{label:"岗位名称","min-width":"75px",sortable:"custom",prop:"PositionStation.PositionName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.positionName))])]}}])}),a("el-table-column",{attrs:{label:"岗位级别","min-width":"75px",sortable:"custom",prop:"PositionStation.StationName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.positionLevel))])]}}])}),a("el-table-column",{attrs:{label:"起聘发文编号","min-width":"90px",sortable:"custom",prop:"DocumentNo"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.documentNo))])]}}])}),a("el-table-column",{attrs:{label:"终止发文编号","min-width":"90px",sortable:"custom",prop:"EndDocumentNo"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.endDocumentNo))])]}}])}),a("el-table-column",{attrs:{label:"起聘日期","min-width":"75px",sortable:"custom",prop:"StartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.startDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"终止日期","min-width":"75px",sortable:"custom",prop:"EndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.endDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"最高岗位","min-width":"75px",sortable:"custom",prop:"IsTopStation"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-checkbox",{attrs:{disabled:""},model:{value:o.isTopStation,callback:function(t){e.$set(o,"isTopStation",t)},expression:"row.isTopStation"}})]}}])}),a("el-table-column",{attrs:{label:"最高岗位等级","min-width":"90px",sortable:"custom",prop:"IsTopStationRank"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-checkbox",{attrs:{disabled:""},model:{value:o.isTopStationRank,callback:function(t){e.$set(o,"isTopStationRank",t)},expression:"row.isTopStationRank"}})]}}])}),a("el-table-column",{attrs:{label:"是否兼职","min-width":"75px",sortable:"custom",prop:"IsParttime"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-checkbox",{attrs:{disabled:""},model:{value:o.isParttime,callback:function(t){e.$set(o,"isParttime",t)},expression:"row.isParttime"}})]}}])}),a("el-table-column",{attrs:{label:"聘任职别","min-width":"75px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.officialRank))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteStation(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getStationList}})],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("d3b7"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},containRank:{type:Array,default:function(){return[]}},notContainRank:{type:Array,default:function(){return[]}},userPermission:{type:Object,default:function(){return{}}},estype:{type:Number,required:!0}},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.endDate){var t=new Date(e.tempData.tempFormModel.endDate),r=new Date(a);r<t?o():o(new Error("起聘日期不得晚于终止日期。"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.startDate){var t=new Date(e.tempData.tempFormModel.startDate),r=new Date(a);r>t?o():o(new Error("终止日期不得早于起聘日期。"))}else o()}),100)};return{rules:{positionLevelIDs:[{required:!0,message:"请选择岗位级别",trigger:"change"}],startDate:[{validator:t,trigger:"blur"}],endDate:[{validator:a,trigger:"blur"}],documentNo:[{max:200,message:"起聘发文编号不允许超过200个字符",trigger:"blur"}],endDocumentNo:[{max:200,message:"终止发文编号不允许超过200个字符",trigger:"blur"}],qualifications:[{max:200,message:"职称资格不允许超过200个字符",trigger:"blur"}],department:[{max:50,message:"聘任部门不允许超过50个字符",trigger:"blur"}],rankId:[{required:!0,message:"请选择聘任职别",trigger:"change"}]},total:1,listLoading:!1,tempData:{tempFormModel:{positionLevelIDs:[]}},startPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.endDate){var a=new Date(e.tempData.tempFormModel.endDate);return t.getTime()>a}}},endPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.startDate){var a=new Date(e.tempData.tempFormModel.startDate);return t.getTime()<a}}},datePickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()-864e4}},positionLevelOptions:[],positionStationOptions:[],pageStationList:[],listQuery:{employeeId:"",ContainRank:[],notContainRank:[],total:1,pageIndex:1,pageSize:5,estype:this.estype},officialRankOptions:[],administrativePositionOptions:[],majorTechnicalOptions:[],majorTechnicalId:[],showMajorTechnicalInput:!1}},created:function(){this.loadStation(),this.loadEmployeeOfficialRank(),this.getStationList(),this.loadAdministrativePosition(),this.loadmajorTechnicalOptions()},methods:{downloadexceltemplate:function(){var e=1===this.estype?"importempstationpost":"importempstationtitle";l["a"].downlodaImportExcelTemplate({type:e}).then((function(e){var t=a("19de"),o="EmployeeStationTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel:function(e){var t=this,a=1===this.estype?"importempstationpost":"importempstationtitle",o=e.file;l["a"].importExcel(o,{type:a}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a)}})).catch((function(e){return console.log(e)}))},loadStation:function(){var e=this;l["a"].queryStation({type:this.estype}).then((function(t){e.positionLevelOptions=t.data})).catch((function(e){console.log(e)}))},positionLevelChange:function(){var e=this.tempData.tempFormModel.positionLevelIDs[0];this.tempData.tempFormModel.positionLevelIDs.length>1&&(e=this.tempData.tempFormModel.positionLevelIDs[1]),this.tempData.tempFormModel.stationId=e,this.loadPositionStation(e)},majorTechnicalChange:function(){if(this.$refs["cascaderMajorTechnical"].getCheckedNodes().length>0){var e=this.$refs["cascaderMajorTechnical"].getCheckedNodes()[0].data.titleCode;this.showMajorTechnicalInput="999"===e,this.majorTechnicalId=this.$refs["cascaderMajorTechnical"].getCheckedNodes()[0].data.id,this.tempData.tempFormModel.majorTechnicalId=this.majorTechnicalId}},loadPositionStation:function(e){var t=this;e?l["a"].queryPositionStation({stationID:e}).then((function(e){t.positionStationOptions=e.data})).catch((function(e){console.log(e)})):this.tempData.tempFormModel.positionStationId=""},loadEmployeeOfficialRank:function(){var e=this;l["a"].queryRank().then((function(t){var a=t.data.datas;e.officialRankOptions=a})).catch((function(e){console.log(e)}))},loadAdministrativePosition:function(){var e=this;l["a"].queryAdministrativePosition().then((function(t){e.administrativePositionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getStationList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,this.listQuery.containRank=this.containRank,this.listQuery.notContainRank=this.notContainRank,l["a"].queryEmployeeStation(this.listQuery).then((function(t){t.succeed?(e.pageStationList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getStationList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.isTopStation||(e.tempData.tempFormModel.isTopStation=!1),e.tempData.tempFormModel.isTopStationRank||(e.tempData.tempFormModel.isTopStationRank=!1),e.tempData.tempFormModel.isParttime||(e.tempData.tempFormModel.isParttime=!1),e.tempData.tempFormModel.type=e.estype,2===e.estype&&(void 0===e.majorTechnicalId?e.tempData.tempFormModel.majorTechnicalId=null:"{}"!==JSON.stringify(e.majorTechnicalId)&&e.majorTechnicalId.length>0?e.tempData.tempFormModel.majorTechnicalId=e.majorTechnicalId:e.tempData.tempFormModel.majorTechnicalId=null),e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeStation(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getStationList(),e.clear(),e.$notice.message("新增成功。","success"),e.uploadEmployee(t.data.uid,2)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeStation(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getStationList(),e.$notice.message("修改成功。","success"),e.uploadEmployee(t.data.uid,2)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},uploadEmployee:function(e,t){var a={condition:e};l["a"].getPersonnelInformation(a).then((function(e){})).catch((function(e){console.log(e)}))},clear:function(){this.majorTechnicalId=[],delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel.isTopStation=!1,this.tempData.tempFormModel.isTopStationRank=!1,this.tempData.tempFormModel.isParttime=!1,this.tempData.tempFormModel.isInitial=!1},showInfo:function(e){this.majorTechnicalId=e.majorTechnicalId,this.loadPositionStation(e.stationId),this.tempData.tempFormModel=JSON.parse(JSON.stringify(e)),this.tempData.tempFormModel.stationId=e.stationId,this.tempData.tempFormModel.positionLevelIDs=e.stationId,this.showMajorTechnicalInput=!1,"999"===e.majorTechnicalTitleCode&&(this.showMajorTechnicalInput=!0)},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteStation:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeStation(e).then((function(e){e.succeed?(t.getStationList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))},loadmajorTechnicalOptions:function(){var e=this;l["a"].queryMajorTechnical().then((function(t){e.majorTechnicalOptions=t.data})).catch((function(e){console.log(e)}))}}},n=i,s=(a("1058"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"64779ff5",null);t["a"]=c.exports},e44c:function(e,t,a){"use strict";a("4160"),a("b64b"),a("159b");var o=a("cfe3"),r="HR",l=new o["a"](r);t["a"]={queryEmployee:function(e){return l.get("QueryEmployee",e)},queryEmployeeByCommonCondition:function(e){return l.post("QueryEmployeeByCommonCondition",e)},queryEmployeeStatus:function(){return l.get("QueryEmployeeStatus")},queryRank:function(){return l.get("QueryRank")},queryAdministrativePosition:function(){return l.get("queryAdministrativePosition")},queryMajorTechnical:function(){return l.get("queryMajorTechnical")},queryOfficialRank:function(){return l.get("QueryOfficialRank")},queryHireStyle:function(){return l.get("QueryHireStyle")},queryLeaveStyle:function(){return l.get("QueryLeaveStyle")},queryMarryList:function(){return l.get("QueryMarryList")},queryNationality:function(){return l.get("QueryNationality")},queryRegisterType:function(){return l.get("QueryRegisterType")},deleteEmployee:function(e){return l.post("DeleteEmployee",e)},queryDocumentType:function(){return l.get("QueryDocumentType")},addEmployee:function(e){return l.post("AddEmployee",e)},isEmpCodeExists:function(e){var t={id:e.id,empCode:e.empCode};return l.get("IsEmpCodeExists",t)},checkIdentityNumber:function(e){var t={id:e.id,identityNumber:e.identityNumber};return l.get("checkIdentityNumber",t)},getEmployee:function(e){return l.get("GetEmployee",e)},updateEmployee:function(e){return l.post("UpdateEmployee",e)},getPersonnelInformation:function(e){return l.get("GetPersonnelInformation",e)},postAddDocumentInformation:function(e){return l.get("PostAddDocumentInformation",e)},postUpdateDocumentInformation:function(e){return l.get("PostUpdateDocumentInformation",e)},postDeleteDocumentInformation:function(e){return l.get("PostDeleteDocumentInformation",e)},queryEmployeeByConditions:function(e){return l.post("QueryEmployeeByConditions",e)},queryEmployeeByCommonConditions:function(e){return l.post("QueryEmployeeByCommonConditions",e)},queryEmployeeQuerySetting:function(e){return l.get("QueryEmployeeQuerySetting",e)},querySettingColumns:function(){return l.get("QueryEmployeeInfoQuerySettingColumns")},queryOperations:function(e){return l.get("QueryOperationByColumnType",e)},addEmployeeQuerySetting:function(e){return l.post("AddEmployeeQuerySetting",e)},deleteEmployeeQuerySetting:function(e){return l.post("DeleteEmployeeQuerySetting",e)},queryDegrees:function(){return l.get("QueryDegrees")},queryEducation:function(){return l.get("QueryEducation")},QuerySocialSecurity:function(){return l.get("QuerySocialSecurity")},queryParty:function(){return l.get("QueryParty")},queryRecruitmentCategory:function(){return l.get("QueryRecruitmentCategory")},queryRecruitmentCompany:function(){return l.get("QueryRecruitmentCompany")},getEmployeeHR:function(e){return l.get("GetEmployeeHR",e)},updateEmployeeHR:function(e){return l.post("UpdateEmployeeHR",e)},updateCompanyAge:function(){return l.post("UpdateCompanyAge")},calculateGeneralHoliday:function(){return l.get("CalculateGeneralHoliday")},queryStation:function(e){return l.get("QueryStation",e)},queryPositionStation:function(e){return l.get("QueryPositionStation",e)},queryEmployeeStation:function(e){return l.get("QueryEmployeeStation",e)},addEmployeeStation:function(e){return l.post("AddEmployeeStation",e)},updateEmployeeStation:function(e){return l.post("UpdateEmployeeStation",e)},deleteEmployeeStation:function(e){return l.post("DeleteEmployeeStation",e)},queryLevel:function(){return l.get("QueryLevel")},queryEmployeeCertify:function(e){return l.get("QueryEmployeeCertify",e)},addEmployeeCertify:function(e){return l.post("AddEmployeeCertify",e)},updateEmployeeCertify:function(e){return l.post("UpdateEmployeeCertify",e)},deleteEmployeeCertify:function(e){return l.post("DeleteEmployeeCertify",e)},queryGraduation:function(){return l.get("QueryGraduation")},queryLearnWay:function(){return l.get("QueryLearnWay")},queryEmployeeEducation:function(e){return l.get("QueryEmployeeEducation",e)},addEmployeeEducation:function(e){return l.post("AddEmployeeEducation",e)},updateEmployeeEducation:function(e){return l.post("UpdateEmployeeEducation",e)},deleteEmployeeEducation:function(e){return l.post("DeleteEmployeeEducation",e)},queryEmployeeWork:function(e){return l.get("QueryEmployeeWork",e)},addEmployeeWork:function(e){return l.post("AddEmployeeWork",e)},updateEmployeeWork:function(e){return l.post("UpdateEmployeeWork",e)},deleteEmployeeWork:function(e){return l.post("DeleteEmployeeWork",e)},queryAbroadType:function(){return l.get("QueryAbroadType")},queryEmployeeAbroad:function(e){return l.get("QueryEmployeeAbroadInfo",e)},addEmployeeAbroad:function(e){return l.post("AddEmployeeAbroadInfo",e)},updateEmployeeAbroad:function(e){return l.post("UpdateEmployeeAbroadInfo",e)},deleteEmployeeAbroad:function(e){return l.post("DeleteEmployeeAbroadInfo",e)},queryContractType:function(){return l.get("QueryContractType")},queryEmployeeContract:function(e){return l.get("QueryEmployeeContract",e)},queryRenewEmployeeContract:function(e){return l.get("QueryRenewEmployeeContract",e)},updateNextTimeRemind:function(e){return l.post("UpdateNextTimeRemind",e)},sendEmailForRenewRemind:function(e){return l.post("SendEmailForRenewRemind",e)},batchUpdateNextTimeRemind:function(e){return l.post("BatchUpdateNextTimeRemind",e)},batchSendEmailForRenewRemind:function(e){return l.post("BatchSendEmailForRenewRemind",e)},addEmployeeContract:function(e){return l.post("AddEmployeeContract",e)},updateEmployeeContract:function(e){return l.post("UpdateEmployeeContract",e)},deleteEmployeeContract:function(e){return l.post("DeleteEmployeeContract",e)},queryTrainLevel:function(){return l.get("QueryTrainLevel")},queryEmployeeTrain:function(e){return l.get("QueryEmployeeTrain",e)},addEmployeeTrain:function(e){return l.post("AddEmployeeTrain",e)},updateEmployeeTrain:function(e){return l.post("UpdateEmployeeTrain",e)},deleteEmployeeTrain:function(e){return l.post("DeleteEmployeeTrain",e)},queryYearList:function(){return l.get("QueryYearList")},queryEvaluateResult:function(){return l.get("QueryEvaluateResult")},queryEmployeeAssessment:function(e){return l.get("QueryEmployeeAssessment",e)},addEmployeeAssessment:function(e){return l.post("AddEmployeeAssessment",e)},updateEmployeeAssessment:function(e){return l.post("UpdateEmployeeAssessment",e)},deleteEmployeeAssessment:function(e){return l.post("DeleteEmployeeAssessment",e)},queryEmployeeDeduct:function(e){return l.get("QueryEmployeeDeduct",e)},addEmployeeDeduct:function(e){return l.post("AddEmployeeDeduct",e)},updateEmployeeDeduct:function(e){return l.get("UpdateEmployeeDeduct",e)},deleteEmployeeDeduct:function(e){return l.post("DeleteEmployeeDeduct",e)},downloadEmployeeDeductTemplate:function(e){return l.getFile("DownlodaEmployeeDeductTemplate",e)},importEmployeeDeduct:function(e,t){var a=new FormData;return t&&Object.keys(t).forEach((function(e){return a.append(e,t[e])})),a.append("file",e),l.postForm("ImportEmployeeDeduct",a)},queryEmployeeDeductUnCalculate:function(e){return l.get("QueryEmployeeDeductUnCalculate",e)},queryEmployeeDeductCalculate:function(e){return l.get("QueryEmployeeDeductCalculate",e)},getEmployeeDeductCalculate:function(e){return l.get("GetEmployeeDeductCalculate",e)},updateEmployeeDeductCalculate:function(e){return l.post("UpdateEmployeeDeductCalculate",e)},exportEmployeeDeductCalculate:function(e){return l.post("ExportEmployeeDeductCalculate",{data:e,responseType:"arraybuffer"})},queryEmployeeDeductWorkingAge:function(e){return l.get("QueryEmployeeDeductWorkingAge",e)},getEmployeeDeductWorkingAge:function(e){return l.get("GetEmployeeDeductWorkingAge",e)},addEmployeeDeductWorkingAge:function(e){return l.post("AddEmployeeDeductWorkingAge",e)},updateEmployeeDeductWorkingAge:function(e){return l.post("UpdateEmployeeDeductWorkingAge",e)},deleteEmployeeDeductWorkingAge:function(e){return l.post("DeleteEmployeeDeductWorkingAge",e)},queryIncentType:function(){return l.get("QueryIncentType")},queryIncentLevel:function(){return l.get("QueryIncentLevel")},queryEmployeeIncentive:function(e){return l.get("QueryEmployeeIncentive",e)},addEmployeeIncentive:function(e){return l.post("AddEmployeeIncentive",e)},updateEmployeeIncentive:function(e){return l.post("UpdateEmployeeIncentive",e)},deleteEmployeeIncentive:function(e){return l.post("DeleteEmployeeIncentive",e)},getEmployeeHealth:function(e){return l.get("GetEmployeeHealth",e)},updateEmployeeHealth:function(e){return l.post("UpdateEmployeeHealth",e)},queryAccidentType:function(){return l.get("QueryAccidentType")},queryEmployeeAccident:function(e){return l.get("QueryEmployeeAccident",e)},addEmployeeAccident:function(e){return l.post("AddEmployeeAccident",e)},updateEmployeeAccident:function(e){return l.post("UpdateEmployeeAccident",e)},deleteEmployeeAccident:function(e){return l.post("DeleteEmployeeAccident",e)},queryEmployeeTeach:function(e){return l.get("QueryEmployeeTeach",e)},addEmployeeTeach:function(e){return l.post("AddEmployeeTeach",e)},updateEmployeeTeach:function(e){return l.post("UpdateEmployeeTeach",e)},deleteEmployeeTeach:function(e){return l.post("DeleteEmployeeTeach",e)},queryEmployeeRelation:function(e){return l.get("QueryEmployeeRelation",e)},addEmployeeRelation:function(e){return l.post("AddEmployeeRelation",e)},updateEmployeeRelation:function(e){return l.post("UpdateEmployeeRelation",e)},deleteEmployeeRelation:function(e){return l.post("DeleteEmployeeRelation",e)},queryIncomeType:function(){return l.get("QueryIncomeType")},queryEmployeeArticle:function(e){return l.get("QueryEmployeeArticle",e)},addEmployeeArticle:function(e){return l.post("AddEmployeeArticle",e)},updateEmployeeArticle:function(e){return l.post("UpdateEmployeeArticle",e)},deleteEmployeeArticle:function(e){return l.post("DeleteEmployeeArticle",e)},queryClassLevel:function(){return l.get("QueryClassLevel")},queryEmployeeClass:function(e){return l.get("QueryEmployeeClass",e)},addEmployeeClass:function(e){return l.post("AddEmployeeClass",e)},updateEmployeeClass:function(e){return l.post("UpdateEmployeeClass",e)},deleteEmployeeClass:function(e){return l.post("DeleteEmployeeClass",e)},queryEmployeePatent:function(e){return l.get("QueryEmployeePatent",e)},addEmployeePatent:function(e){return l.post("AddEmployeePatent",e)},updateEmployeePatent:function(e){return l.post("UpdateEmployeePatent",e)},deleteEmployeePatent:function(e){return l.post("DeleteEmployeePatent",e)},queryTeacherType:function(){return l.get("QueryTeacherType")},queryEmployeeTeacher:function(e){return l.get("QueryEmployeeTeacher",e)},addEmployeeTeacher:function(e){return l.post("AddEmployeeTeacher",e)},updateEmployeeTeacher:function(e){return l.post("UpdateEmployeeTeacher",e)},deleteEmployeeTeacher:function(e){return l.post("DeleteEmployeeTeacher",e)},queryAwardLevel:function(){return l.get("QueryAwardLevel")},queryDictByParentCode:function(e){return l.get("QueryDictByParentCode",e)},queryHighTalent:function(){return l.get("QueryHighTalent")},queryEmployeeAward:function(e){return l.get("QueryEmployeeAward",e)},addEmployeeAward:function(e){return l.post("AddEmployeeAward",e)},updateEmployeeAward:function(e){return l.post("UpdateEmployeeAward",e)},deleteEmployeeAward:function(e){return l.post("DeleteEmployeeAward",e)},addEmployeeHighTalent:function(e){return l.post("AddEmployeeHighTalent",e)},updateEmployeeHighTalent:function(e){return l.post("UpdateEmployeeHighTalent",e)},queryEmployeeHighTalent:function(e){return l.get("QueryEmployeeHighTalent",e)},deleteEmployeeHighTalent:function(e){return l.post("DeleteEmployeeHighTalent",e)},queryEmployeeDeptHistory:function(e){return l.get("QueryEmployeeDeptHistory",e)},updateEmployeeDept:function(e){return l.post("UpdateEmployeeDept",e)},batchUpdateEmployeeDept:function(e){return l.post("BatchUpdateEmployeeDept",e)},queryEmployeeList:function(e){return l.post("QueryEmployeeList",e)},queryEmployeeListTree:function(e){return l.get("QueryEmployeeListTree",e)},queryDictsSetting:function(e){return l.get("QueryDictsSetting",e)},queryEmployeeListSettingTree:function(e){return l.get("QueryEmployeeListSettingTree",e)},queryAdvancedQueryType:function(){return l.get("QueryAdvancedQueryType")},saveEmployeeList:function(e){return l.post("SaveEmployeeList",e)},deleteEmployeeList:function(e){return l.post("DeleteEmployeeList",e)},getEmployeeList:function(e){return l.get("GetEmployeeList",e)},getEmployeeListExcel:function(e){return l.getFile("GetEmployeeListExcel",e)},queryDeptPrincipal:function(e){return l.get("QueryDeptPrincipal",e)},queryOtherEmployeeInfo:function(e){return l.post("QueryOtherEmployeeInfo",e)},saveOtherEmployeeInfo:function(e){return l.post("SaveOtherEmployeeInfo",e)},deleteOtherEmployeeInfo:function(e){return l.post("DeleteOtherEmployeeInfo",e)},queryOtherEmpTypes:function(e){return l.get("QueryOtherEmpTypes",e)},importExcel:function(e,t){var a=new FormData;return t&&Object.keys(t).forEach((function(e){return a.append(e,t[e])})),a.append("file",e),l.postForm("ImportExcel",a)},downlodaImportExcelTemplate:function(e){return l.getFile("DownlodaImportExcelTemplate",e)},getSocialSecurityInfo:function(e){return l.get("getSocialSecurityInfo",e)},updateEmployeeSocialSecurity:function(e){return l.post("updateEmployeeSocialSecurity",e)},queryPayRollOrgClass:function(){return l.get("QueryPayRollOrgClass")},queryPayRollCompGroup:function(){return l.get("QueryPayRollCompGroup")},queryPayRollOrgSalary:function(e){return l.get("QueryPayRollOrgSalary",e)},queryPayRollOrgSalaryLevel:function(e){return l.get("QueryPayRollOrgSalaryLevel",e)},queryPayRollOrgPositionSalarys:function(e){return l.get("QueryPayRollOrgPositionSalarys",e)},queryWorkState:function(){return l.get("QueryWorkState")},getWagesInfo:function(e){return l.get("GetWagesInfo",e)},updateEmployeeBenefit:function(e){return l.post("UpdateEmployeeBenefit",e)},queryWagesHistory:function(e){return l.get("QueryWagesHistory",e)},getEmployeePayrollPrint:function(e){return l.get("GetEmployeePayrollPrint",e)},updateEmployeePayrollPrint:function(e){return l.post("UpdateEmployeePayrollPrint",e)},deleteEmployeePayrollPrint:function(e){return l.post("DeleteEmployeePayrollPrint",e)},getPrintDetailsInfo:function(e){return l.get("GetPrintDetailsInfo",e)},getEmployeeWage:function(e){return l.get("GetEmployeeWage",e)},editEmployeeWage:function(e){return l.post("EditEmployeeWage",e)},editEmployeeParty:function(e){return l.post("EditEmployeeParty",e)},queryEmployeeHRAffiliatedBranch:function(e){return l.get("QueryEmployeeHRAffiliatedBranch",e)},getEmployeeHRAffiliatedBranch:function(e){return l.get("GetEmployeeHRAffiliatedBranch",e)},addEmployeeHRAffiliatedBranch:function(e){return l.post("AddEmployeeHRAffiliatedBranch",e)},updateEmployeeHRAffiliatedBranch:function(e){return l.post("UpdateEmployeeHRAffiliatedBranch",e)},deleteEmployeeHRAffiliatedBranch:function(e){return l.post("DeleteEmployeeHRAffiliatedBranch",e)},queryEmployeeHRDict:function(e){return l.get("QueryEmployeeHRDict",e)},getEmployeeHRDict:function(e){return l.get("GetEmployeeHRDict",e)},addEmployeeHRDict:function(e){return l.post("AddEmployeeHRDict",e)},updateEmployeeHRDict:function(e){return l.post("UpdateEmployeeHRDict",e)},deleteEmployeeHRDict:function(e){return l.post("DeleteEmployeeHRDict",e)},queryEmployeeHRPartyMemberHonor:function(e){return l.get("QueryEmployeeHRPartyMemberHonor",e)},getEmployeeHRPartyMemberHonor:function(e){return l.get("GetEmployeeHRPartyMemberHonor",e)},addEmployeeHRPartyMemberHonor:function(e){return l.post("AddEmployeeHRPartyMemberHonor",e)},updateEmployeeHRPartyMemberHonor:function(e){return l.post("UpdateEmployeeHRPartyMemberHonor",e)},deleteEmployeeHRPartyMemberHonor:function(e){return l.post("DeleteEmployeeHRPartyMemberHonor",e)}}},e58c:function(e,t,a){"use strict";var o=a("fc6a"),r=a("a691"),l=a("50c4"),i=a("a640"),n=a("ae40"),s=Math.min,c=[].lastIndexOf,p=!!c&&1/[1].lastIndexOf(1,-0)<0,m=i("lastIndexOf"),d=n("indexOf",{ACCESSORS:!0,1:0}),u=p||!m||!d;e.exports=u?function(e){if(p)return c.apply(this,arguments)||0;var t=o(this),a=l(t.length),i=a-1;for(arguments.length>1&&(i=s(i,r(arguments[1]))),i<0&&(i=a+i);i>=0;i--)if(i in t&&t[i]===e)return i||0;return-1}:c},e675:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("考核")])]),a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"考核项目",prop:"name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入考核项目"},model:{value:e.tempData.tempFormModel.name,callback:function(t){e.$set(e.tempData.tempFormModel,"name",t)},expression:"tempData.tempFormModel.name"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"考核年度",prop:"year"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择考核年度"},model:{value:e.tempData.tempFormModel.year,callback:function(t){e.$set(e.tempData.tempFormModel,"year",t)},expression:"tempData.tempFormModel.year"}},e._l(e.yearOptions,(function(e){return a("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"考核日期",prop:"evaluateDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择考核日期","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.evaluateDate,callback:function(t){e.$set(e.tempData.tempFormModel,"evaluateDate",t)},expression:"tempData.tempFormModel.evaluateDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"考核结果",prop:"resultId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择考核结果"},model:{value:e.tempData.tempFormModel.resultId,callback:function(t){e.$set(e.tempData.tempFormModel,"resultId",t)},expression:"tempData.tempFormModel.resultId"}},e._l(e.resultOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"考核人",prop:"evaluater"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入考核人"},model:{value:e.tempData.tempFormModel.evaluater,callback:function(t){e.$set(e.tempData.tempFormModel,"evaluater",t)},expression:"tempData.tempFormModel.evaluater"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"批准人",prop:"approver"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入批准人"},model:{value:e.tempData.tempFormModel.approver,callback:function(t){e.$set(e.tempData.tempFormModel,"approver",t)},expression:"tempData.tempFormModel.approver"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入备注"},model:{value:e.tempData.tempFormModel.remark,callback:function(t){e.$set(e.tempData.tempFormModel,"remark",t)},expression:"tempData.tempFormModel.remark"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageAssessmentList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"考核项目","min-width":"100px",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.name))])]}}])}),a("el-table-column",{attrs:{label:"考核年度","min-width":"80px",sortable:"custom",prop:"Year"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.year))])]}}])}),a("el-table-column",{attrs:{label:"考核结果","min-width":"80px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.resultName))])]}}])}),a("el-table-column",{attrs:{label:"考核人","min-width":"80px",sortable:"custom",prop:"Evaluater"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.evaluater))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteAssessment(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getAssessmentList}})],1)],1)],1)],1),a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("扣减工龄记录")])]),a("el-form",{ref:"dataFormForDeduct",attrs:{rules:e.rulesForDeduct,model:e.tempData.tempFormForDeduct,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnNew?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addDeduct}},[e._v("新增")]):e._e(),e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.saveDeduct}},[e._v("保存")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"考核年度",prop:"year"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择考核年度"},model:{value:e.tempData.tempFormForDeduct.year,callback:function(t){e.$set(e.tempData.tempFormForDeduct,"year",t)},expression:"tempData.tempFormForDeduct.year"}},e._l(e.yearOptionsForDeduct,(function(e){return a("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"扣减原因",prop:"reason"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入扣减原因"},model:{value:e.tempData.tempFormForDeduct.reason,callback:function(t){e.$set(e.tempData.tempFormForDeduct,"reason",t)},expression:"tempData.tempFormForDeduct.reason"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoadingForDeduct,expression:"listLoadingForDeduct"}],staticStyle:{width:"100%"},attrs:{data:e.pageDeductList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortDeduct,"row-click":e.rowShowDeductInfo}},[a("el-table-column",{attrs:{label:"扣减工龄年份","min-width":"100px",sortable:"custom",prop:"Year"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.year))])]}}])}),a("el-table-column",{attrs:{label:"扣减原因","min-width":"80px",sortable:"custom",prop:"Reason"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.reason))])]}}])}),a("el-table-column",{attrs:{label:"登记人","min-width":"80px",sortable:"custom",prop:"Record.DisplayName"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.recorderDisplayName))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteDeduct(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQueryForDeduct.total>0,expression:"listQueryForDeduct.total > 0"}],attrs:{total:e.listQueryForDeduct.total,"page-sizes":[5,10,20],page:e.listQueryForDeduct.pageIndex,limit:e.listQueryForDeduct.pageSize},on:{"update:page":function(t){return e.$set(e.listQueryForDeduct,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQueryForDeduct,"pageSize",t)},pagination:e.getDeductList}})],1)],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{rules:{name:[{required:!0,message:"请输入考核项目",trigger:"blur"},{max:100,message:"考核项目不允许超过100个字符",trigger:"blur"}],subject:[{required:!0,message:"请输入培养计划课题",trigger:"blur"},{max:200,message:"培养计划课题不允许超过200个字符",trigger:"blur"}],result:[{max:200,message:"考核成绩不允许超过200个字符",trigger:"blur"}],institute:[{max:100,message:"培养计划机构不允许超过100个字符",trigger:"blur"}],outlay:[{required:!1,type:"number",min:0,max:999999999,message:"培养经费必须在0到999999999之间",transform:function(e){return e?Number(e):null}}],completion:[{max:200,message:"完成情况不允许超过200个字符",trigger:"blur"}]},rulesForDeduct:{year:[{required:!0,message:"请选择考核年度",trigger:"change"}],reason:[{max:200,message:"扣减原因不允许超过200个字符",trigger:"blur"}]},total:1,listLoading:!1,listLoadingForDeduct:!1,tempData:{tempFormModel:{},tempFormForDeduct:{}},pageAssessmentList:[],pageDeductList:[],listQuery:{total:3,pageIndex:1,pageSize:5},listQueryForDeduct:{total:3,pageIndex:1,pageSize:5},yearOptions:[],yearOptionsForDeduct:[],resultOptions:[]}},created:function(){this.loadYear(),this.loadResult(),this.getAssessmentList(),this.getDeductList()},methods:{loadYear:function(){var e=this;l["a"].queryYearList().then((function(t){e.yearOptions=t.data.datas,e.yearOptionsForDeduct=JSON.parse(JSON.stringify(t.data.datas))})).catch((function(e){console.log(e)}))},loadResult:function(){var e=this;l["a"].queryEvaluateResult().then((function(t){e.resultOptions=t.data.datas})).catch((function(e){console.log(e)}))},getAssessmentList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeAssessment(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageAssessmentList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getAssessmentList()},add:function(){this.clearForm()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeAssessment(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getAssessmentList(),e.clearForm(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeAssessment(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getAssessmentList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clearForm:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={}},clear:function(){this.clearForm(),this.clearDeduct()},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteAssessment:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeAssessment(e).then((function(e){e.succeed?(t.getAssessmentList(),t.clearForm(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))},getDeductList:function(){var e=this;this.listLoadingForDeduct=!0,this.listQueryForDeduct.employeeId=this.empId,l["a"].queryEmployeeDeductWorkingAge(this.listQueryForDeduct).then((function(t){e.listLoadingForDeduct=!1,t.succeed?(e.pageDeductList=t.data.datas,e.listQueryForDeduct.total=t.data.recordCount,e.listQueryForDeduct.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoadingForDeduct=!1}))},sortDeduct:function(e,t,a){this.listQueryForDeduct.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQueryForDeduct.order=e.prop+" "+o,this.getDeductList()},addDeduct:function(){this.clearDeduct()},saveDeduct:function(){var e=this;this.$refs["dataFormForDeduct"].validate((function(t){t&&(e.tempData.tempFormForDeduct.id?e.updateDeduct():e.addNewDeduct())}))},addNewDeduct:function(){var e=this;this.tempData.tempFormForDeduct.employeeId=this.empId,l["a"].addEmployeeDeductWorkingAge(this.tempData.tempFormForDeduct).then((function(t){t.succeed?(e.tempData.tempFormForDeduct.id=t.data.id,e.getDeductList(),e.clearDeduct(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},updateDeduct:function(){var e=this;l["a"].updateEmployeeDeductWorkingAge(this.tempData.tempFormForDeduct).then((function(t){t.succeed?(e.getDeductList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clearDeduct:function(){delete this.tempData.tempFormForDeduct.id,this.$refs["dataFormForDeduct"].resetFields(),this.tempData.tempFormForDeduct={}},showDeductInfo:function(e){this.tempData.tempFormForDeduct=JSON.parse(JSON.stringify(e))},rowShowDeductInfo:function(e,t,a){this.showDeductInfo(e)},deleteDeduct:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeDeductWorkingAge(e).then((function(e){e.succeed?(t.getDeductList(),t.clearDeduct(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoadingForDeduct=!1,console.log(e)}))})).catch((function(e){t.listLoadingForDeduct=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("74ca"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"30c3924c",null);t["a"]=c.exports},e6a9:function(e,t,a){},f198:function(e,t,a){"use strict";var o=a("ba3d"),r=a.n(o);r.a},f6a1:function(e,t,a){"use strict";var o=a("aec3"),r=a.n(o);r.a},f82b:function(e,t,a){},f98d:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[e.userPermission.isShowBtnNew?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1):e._e(),e.userPermission.isShowBtnSave?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")])],1):e._e(),3===e.userPermission.userRight?a("el-col",{attrs:{span:2}},[a("el-upload",{attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[a("el-button",{attrs:{size:"mini",type:"primary"}},[e._v("导入")])],1)],1):e._e()],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同号",prop:"contractNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入合同号"},model:{value:e.tempData.tempFormModel.contractNo,callback:function(t){e.$set(e.tempData.tempFormModel,"contractNo",t)},expression:"tempData.tempFormModel.contractNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同类型",prop:"typeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择合同类型"},model:{value:e.tempData.tempFormModel.typeId,callback:function(t){e.$set(e.tempData.tempFormModel,"typeId",t)},expression:"tempData.tempFormModel.typeId"}},e._l(e.typeOptions,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同持续时间",prop:"continuanceDate"}},[a("el-input",{attrs:{type:"number",clearable:"",placeholder:"请输入合同持续时间"},model:{value:e.tempData.tempFormModel.continuanceDate,callback:function(t){e.$set(e.tempData.tempFormModel,"continuanceDate",e._n(t))},expression:"tempData.tempFormModel.continuanceDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"最新有效合同",prop:"isTheLatest"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择是否最新有效合同"},model:{value:e.tempData.tempFormModel.isTheLatest,callback:function(t){e.$set(e.tempData.tempFormModel,"isTheLatest",t)},expression:"tempData.tempFormModel.isTheLatest"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"开始时间",prop:"startDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择开始时间","value-format":"yyyy-MM-dd","picker-options":e.startPickerDisabled},model:{value:e.tempData.tempFormModel.startDate,callback:function(t){e.$set(e.tempData.tempFormModel,"startDate",t)},expression:"tempData.tempFormModel.startDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"结束时间",prop:"endDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择结束时间","value-format":"yyyy-MM-dd","picker-options":e.endPickerDisabled},model:{value:e.tempData.tempFormModel.endDate,callback:function(t){e.$set(e.tempData.tempFormModel,"endDate",t)},expression:"tempData.tempFormModel.endDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"试用期结束日",prop:"proEndDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择试用期结束日","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.proEndDate,callback:function(t){e.$set(e.tempData.tempFormModel,"proEndDate",t)},expression:"tempData.tempFormModel.proEndDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"试用期实际结束时间",prop:"proActualEndDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择试用期实际结束时间","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.proActualEndDate,callback:function(t){e.$set(e.tempData.tempFormModel,"proActualEndDate",t)},expression:"tempData.tempFormModel.proActualEndDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{clearable:"",type:"textarea",rows:2,placeholder:"请输入备注"},model:{value:e.tempData.tempFormModel.remark,callback:function(t){e.$set(e.tempData.tempFormModel,"remark",t)},expression:"tempData.tempFormModel.remark"}})],1)],1)],1),e._e(),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.pageContractList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange,"row-click":e.rowShowInfo}},[a("el-table-column",{attrs:{label:"合同号","min-width":"100px",sortable:"custom",prop:"ContractNo"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.contractNo))])]}}])}),a("el-table-column",{attrs:{label:"合同类型","min-width":"80px",sortable:"custom",prop:"Dict.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.typeName))])]}}])}),a("el-table-column",{attrs:{label:"开始时间","min-width":"80px",sortable:"custom",prop:"StartDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.startDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"结束时间","min-width":"80px",sortable:"custom",prop:"EndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.endDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"试用期结束日","min-width":"100px",sortable:"custom",prop:"ProEndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.proEndDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"试用期实际结束时间","min-width":"100px",sortable:"custom",prop:"ProActualEndDate"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",[e._v(e._s(o.proActualEndDateFormat))])]}}])}),a("el-table-column",{attrs:{label:"最新有效合同","min-width":"100px",sortable:"custom",prop:"ProIsTheLatest"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("el-checkbox",{attrs:{disabled:""},model:{value:o.isTheLatest,callback:function(t){e.$set(o,"isTheLatest",t)},expression:"row.isTheLatest"}})]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center","header-align":"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[e.userPermission.isShowBtnDelete?a("el-button",{staticClass:"el-icon-delete",attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteContract(o)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[5,10,20],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getContractList}})],1)],1)],1)],1)},r=[],l=(a("a9e3"),a("e44c")),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){var e=this,t=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.endDate){var t=new Date(e.tempData.tempFormModel.endDate),r=new Date(a);r<t?o():o(new Error("开始日期不得晚于结束日期。"))}else o()}),100)},a=function(t,a,o){if(!a)return o();setTimeout((function(){if(e.tempData.tempFormModel.startDate){var t=new Date(e.tempData.tempFormModel.startDate),r=new Date(a);r>t?o():o(new Error("结束日期不得早于开始日期。"))}else o()}),100)};return{rules:{contractNo:[{required:!0,message:"请输入合同号",trigger:"blur"},{max:100,message:"合同号不允许超过100个字符",trigger:"blur"}],continuanceDate:[{required:!1,type:"number",min:0,max:9999,message:"合同持续时间必须在0到9999之间",transform:function(e){return e?Number(e):null}},{pattern:/^[1-9][0-9]*$/,message:"仅支持整数"}],startDate:[{validator:t,trigger:"blur"}],endDate:[{validator:a,trigger:"blur"}],remark:[{max:200,message:"备注不允许超过200个字符",trigger:"blur"}]},total:1,listLoading:!1,tempData:{tempFormModel:{isTheLatest:!1}},startPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.endDate){var a=new Date(e.tempData.tempFormModel.endDate);return t.getTime()>a}}},endPickerDisabled:{disabledDate:function(t){if(e.tempData.tempFormModel.startDate){var a=new Date(e.tempData.tempFormModel.startDate);return t.getTime()<a}}},pageContractList:[],listQuery:{total:3,pageIndex:1,pageSize:5},typeOptions:[]}},created:function(){this.loadType(),this.getContractList()},methods:{downloadexceltemplate:function(){l["a"].downlodaImportExcelTemplate({type:"importempcontract"}).then((function(e){var t=a("19de"),o="EmployeeContractTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},importExcel:function(e){var t=this,a=e.file;l["a"].importExcel(a,{type:"importempcontract"}).then((function(e){if(e.succeed){var a=e.data;t.$message.success(a)}})).catch((function(e){}))},loadType:function(){var e=this;l["a"].queryContractType().then((function(t){e.typeOptions=t.data.datas})).catch((function(e){console.log(e)}))},getContractList:function(){var e=this;this.listLoading=!0,this.listQuery.employeeId=this.empId,l["a"].queryEmployeeContract(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.pageContractList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="desc"),"ascending"===e.order&&(o="asc"),this.listQuery.order=e.prop+" "+o,this.getContractList()},add:function(){this.clear()},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempData.tempFormModel.continuanceDate&&(e.tempData.tempFormModel.continuanceDate=parseInt(e.tempData.tempFormModel.continuanceDate,10)),e.tempData.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.tempData.tempFormModel.employeeId=this.empId,l["a"].addEmployeeContract(this.tempData.tempFormModel).then((function(t){t.succeed?(e.tempData.tempFormModel.id=t.data.id,e.getContractList(),e.clear(),e.$notice.message("新增成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;l["a"].updateEmployeeContract(this.tempData.tempFormModel).then((function(t){t.succeed?(e.getContractList(),e.$notice.message("修改成功","success")):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){delete this.tempData.tempFormModel.id,this.$refs["dataForm"].resetFields(),this.tempData.tempFormModel={},this.tempData.tempFormModel.isTheLatest=!1},showInfo:function(e){this.tempData.tempFormModel=JSON.parse(JSON.stringify(e))},rowShowInfo:function(e,t,a){this.showInfo(e)},deleteContract:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].deleteEmployeeContract(e).then((function(e){e.succeed?(t.getContractList(),t.clear(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1,e.succeed||t.$notice.message("取消删除","info")}))}}},n=i,s=(a("6aee"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"a1ebc33e",null);t["a"]=c.exports},f9ac:function(e,t,a){"use strict";var o=a("cfe3"),r="SysManage",l=new o["a"](r);t["a"]={queryDict:function(e){return l.get("QueryDict",e)},queryDictType:function(e){return l.post("QueryDictType",e)},addDict:function(e){return l.post("AddDict",e)},deleteDict:function(e){return l.post("DeleteDict",e)},updateDict:function(e){return l.post("UpdateDict",e)},getDict:function(e){return l.get("GetDict",e)},querySysSetting:function(e){return l.get("QuerySysSetting",e)},addSysSetting:function(e){return l.post("AddSysSetting",e)},deleteSysSetting:function(e){return l.post("DeleteSysSetting",e)},updateSysSetting:function(e){return l.post("UpdateSysSetting",e)},getSysSetting:function(e){return l.get("GetSysSetting",e)},queryLanguage:function(e){return l.get("QueryLanguage",e)},getEnumInfos:function(e){return l.get("GetEnumInfos",e)},queryUserGroups:function(e){return l.post("QueryUserGroups",e)},saveUserGroup:function(e){return l.post("SaveUserGroup",e)},deleteUserGroup:function(e){return l.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return l.get("DropdownUserGroups",e)},queryUsers:function(e){return l.post("QueryUsers",e)},saveUser:function(e){return l.post("SaveUser",e)},deleteUser:function(e){return l.post("DeleteUser",e)},initPwd:function(e){return l.post("InitPwd",e)},getUserById:function(e){return l.get("GetUserById",e)},queryEmployees:function(e){return l.post("QueryEmployees",e)},queryModuleInfos:function(e){return l.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return l.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return l.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return l.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return l.post("SaveRightOfDept",e)},queryControlRight:function(e){return l.post("QueryControlRight",e)},saveControlRights:function(e){return l.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return l.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return l.get("QueryStationTree",e)},queryStationTypeSelector:function(){return l.get("QueryStationTypeSelector")},queryStationSelector:function(e){return l.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return l.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return l.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return l.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return l.get("QueryStationAllowance",e)}}},ff52:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.tempData.tempFormModel,"label-position":"right","label-width":"100px"}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-form-item",[e.userPermission.isShowBtnSave?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document"},on:{click:e.save}},[e._v("保存")]):e._e(),e.userPermission.isShowBtnClear?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-document-delete"},on:{click:e.clear}},[e._v("清除")]):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"残疾名称",prop:"disabilityName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入残疾名称"},model:{value:e.tempData.tempFormModel.disabilityName,callback:function(t){e.$set(e.tempData.tempFormModel,"disabilityName",t)},expression:"tempData.tempFormModel.disabilityName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"身高",prop:"height"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入身高"},model:{value:e.tempData.tempFormModel.height,callback:function(t){e.$set(e.tempData.tempFormModel,"height",t)},expression:"tempData.tempFormModel.height"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否残疾",prop:"disability"}},[a("el-checkbox",{attrs:{clearable:"",placeholder:"请选择是否残疾"},model:{value:e.tempData.tempFormModel.disability,callback:function(t){e.$set(e.tempData.tempFormModel,"disability",t)},expression:"tempData.tempFormModel.disability"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"血型",prop:"bloodType"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入血型"},model:{value:e.tempData.tempFormModel.bloodType,callback:function(t){e.$set(e.tempData.tempFormModel,"bloodType",t)},expression:"tempData.tempFormModel.bloodType"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"RH类型",prop:"rhType"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入RH类型"},model:{value:e.tempData.tempFormModel.rhType,callback:function(t){e.$set(e.tempData.tempFormModel,"rhType",t)},expression:"tempData.tempFormModel.rhType"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"鞋子尺寸",prop:"shoeSize"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入鞋子尺寸"},model:{value:e.tempData.tempFormModel.shoeSize,callback:function(t){e.$set(e.tempData.tempFormModel,"shoeSize",t)},expression:"tempData.tempFormModel.shoeSize"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"体检情况一",prop:"examinationRemark1"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入体检情况一"},model:{value:e.tempData.tempFormModel.examinationRemark1,callback:function(t){e.$set(e.tempData.tempFormModel,"examinationRemark1",t)},expression:"tempData.tempFormModel.examinationRemark1"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"体检情况二",prop:"examinationRemark2"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入体检情况二"},model:{value:e.tempData.tempFormModel.examinationRemark2,callback:function(t){e.$set(e.tempData.tempFormModel,"examinationRemark2",t)},expression:"tempData.tempFormModel.examinationRemark2"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"体检情况三",prop:"examinationRemark3"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入体检情况三"},model:{value:e.tempData.tempFormModel.examinationRemark3,callback:function(t){e.$set(e.tempData.tempFormModel,"examinationRemark3",t)},expression:"tempData.tempFormModel.examinationRemark3"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"体检时间一",prop:"examinationDate1"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择体检时间一","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.examinationDate1,callback:function(t){e.$set(e.tempData.tempFormModel,"examinationDate1",t)},expression:"tempData.tempFormModel.examinationDate1"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"体检时间二",prop:"examinationDate2"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择体检时间二","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.examinationDate2,callback:function(t){e.$set(e.tempData.tempFormModel,"examinationDate2",t)},expression:"tempData.tempFormModel.examinationDate2"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"体检时间三",prop:"examinationDate3"}},[a("el-date-picker",{attrs:{clearable:"",type:"date",placeholder:"请选择体检时间三","value-format":"yyyy-MM-dd"},model:{value:e.tempData.tempFormModel.examinationDate3,callback:function(t){e.$set(e.tempData.tempFormModel,"examinationDate3",t)},expression:"tempData.tempFormModel.examinationDate3"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否纳入核酸",prop:"isNucleicAcid"}},[a("el-checkbox",{attrs:{clearable:""},model:{value:e.tempData.tempFormModel.isNucleicAcid,callback:function(t){e.$set(e.tempData.tempFormModel,"isNucleicAcid",t)},expression:"tempData.tempFormModel.isNucleicAcid"}})],1)],1)],1)],1)],1)},r=[],l=a("e44c"),i={name:"",components:{},props:{empId:{type:String,default:""},userPermission:{type:Object,default:function(){return{}}}},data:function(){return{rules:{disabilityName:[{max:200,message:"残疾名称不允许超过200个字符",trigger:"blur"}],height:[{max:20,message:"身高不允许超过20个字符",trigger:"blur"}],bloodType:[{max:20,message:"血型不允许超过20个字符",trigger:"blur"}],rhType:[{max:20,message:"RH类型不允许超过20个字符",trigger:"blur"}],shoeSize:[{max:20,message:"鞋子尺寸不允许超过20个字符",trigger:"blur"}],examinationRemark1:[{max:200,message:"体检情况一不允许超过200个字符",trigger:"blur"}],examinationRemark2:[{max:200,message:"体检情况二不允许超过200个字符",trigger:"blur"}],examinationRemark3:[{max:200,message:"体检情况三不允许超过200个字符",trigger:"blur"}]},tempData:{tempFormModel:{disability:!1,isNucleicAcid:!1}}}},created:function(){this.init()},methods:{init:function(){this.empId&&this.getEmployeeHealth(this.empId)},getEmployeeHealth:function(e){var t=this;l["a"].getEmployeeHealth({id:e}).then((function(e){e.data&&(t.tempData.tempFormModel=e.data)})).catch((function(e){console.log(e)}))},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.empId?e.update():e.$notice.message("请先新增基本信息。","info"))}))},update:function(){var e=this;this.tempData.tempFormModel.id=this.empId,l["a"].updateEmployeeHealth(this.tempData.tempFormModel).then((function(t){t.succeed?e.$notice.message("修改成功","success"):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){t.processed||e.$notice.message("修改失败。","error")}))},clear:function(){this.$refs["dataForm"].resetFields()}}},n=i,s=(a("f198"),a("2877")),c=Object(s["a"])(n,o,r,!1,null,"331202e0",null);t["a"]=c.exports}}]);