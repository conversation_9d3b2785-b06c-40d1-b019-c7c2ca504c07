(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6c6b1dc4"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"19de":function(e,t){e.exports=function(e,t,n,r){var o="undefined"!==typeof r?[r,e]:[e],a=new Blob(o,{type:n||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(a,t);else{var i=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(a):window.webkitURL.createObjectURL(a),u=document.createElement("a");u.style.display="none",u.href=i,u.setAttribute("download",t),"undefined"===typeof u.download&&u.setAttribute("target","_blank"),document.body.appendChild(u),u.click(),setTimeout((function(){document.body.removeChild(u),window.URL.revokeObjectURL(i)}),200)}}},"841c":function(e,t,n){"use strict";var r=n("d784"),o=n("825a"),a=n("1d80"),i=n("129f"),u=n("14c3");r("search",1,(function(e,t,n){return[function(t){var n=a(this),r=void 0==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var a=o(e),l=String(this),p=a.lastIndex;i(p,0)||(a.lastIndex=0);var c=u(a,l);return i(a.lastIndex,p)||(a.lastIndex=p),null===c?-1:c.index}]}))},aa67:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[n("el-checkbox",{on:{change:e.selectedChange},model:{value:e.listQuery.IsContainSubDept,callback:function(t){e.$set(e.listQuery,"IsContainSubDept",t)},expression:"listQuery.IsContainSubDept"}},[e._v("包含下级部门")]),n("c-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.treeLoading,expression:"treeLoading"}],attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[n("el-card",[n("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[n("el-form-item",[n("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择字段"},model:{value:e.listQuery.entityColumn,callback:function(t){e.$set(e.listQuery,"entityColumn",t)},expression:"listQuery.entityColumn"}},e._l(e.dataColumns,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e}})})),1)],1),n("el-form-item",[n("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"请选择条件"},model:{value:e.listQuery.queryCondition.EnumOperation,callback:function(t){e.$set(e.listQuery.queryCondition,"EnumOperation",t)},expression:"listQuery.queryCondition.EnumOperation"}},e._l(e.selectConditionOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1),n("el-form-item",[n("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",placeholder:"请输入关键字"},model:{value:e.listQuery.queryCondition.Keywords,callback:function(t){e.$set(e.listQuery.queryCondition,"Keywords",t)},expression:"listQuery.queryCondition.Keywords"}})],1),n("el-form-item",{attrs:{label:"在岗状态"}},[n("el-select",{staticStyle:{width:"120px"},attrs:{clearable:""},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.empStatusOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addDialog}},[e._v("添加")]),n("el-button",{attrs:{type:"primary"},on:{click:e.downloadexceltemplate}},[e._v("模板下载")]),n("el-upload",{staticStyle:{float:"right","margin-left":"10px"},attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[n("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload",type:"primary"},slot:"trigger"},[e._v("导入")])],1)],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"姓名",sortable:"custom",prop:"Name"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.name))])]}}])}),n("el-table-column",{attrs:{label:"身份证号",sortable:"custom",prop:"IDCard"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.idCard))])]}}])}),n("el-table-column",{attrs:{label:"联系电话",sortable:"custom",prop:"Phone"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.phone))])]}}])}),n("el-table-column",{attrs:{label:"科室",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.deptName))])]}}])}),n("el-table-column",{attrs:{label:"进院日期",sortable:"custom",prop:"EnterDateTime"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.enterDateTime?new Date(r.enterDateTime).Format("yyyy-MM-dd"):""))])]}}])}),n("el-table-column",{attrs:{label:"离院日期",sortable:"custom",prop:"LeaveDateTime"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.leaveDateTime?new Date(r.leaveDateTime).Format("yyyy-MM-dd"):""))])]}}])}),n("el-table-column",{attrs:{label:"人员类型",sortable:"custom",prop:"OtherEmpType.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.otherEmpTypeName))])]}}])}),n("el-table-column",{attrs:{label:"在岗状态",sortable:"custom",prop:"EmpStatus.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.empStateName))])]}}])}),n("el-table-column",{attrs:{label:"是否纳入核酸","min-width":"90px",sortable:"custom",prop:"IsNucleicAcid"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("span",[e._v(e._s(r.isNucleicAcid?"是":"否"))])]}}])}),n("el-table-column",{attrs:{fixed:"right",label:"操作",align:"left","header-align":"center",width:"180","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.updateDialog(r)}}},[e._v(" 编辑 ")]),n("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.deleteRecord(r)}}},[e._v(" 删除 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})],1)]},proxy:!0}])}),n("el-dialog",{attrs:{top:"5vh",title:"添加",visible:e.addDialogVisible,width:"50%"},on:{"update:visible":function(t){e.addDialogVisible=t},close:e.closeAddDialog}},[n("el-form",{ref:"ref_addForm",attrs:{rules:e.rules,model:e.addForm,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"姓名",prop:"name"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.addForm.name,callback:function(t){e.$set(e.addForm,"name",t)},expression:"addForm.name"}})],1),n("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.addForm.idCard,callback:function(t){e.$set(e.addForm,"idCard",t)},expression:"addForm.idCard"}})],1),n("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.addForm.phone,callback:function(t){e.$set(e.addForm,"phone",t)},expression:"addForm.phone"}})],1),n("el-form-item",{attrs:{label:"科室",prop:"departmentID"}},[n("el-cascader",{staticStyle:{width:"100%"},attrs:{clearable:"",options:e.departmentOptions,props:{expandTrigger:"hover",value:"id",label:"name",checkStrictly:!0},disabled:!0,placeholder:"请选择部门"},model:{value:e.addForm.departmentID,callback:function(t){e.$set(e.addForm,"departmentID",t)},expression:"addForm.departmentID"}})],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"进院日期",prop:"enterDateTime"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择进院日期"},model:{value:e.addForm.enterDateTime,callback:function(t){e.$set(e.addForm,"enterDateTime",t)},expression:"addForm.enterDateTime"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"离院日期",prop:"leaveDateTime"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择离院日期"},model:{value:e.addForm.leaveDateTime,callback:function(t){e.$set(e.addForm,"leaveDateTime",t)},expression:"addForm.leaveDateTime"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"人员类型",prop:"otherEmpTypeId"}},[n("el-select",{staticStyle:{width:"100%"},model:{value:e.addForm.otherEmpTypeId,callback:function(t){e.$set(e.addForm,"otherEmpTypeId",t)},expression:"addForm.otherEmpTypeId"}},e._l(e.otheremptype,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"在岗状态",prop:"empStatusId"}},[n("el-select",{staticStyle:{width:"100%"},model:{value:e.addForm.empStatusId,callback:function(t){e.$set(e.addForm,"empStatusId",t)},expression:"addForm.empStatusId"}},e._l(e.empStatusOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),n("el-form-item",{attrs:{label:"是否纳入核酸",prop:"isNucleicAcid"}},[n("el-checkbox",{model:{value:e.addForm.isNucleicAcid,callback:function(t){e.$set(e.addForm,"isNucleicAcid",t)},expression:"addForm.isNucleicAcid"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.addDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitAddForm}},[e._v("保 存")])],1)],1),n("el-dialog",{attrs:{top:"5vh",title:"更新",visible:e.updateDialogVisible,width:"50%"},on:{"update:visible":function(t){e.updateDialogVisible=t},close:e.closeupdateDialog}},[n("el-form",{ref:"ref_updateForm",attrs:{rules:e.rules,model:e.updateForm,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"姓名",prop:"name"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.updateForm.name,callback:function(t){e.$set(e.updateForm,"name",t)},expression:"updateForm.name"}})],1),n("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.updateForm.idCard,callback:function(t){e.$set(e.updateForm,"idCard",t)},expression:"updateForm.idCard"}})],1),n("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[n("el-input",{attrs:{placeholder:""},model:{value:e.updateForm.phone,callback:function(t){e.$set(e.updateForm,"phone",t)},expression:"updateForm.phone"}})],1),n("el-form-item",{attrs:{label:"科室",prop:"departmentID"}},[n("el-cascader",{staticStyle:{width:"100%"},attrs:{clearable:"",options:e.departmentOptions,props:{expandTrigger:"hover",value:"id",label:"name",checkStrictly:!0},disabled:!0,placeholder:"请选择部门"},model:{value:e.updateForm.departmentID,callback:function(t){e.$set(e.updateForm,"departmentID",t)},expression:"updateForm.departmentID"}})],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"进院日期",prop:"enterDateTime"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择进院日期"},model:{value:e.updateForm.enterDateTime,callback:function(t){e.$set(e.updateForm,"enterDateTime",t)},expression:"updateForm.enterDateTime"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"离院日期",prop:"leaveDateTime"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择离院日期"},model:{value:e.updateForm.leaveDateTime,callback:function(t){e.$set(e.updateForm,"leaveDateTime",t)},expression:"updateForm.leaveDateTime"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"人员类型",prop:"otherEmpTypeId"}},[n("el-select",{staticStyle:{width:"100%"},model:{value:e.updateForm.otherEmpTypeId,callback:function(t){e.$set(e.updateForm,"otherEmpTypeId",t)},expression:"updateForm.otherEmpTypeId"}},e._l(e.otheremptype,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"在岗状态",prop:"empStatusId"}},[n("el-select",{staticStyle:{width:"100%"},model:{value:e.updateForm.empStatusId,callback:function(t){e.$set(e.updateForm,"empStatusId",t)},expression:"updateForm.empStatusId"}},e._l(e.empStatusOptions,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),n("el-form-item",{attrs:{label:"是否纳入核酸",prop:"isNucleicAcid"}},[n("el-checkbox",{model:{value:e.updateForm.isNucleicAcid,callback:function(t){e.$set(e.updateForm,"isNucleicAcid",t)},expression:"updateForm.isNucleicAcid"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.updateDialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submitUpdateForm}},[e._v("保 存")])],1)],1)],1)},o=[],a=(n("d3b7"),n("ac1f"),n("841c"),n("e44c")),i=n("f9ac"),u=n("d368"),l={components:{},data:function(){var e=this,t=function(t,n,r){if(!n)return r();setTimeout((function(){var t=e.addForm.leaveDateTime||e.updateForm.leaveDateTime;if(t){var o=new Date(t),a=new Date(n);a<=o?r():r(new Error("进院日期不得晚于离院日期"))}else r()}),100)},n=function(t,n,r){if(!n)return r();setTimeout((function(){var t=e.addForm.enterDateTime||e.updateForm.enterDateTime;if(t){var o=new Date(t),a=new Date(n);a>=o?r():r(new Error("离院日期不得早于起聘进院。"))}else r()}),100)},r=function(e,t,n){var r=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;if(!t)return n();setTimeout((function(){r.test(t)?n():n(new Error("请输入正确的身份证号"))}),100)};return{treeData:[],treeExpandedKeys:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},addForm:{departmentID:null,enterDateTime:"",leaveDateTime:"",empStatusId:null,name:"",idCard:"",phone:"",otherEmpTypeId:null,isNucleicAcid:!1},updateForm:{},rules:{name:[{required:!0,message:"[姓名]是必填项！",trigger:"blur"},{max:100,message:"[姓名]超长！",trigger:"blur"}],idCard:[{max:18,message:"[身份证号]超长！",trigger:"blur"},{trigger:"blur",validator:r}],phone:[{max:11,message:"[联系电话]超长！",trigger:"blur"}],enterDateTime:[{validator:t,trigger:"blur"}],leaveDateTime:[{validator:n,trigger:"blur"}],otherEmpTypeId:[{required:!0,message:"[人员类型]是必选项！",trigger:["blur"]}]},addDialogVisible:!1,updateDialogVisible:!1,pageList:[],listQuery:{queryCondition:{},total:1,pageIndex:1,pageSize:10},listLoading:!1,dataColumns:[{value:"1",label:"姓名",type:"System.String",columnName:"Name"},{value:"2",label:"身份证号",type:"System.String",columnName:"IDCard"},{value:"3",label:"联系电话",type:"System.String",columnName:"Phone"}],selectConditionOptions:[],empStatusOptions:[],otheremptype:[],isShowDynamicQueryTable:!1,treeLoading:!1,currentNode:{},departmentOptions:[]}},created:function(){this.getPageList(),this.loadConditions(),this.loadTree(),this.loadEmployeeStatus(),this.loadOtherEmpType(),this.loadDepartment()},methods:{loadOtherEmpType:function(){var e=this;a["a"].queryOtherEmpTypes().then((function(t){e.otheremptype=t.data.datas})).catch((function(e){console.log(e)}))},loadDepartment:function(){var e=this;u["a"].QueryOrganization({}).then((function(t){e.departmentOptions=t.data})).catch((function(e){console.log(e)}))},loadTree:function(){var e=this;this.treeLoading=!0,u["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeData&&e.treeData.length>0&&e.treeExpandedKeys.push(e.treeData[0].id)})).catch((function(e){console.log(e)})).finally((function(){e.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(e){this.currentNode=e,this.listQuery.deptId=e.id,this.listQuery.pageIndex=1,this.isShowDynamicQueryTable=!1,this.getPageList()},loadEmployeeStatus:function(){var e=this;a["a"].queryEmployeeStatus().then((function(t){e.empStatusOptions=t.data.datas})).catch((function(e){console.log(e)}))},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.search()},search:function(){this.listQuery.pageIndex=1,this.getPageList()},loadConditions:function(){var e=this;i["a"].getEnumInfos({enumType:"Operations"}).then((function(t){e.selectConditionOptions=t.data.datas})).catch((function(e){console.log(e)}))},getPageList:function(){var e=this;this.listLoading=!0,this.listQuery.entityColumn&&""!==this.listQuery.entityColumn&&this.listQuery.queryCondition.EnumOperation?(this.listQuery.queryCondition.Keywords||(this.listQuery.queryCondition.Keywords=""),this.listQuery.queryCondition.EntityColumnName=this.listQuery.entityColumn.columnName,this.listQuery.queryCondition.EntityColumnType=this.listQuery.entityColumn.type,this.listQuery.queryCondition.EnumLogicRelationship=10,this.listQuery.ConditionList=[this.listQuery.queryCondition]):(this.listQuery.ConditionList=[],this.listQuery.queryCondition={}),a["a"].queryOtherEmployeeInfo(this.listQuery).then((function(t){t.succeed?(e.pageList=t.data.datas,e.listQuery.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(e){console.log(e)})).finally((function(){e.listLoading=!1}))},addDialog:function(){this.listQuery.deptId?(this.addForm.departmentID=this.listQuery.deptId,this.addDialogVisible=!0):this.$notice.message("请先选择部门。","info")},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()},updateDialog:function(e){this.updateDialogVisible=!0,console.log(e),this.updateForm=Object.assign({},e)},closeupdateDialog:function(){this.$refs["ref_updateForm"].resetFields(),this.$refs["ref_updateForm"].clearValidate()},submitAddForm:function(){var e=this;this.$refs["ref_addForm"].validate((function(t){t&&a["a"].saveOtherEmployeeInfo(e.addForm).then((function(t){t.succeed?(e.addDialogVisible=!1,e.search(),e.$notice.message("创建成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))}))},submitUpdateForm:function(){var e=this;this.$refs["ref_updateForm"].validate((function(t){t&&a["a"].saveOtherEmployeeInfo(e.updateForm).then((function(t){t.succeed?(e.updateDialogVisible=!1,e.search(),e.$notice.message("更新成功","success")):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))}))},deleteRecord:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a["a"].deleteOtherEmployeeInfo({id:e.id}).then((function(e){e.succeed?(t.search(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){console.log(e)}))})).catch((function(e){e.succeed||t.$notice.message("取消删除","info")}))},selectedChange:function(){this.isShowDynamicQueryTable||(this.listQuery.pageIndex=1,this.getPageList())},downloadexceltemplate:function(){a["a"].downlodaImportExcelTemplate({type:"importotheremp"}).then((function(e){var t=n("19de"),r="OtherEmployeeInfoTemplate.xlsx";e.data?t(e.data,r):t(e,r)})).catch((function(e){}))},importExcel:function(e){var t=this,n=e.file;a["a"].importExcel(n,{type:"importotheremp"}).then((function(e){if(e.succeed){var n=e.data;t.$message.success(n),t.search()}})).catch((function(e){}))}}},p=l,c=n("2877"),s=Object(c["a"])(p,r,o,!1,null,"d52574a2",null);t["default"]=s.exports},d368:function(e,t,n){"use strict";var r=n("cfe3"),o="Organization",a=new r["a"](o);t["a"]={QueryOrganizationHiddenTop:function(e){return a.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return a.get("QueryOrganization",e)},QueryDepartment:function(e){return a.get("QueryDepartment",e)},GetDepartment:function(e){return a.get("GetDepartment",e)},AddDepartment:function(e){return a.post("AddDepartment",e)},UpdateDepartment:function(e){return a.post("UpdateDepartment",e)},MoveDepartment:function(e){return a.post("MoveDepartment",e)},MergeDepartment:function(e){return a.post("MergeDepartment",e)},DeleteDepartment:function(e){return a.post("DeleteDepartment",e)},queryPosition:function(e){return a.post("QueryPosition",e)},getPosition:function(e){return a.get("GetPosition",e)},addPosition:function(e){return a.post("AddPosition",e)},updatePosition:function(e){return a.post("UpdatePosition",e)},deletePosition:function(e){return a.post("DeletePosition",e)},GetStation:function(e){return a.get("GetStation",e)},AddStation:function(e){return a.post("AddStation",e)},UpdateStation:function(e){return a.post("UpdateStation",e)},DeleteStation:function(e){return a.post("DeleteStation",e)},QueryPositionStationTree:function(e){return a.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return a.post("AllocatePosition",e)},DeletePositionStation:function(e){return a.post("DeletePositionStation",e)},queryDeptByUser:function(e){return a.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return a.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return a.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),a.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return a.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return a.get("GetStationAllowance",e)},addStationAllowance:function(e){return a.post("AddStationAllowance",e)},updateStationAllowance:function(e){return a.post("UpdateStationAllowance",e)},querySeniority:function(e){return a.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),a.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return a.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return a.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return a.get("GetSeniority",e)},addSeniority:function(e){return a.post("AddSeniority",e)},updateSeniority:function(e){return a.post("UpdateSeniority",e)},querySalaryScale:function(e){return a.get("QuerySalaryScale",e)},getSalaryScale:function(e){return a.get("GetSalaryScale",e)},addSalaryScale:function(e){return a.post("AddSalaryScale",e)},updateSalaryScale:function(e){return a.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return a.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),a.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return a.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return a.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return a.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return a.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return a.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return a.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return a.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return a.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return a.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return a.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return a.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return a.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return a.post("DeleteTelephoneFee",e)}}},e44c:function(e,t,n){"use strict";n("4160"),n("b64b"),n("159b");var r=n("cfe3"),o="HR",a=new r["a"](o);t["a"]={queryEmployee:function(e){return a.get("QueryEmployee",e)},queryEmployeeByCommonCondition:function(e){return a.post("QueryEmployeeByCommonCondition",e)},queryEmployeeStatus:function(){return a.get("QueryEmployeeStatus")},queryRank:function(){return a.get("QueryRank")},queryAdministrativePosition:function(){return a.get("queryAdministrativePosition")},queryMajorTechnical:function(){return a.get("queryMajorTechnical")},queryOfficialRank:function(){return a.get("QueryOfficialRank")},queryHireStyle:function(){return a.get("QueryHireStyle")},queryLeaveStyle:function(){return a.get("QueryLeaveStyle")},queryMarryList:function(){return a.get("QueryMarryList")},queryNationality:function(){return a.get("QueryNationality")},queryRegisterType:function(){return a.get("QueryRegisterType")},deleteEmployee:function(e){return a.post("DeleteEmployee",e)},queryDocumentType:function(){return a.get("QueryDocumentType")},addEmployee:function(e){return a.post("AddEmployee",e)},isEmpCodeExists:function(e){var t={id:e.id,empCode:e.empCode};return a.get("IsEmpCodeExists",t)},checkIdentityNumber:function(e){var t={id:e.id,identityNumber:e.identityNumber};return a.get("checkIdentityNumber",t)},getEmployee:function(e){return a.get("GetEmployee",e)},updateEmployee:function(e){return a.post("UpdateEmployee",e)},getPersonnelInformation:function(e){return a.get("GetPersonnelInformation",e)},postAddDocumentInformation:function(e){return a.get("PostAddDocumentInformation",e)},postUpdateDocumentInformation:function(e){return a.get("PostUpdateDocumentInformation",e)},postDeleteDocumentInformation:function(e){return a.get("PostDeleteDocumentInformation",e)},queryEmployeeByConditions:function(e){return a.post("QueryEmployeeByConditions",e)},queryEmployeeByCommonConditions:function(e){return a.post("QueryEmployeeByCommonConditions",e)},queryEmployeeQuerySetting:function(e){return a.get("QueryEmployeeQuerySetting",e)},querySettingColumns:function(){return a.get("QueryEmployeeInfoQuerySettingColumns")},queryOperations:function(e){return a.get("QueryOperationByColumnType",e)},addEmployeeQuerySetting:function(e){return a.post("AddEmployeeQuerySetting",e)},deleteEmployeeQuerySetting:function(e){return a.post("DeleteEmployeeQuerySetting",e)},queryDegrees:function(){return a.get("QueryDegrees")},queryEducation:function(){return a.get("QueryEducation")},QuerySocialSecurity:function(){return a.get("QuerySocialSecurity")},queryParty:function(){return a.get("QueryParty")},queryRecruitmentCategory:function(){return a.get("QueryRecruitmentCategory")},queryRecruitmentCompany:function(){return a.get("QueryRecruitmentCompany")},getEmployeeHR:function(e){return a.get("GetEmployeeHR",e)},updateEmployeeHR:function(e){return a.post("UpdateEmployeeHR",e)},updateCompanyAge:function(){return a.post("UpdateCompanyAge")},calculateGeneralHoliday:function(){return a.get("CalculateGeneralHoliday")},queryStation:function(e){return a.get("QueryStation",e)},queryPositionStation:function(e){return a.get("QueryPositionStation",e)},queryEmployeeStation:function(e){return a.get("QueryEmployeeStation",e)},addEmployeeStation:function(e){return a.post("AddEmployeeStation",e)},updateEmployeeStation:function(e){return a.post("UpdateEmployeeStation",e)},deleteEmployeeStation:function(e){return a.post("DeleteEmployeeStation",e)},queryLevel:function(){return a.get("QueryLevel")},queryEmployeeCertify:function(e){return a.get("QueryEmployeeCertify",e)},addEmployeeCertify:function(e){return a.post("AddEmployeeCertify",e)},updateEmployeeCertify:function(e){return a.post("UpdateEmployeeCertify",e)},deleteEmployeeCertify:function(e){return a.post("DeleteEmployeeCertify",e)},queryGraduation:function(){return a.get("QueryGraduation")},queryLearnWay:function(){return a.get("QueryLearnWay")},queryEmployeeEducation:function(e){return a.get("QueryEmployeeEducation",e)},addEmployeeEducation:function(e){return a.post("AddEmployeeEducation",e)},updateEmployeeEducation:function(e){return a.post("UpdateEmployeeEducation",e)},deleteEmployeeEducation:function(e){return a.post("DeleteEmployeeEducation",e)},queryEmployeeWork:function(e){return a.get("QueryEmployeeWork",e)},addEmployeeWork:function(e){return a.post("AddEmployeeWork",e)},updateEmployeeWork:function(e){return a.post("UpdateEmployeeWork",e)},deleteEmployeeWork:function(e){return a.post("DeleteEmployeeWork",e)},queryAbroadType:function(){return a.get("QueryAbroadType")},queryEmployeeAbroad:function(e){return a.get("QueryEmployeeAbroadInfo",e)},addEmployeeAbroad:function(e){return a.post("AddEmployeeAbroadInfo",e)},updateEmployeeAbroad:function(e){return a.post("UpdateEmployeeAbroadInfo",e)},deleteEmployeeAbroad:function(e){return a.post("DeleteEmployeeAbroadInfo",e)},queryContractType:function(){return a.get("QueryContractType")},queryEmployeeContract:function(e){return a.get("QueryEmployeeContract",e)},queryRenewEmployeeContract:function(e){return a.get("QueryRenewEmployeeContract",e)},updateNextTimeRemind:function(e){return a.post("UpdateNextTimeRemind",e)},sendEmailForRenewRemind:function(e){return a.post("SendEmailForRenewRemind",e)},batchUpdateNextTimeRemind:function(e){return a.post("BatchUpdateNextTimeRemind",e)},batchSendEmailForRenewRemind:function(e){return a.post("BatchSendEmailForRenewRemind",e)},addEmployeeContract:function(e){return a.post("AddEmployeeContract",e)},updateEmployeeContract:function(e){return a.post("UpdateEmployeeContract",e)},deleteEmployeeContract:function(e){return a.post("DeleteEmployeeContract",e)},queryTrainLevel:function(){return a.get("QueryTrainLevel")},queryEmployeeTrain:function(e){return a.get("QueryEmployeeTrain",e)},addEmployeeTrain:function(e){return a.post("AddEmployeeTrain",e)},updateEmployeeTrain:function(e){return a.post("UpdateEmployeeTrain",e)},deleteEmployeeTrain:function(e){return a.post("DeleteEmployeeTrain",e)},queryYearList:function(){return a.get("QueryYearList")},queryEvaluateResult:function(){return a.get("QueryEvaluateResult")},queryEmployeeAssessment:function(e){return a.get("QueryEmployeeAssessment",e)},addEmployeeAssessment:function(e){return a.post("AddEmployeeAssessment",e)},updateEmployeeAssessment:function(e){return a.post("UpdateEmployeeAssessment",e)},deleteEmployeeAssessment:function(e){return a.post("DeleteEmployeeAssessment",e)},queryEmployeeDeduct:function(e){return a.get("QueryEmployeeDeduct",e)},addEmployeeDeduct:function(e){return a.post("AddEmployeeDeduct",e)},updateEmployeeDeduct:function(e){return a.get("UpdateEmployeeDeduct",e)},deleteEmployeeDeduct:function(e){return a.post("DeleteEmployeeDeduct",e)},downloadEmployeeDeductTemplate:function(e){return a.getFile("DownlodaEmployeeDeductTemplate",e)},importEmployeeDeduct:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),a.postForm("ImportEmployeeDeduct",n)},queryEmployeeDeductUnCalculate:function(e){return a.get("QueryEmployeeDeductUnCalculate",e)},queryEmployeeDeductCalculate:function(e){return a.get("QueryEmployeeDeductCalculate",e)},getEmployeeDeductCalculate:function(e){return a.get("GetEmployeeDeductCalculate",e)},updateEmployeeDeductCalculate:function(e){return a.post("UpdateEmployeeDeductCalculate",e)},exportEmployeeDeductCalculate:function(e){return a.post("ExportEmployeeDeductCalculate",{data:e,responseType:"arraybuffer"})},queryEmployeeDeductWorkingAge:function(e){return a.get("QueryEmployeeDeductWorkingAge",e)},getEmployeeDeductWorkingAge:function(e){return a.get("GetEmployeeDeductWorkingAge",e)},addEmployeeDeductWorkingAge:function(e){return a.post("AddEmployeeDeductWorkingAge",e)},updateEmployeeDeductWorkingAge:function(e){return a.post("UpdateEmployeeDeductWorkingAge",e)},deleteEmployeeDeductWorkingAge:function(e){return a.post("DeleteEmployeeDeductWorkingAge",e)},queryIncentType:function(){return a.get("QueryIncentType")},queryIncentLevel:function(){return a.get("QueryIncentLevel")},queryEmployeeIncentive:function(e){return a.get("QueryEmployeeIncentive",e)},addEmployeeIncentive:function(e){return a.post("AddEmployeeIncentive",e)},updateEmployeeIncentive:function(e){return a.post("UpdateEmployeeIncentive",e)},deleteEmployeeIncentive:function(e){return a.post("DeleteEmployeeIncentive",e)},getEmployeeHealth:function(e){return a.get("GetEmployeeHealth",e)},updateEmployeeHealth:function(e){return a.post("UpdateEmployeeHealth",e)},queryAccidentType:function(){return a.get("QueryAccidentType")},queryEmployeeAccident:function(e){return a.get("QueryEmployeeAccident",e)},addEmployeeAccident:function(e){return a.post("AddEmployeeAccident",e)},updateEmployeeAccident:function(e){return a.post("UpdateEmployeeAccident",e)},deleteEmployeeAccident:function(e){return a.post("DeleteEmployeeAccident",e)},queryEmployeeTeach:function(e){return a.get("QueryEmployeeTeach",e)},addEmployeeTeach:function(e){return a.post("AddEmployeeTeach",e)},updateEmployeeTeach:function(e){return a.post("UpdateEmployeeTeach",e)},deleteEmployeeTeach:function(e){return a.post("DeleteEmployeeTeach",e)},queryEmployeeRelation:function(e){return a.get("QueryEmployeeRelation",e)},addEmployeeRelation:function(e){return a.post("AddEmployeeRelation",e)},updateEmployeeRelation:function(e){return a.post("UpdateEmployeeRelation",e)},deleteEmployeeRelation:function(e){return a.post("DeleteEmployeeRelation",e)},queryIncomeType:function(){return a.get("QueryIncomeType")},queryEmployeeArticle:function(e){return a.get("QueryEmployeeArticle",e)},addEmployeeArticle:function(e){return a.post("AddEmployeeArticle",e)},updateEmployeeArticle:function(e){return a.post("UpdateEmployeeArticle",e)},deleteEmployeeArticle:function(e){return a.post("DeleteEmployeeArticle",e)},queryClassLevel:function(){return a.get("QueryClassLevel")},queryEmployeeClass:function(e){return a.get("QueryEmployeeClass",e)},addEmployeeClass:function(e){return a.post("AddEmployeeClass",e)},updateEmployeeClass:function(e){return a.post("UpdateEmployeeClass",e)},deleteEmployeeClass:function(e){return a.post("DeleteEmployeeClass",e)},queryEmployeePatent:function(e){return a.get("QueryEmployeePatent",e)},addEmployeePatent:function(e){return a.post("AddEmployeePatent",e)},updateEmployeePatent:function(e){return a.post("UpdateEmployeePatent",e)},deleteEmployeePatent:function(e){return a.post("DeleteEmployeePatent",e)},queryTeacherType:function(){return a.get("QueryTeacherType")},queryEmployeeTeacher:function(e){return a.get("QueryEmployeeTeacher",e)},addEmployeeTeacher:function(e){return a.post("AddEmployeeTeacher",e)},updateEmployeeTeacher:function(e){return a.post("UpdateEmployeeTeacher",e)},deleteEmployeeTeacher:function(e){return a.post("DeleteEmployeeTeacher",e)},queryAwardLevel:function(){return a.get("QueryAwardLevel")},queryDictByParentCode:function(e){return a.get("QueryDictByParentCode",e)},queryHighTalent:function(){return a.get("QueryHighTalent")},queryEmployeeAward:function(e){return a.get("QueryEmployeeAward",e)},addEmployeeAward:function(e){return a.post("AddEmployeeAward",e)},updateEmployeeAward:function(e){return a.post("UpdateEmployeeAward",e)},deleteEmployeeAward:function(e){return a.post("DeleteEmployeeAward",e)},addEmployeeHighTalent:function(e){return a.post("AddEmployeeHighTalent",e)},updateEmployeeHighTalent:function(e){return a.post("UpdateEmployeeHighTalent",e)},queryEmployeeHighTalent:function(e){return a.get("QueryEmployeeHighTalent",e)},deleteEmployeeHighTalent:function(e){return a.post("DeleteEmployeeHighTalent",e)},queryEmployeeDeptHistory:function(e){return a.get("QueryEmployeeDeptHistory",e)},updateEmployeeDept:function(e){return a.post("UpdateEmployeeDept",e)},batchUpdateEmployeeDept:function(e){return a.post("BatchUpdateEmployeeDept",e)},queryEmployeeList:function(e){return a.post("QueryEmployeeList",e)},queryEmployeeListTree:function(e){return a.get("QueryEmployeeListTree",e)},queryDictsSetting:function(e){return a.get("QueryDictsSetting",e)},queryEmployeeListSettingTree:function(e){return a.get("QueryEmployeeListSettingTree",e)},queryAdvancedQueryType:function(){return a.get("QueryAdvancedQueryType")},saveEmployeeList:function(e){return a.post("SaveEmployeeList",e)},deleteEmployeeList:function(e){return a.post("DeleteEmployeeList",e)},getEmployeeList:function(e){return a.get("GetEmployeeList",e)},getEmployeeListExcel:function(e){return a.getFile("GetEmployeeListExcel",e)},queryDeptPrincipal:function(e){return a.get("QueryDeptPrincipal",e)},queryOtherEmployeeInfo:function(e){return a.post("QueryOtherEmployeeInfo",e)},saveOtherEmployeeInfo:function(e){return a.post("SaveOtherEmployeeInfo",e)},deleteOtherEmployeeInfo:function(e){return a.post("DeleteOtherEmployeeInfo",e)},queryOtherEmpTypes:function(e){return a.get("QueryOtherEmpTypes",e)},importExcel:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),a.postForm("ImportExcel",n)},downlodaImportExcelTemplate:function(e){return a.getFile("DownlodaImportExcelTemplate",e)},getSocialSecurityInfo:function(e){return a.get("getSocialSecurityInfo",e)},updateEmployeeSocialSecurity:function(e){return a.post("updateEmployeeSocialSecurity",e)},queryPayRollOrgClass:function(){return a.get("QueryPayRollOrgClass")},queryPayRollCompGroup:function(){return a.get("QueryPayRollCompGroup")},queryPayRollOrgSalary:function(e){return a.get("QueryPayRollOrgSalary",e)},queryPayRollOrgSalaryLevel:function(e){return a.get("QueryPayRollOrgSalaryLevel",e)},queryPayRollOrgPositionSalarys:function(e){return a.get("QueryPayRollOrgPositionSalarys",e)},queryWorkState:function(){return a.get("QueryWorkState")},getWagesInfo:function(e){return a.get("GetWagesInfo",e)},updateEmployeeBenefit:function(e){return a.post("UpdateEmployeeBenefit",e)},queryWagesHistory:function(e){return a.get("QueryWagesHistory",e)},getEmployeePayrollPrint:function(e){return a.get("GetEmployeePayrollPrint",e)},updateEmployeePayrollPrint:function(e){return a.post("UpdateEmployeePayrollPrint",e)},deleteEmployeePayrollPrint:function(e){return a.post("DeleteEmployeePayrollPrint",e)},getPrintDetailsInfo:function(e){return a.get("GetPrintDetailsInfo",e)},getEmployeeWage:function(e){return a.get("GetEmployeeWage",e)},editEmployeeWage:function(e){return a.post("EditEmployeeWage",e)},editEmployeeParty:function(e){return a.post("EditEmployeeParty",e)},queryEmployeeHRAffiliatedBranch:function(e){return a.get("QueryEmployeeHRAffiliatedBranch",e)},getEmployeeHRAffiliatedBranch:function(e){return a.get("GetEmployeeHRAffiliatedBranch",e)},addEmployeeHRAffiliatedBranch:function(e){return a.post("AddEmployeeHRAffiliatedBranch",e)},updateEmployeeHRAffiliatedBranch:function(e){return a.post("UpdateEmployeeHRAffiliatedBranch",e)},deleteEmployeeHRAffiliatedBranch:function(e){return a.post("DeleteEmployeeHRAffiliatedBranch",e)},queryEmployeeHRDict:function(e){return a.get("QueryEmployeeHRDict",e)},getEmployeeHRDict:function(e){return a.get("GetEmployeeHRDict",e)},addEmployeeHRDict:function(e){return a.post("AddEmployeeHRDict",e)},updateEmployeeHRDict:function(e){return a.post("UpdateEmployeeHRDict",e)},deleteEmployeeHRDict:function(e){return a.post("DeleteEmployeeHRDict",e)},queryEmployeeHRPartyMemberHonor:function(e){return a.get("QueryEmployeeHRPartyMemberHonor",e)},getEmployeeHRPartyMemberHonor:function(e){return a.get("GetEmployeeHRPartyMemberHonor",e)},addEmployeeHRPartyMemberHonor:function(e){return a.post("AddEmployeeHRPartyMemberHonor",e)},updateEmployeeHRPartyMemberHonor:function(e){return a.post("UpdateEmployeeHRPartyMemberHonor",e)},deleteEmployeeHRPartyMemberHonor:function(e){return a.post("DeleteEmployeeHRPartyMemberHonor",e)}}},f9ac:function(e,t,n){"use strict";var r=n("cfe3"),o="SysManage",a=new r["a"](o);t["a"]={queryDict:function(e){return a.get("QueryDict",e)},queryDictType:function(e){return a.post("QueryDictType",e)},addDict:function(e){return a.post("AddDict",e)},deleteDict:function(e){return a.post("DeleteDict",e)},updateDict:function(e){return a.post("UpdateDict",e)},getDict:function(e){return a.get("GetDict",e)},querySysSetting:function(e){return a.get("QuerySysSetting",e)},addSysSetting:function(e){return a.post("AddSysSetting",e)},deleteSysSetting:function(e){return a.post("DeleteSysSetting",e)},updateSysSetting:function(e){return a.post("UpdateSysSetting",e)},getSysSetting:function(e){return a.get("GetSysSetting",e)},queryLanguage:function(e){return a.get("QueryLanguage",e)},getEnumInfos:function(e){return a.get("GetEnumInfos",e)},queryUserGroups:function(e){return a.post("QueryUserGroups",e)},saveUserGroup:function(e){return a.post("SaveUserGroup",e)},deleteUserGroup:function(e){return a.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return a.get("DropdownUserGroups",e)},queryUsers:function(e){return a.post("QueryUsers",e)},saveUser:function(e){return a.post("SaveUser",e)},deleteUser:function(e){return a.post("DeleteUser",e)},initPwd:function(e){return a.post("InitPwd",e)},getUserById:function(e){return a.get("GetUserById",e)},queryEmployees:function(e){return a.post("QueryEmployees",e)},queryModuleInfos:function(e){return a.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return a.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return a.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return a.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return a.post("SaveRightOfDept",e)},queryControlRight:function(e){return a.post("QueryControlRight",e)},saveControlRights:function(e){return a.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return a.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return a.get("QueryStationTree",e)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(e){return a.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return a.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)}}}}]);