(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7cd31563"],{"06c5":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));n("a630"),n("fb6a"),n("b0c0"),n("d3b7"),n("25f0"),n("3ca3");var a=n("6b75");function r(t,e){if(t){if("string"===typeof t)return Object(a["a"])(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(a["a"])(t,e):void 0}}},3835:function(t,e,n){"use strict";function a(t){if(Array.isArray(t))return t}n.d(e,"a",(function(){return l}));n("a4d3"),n("e01a"),n("d28b"),n("d3b7"),n("3ca3"),n("ddb0");function r(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var n=[],a=!0,r=!1,o=void 0;try{for(var i,l=t[Symbol.iterator]();!(a=(i=l.next()).done);a=!0)if(n.push(i.value),e&&n.length===e)break}catch(c){r=!0,o=c}finally{try{a||null==l["return"]||l["return"]()}finally{if(r)throw o}}return n}}var o=n("06c5");function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){return a(t)||r(t,e)||Object(o["a"])(t,e)||i()}},"46f4":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[n("c-tree",{attrs:{options:t.treeData,props:t.treeProps,"expanded-keys":t.treeExpandedKeys},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{staticClass:"input_class",attrs:{label:"月份"}},[n("el-date-picker",{attrs:{type:"month",placeholder:"请选择申领月份","value-format":"yyyy-MM"},on:{change:t.dateChange},model:{value:t.recordMonth,callback:function(e){t.recordMonth=e},expression:"recordMonth"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"本月天数："}},[n("span",[t._v(t._s(t.monthTotalDay))])])],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"备注"}},[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:t.headModel.remark,callback:function(e){t.$set(t.headModel,"remark",e)},expression:"headModel.remark"}}):n("span",[t._v(t._s(t.headModel.remark))])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"状态"}},[t._v(" "+t._s(t.headModel.enumStatusDesc)+" ")])],1)],1),n("el-row",[0==t.headModel.enumStatus?n("el-col",{attrs:{span:24}},[n("el-button",{attrs:{type:"primary"},on:{click:t.submitRecord}},[t._v("提交")])],1):t._e()],1),n("el-row",[2==t.headModel.enumStatus?n("el-col",{staticClass:"diffColor",attrs:{span:24}},[t._v(" 唯一码、工号橙色员工为防保科与考勤员提交数据不一致，正在等待人事确认。 ")]):t._e()],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"},on:{"sort-change":t.sortChange}},[n("el-table-column",{attrs:{prop:"Uid",label:"唯一码",align:"center",width:"85",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",{class:a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?"diffColor":""},[t._v(t._s(a.empUid))])]}}])}),n("el-table-column",{attrs:{prop:"EmpCode",label:"工号",align:"center",width:"80",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",{class:a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?"diffColor":""},[t._v(t._s(a.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"DisplayName",label:"姓名",align:"center",width:"80",sortable:"custom",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.empName))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.hireStyleName))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"应发公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(0===a.generalHoliday?"":a.generalHoliday))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"剩余公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.historyH12=Math.abs(a.historyH12)}},model:{value:a.historyH12,callback:function(e){t.$set(a,"historyH12",t._n(e))},expression:"row.historyH12"}}):n("span",[t._v(t._s(0===a.historyH12?"":a.historyH12))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"公休",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h12=Math.abs(a.h12)}},model:{value:a.h12,callback:function(e){t.$set(a,"h12",t._n(e))},expression:"row.h12"}}):n("span",[t._v(t._s(a.h12))])]}}])}),t._v(" --\x3e "),n("el-table-column",{attrs:{prop:"name",label:"上月卫贴标准",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.preMonthH1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"本月卫贴标准",align:"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h1=Math.abs(a.h1)}},model:{value:a.h1,callback:function(e){t.$set(a,"h1",t._n(e))},expression:"row.h1"}}):n("span",[t._v(t._s(a.h1))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"病假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h2=Math.abs(a.h2)}},model:{value:a.h2,callback:function(e){t.$set(a,"h2",t._n(e))},expression:"row.h2"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h2)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h2))]):t._e()]):n("span",[t._v(t._s(a.h2))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"事假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h3=Math.abs(a.h3)}},model:{value:a.h3,callback:function(e){t.$set(a,"h3",t._n(e))},expression:"row.h3"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h3)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h3))]):t._e()]):n("span",[t._v(t._s(a.h3))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"产假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h4=Math.abs(a.h4)}},model:{value:a.h4,callback:function(e){t.$set(a,"h4",t._n(e))},expression:"row.h4"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h4)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h4))]):t._e()]):n("span",[t._v(t._s(a.h4))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h5=Math.abs(a.h5)}},model:{value:a.h5,callback:function(e){t.$set(a,"h5",t._n(e))},expression:"row.h5"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h5)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h5))]):t._e()]):n("span",[t._v(t._s(a.h5))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h6=Math.abs(a.h6)}},model:{value:a.h6,callback:function(e){t.$set(a,"h6",t._n(e))},expression:"row.h6"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h6)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h6))]):t._e()]):n("span",[t._v(t._s(a.h6))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"计生假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h7=Math.abs(a.h7)}},model:{value:a.h7,callback:function(e){t.$set(a,"h7",t._n(e))},expression:"row.h7"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h7)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h7))]):t._e()]):n("span",[t._v(t._s(a.h7))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h8=Math.abs(a.h8)}},model:{value:a.h8,callback:function(e){t.$set(a,"h8",t._n(e))},expression:"row.h8"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h8)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h8))]):t._e()]):n("span",[t._v(t._s(a.h8))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h9=Math.abs(a.h9)}},model:{value:a.h9,callback:function(e){t.$set(a,"h9",t._n(e))},expression:"row.h9"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h9)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h9))]):t._e()]):n("span",[t._v(t._s(a.h9))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h10=Math.abs(a.h10)}},model:{value:a.h10,callback:function(e){t.$set(a,"h10",t._n(e))},expression:"row.h10"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h10)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h10))]):t._e()]):n("span",[t._v(t._s(a.h10))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"right","header-align":"center",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[0==t.headModel.enumStatus?n("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(t){return a.h11=Math.abs(a.h11)}},model:{value:a.h11,callback:function(e){t.$set(a,"h11",t._n(e))},expression:"row.h11"}}):a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.h11)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.h11))]):t._e()]):n("span",[t._v(t._s(a.h11))])]}}])}),n("el-table-column",{attrs:{prop:"name",label:"修改人",align:"right","header-align":"center","min-width":"140px"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.attDayOffRecordDetailStatus&&1==a.attDayOffRecordDetailStatus?n("span",[n("span",{staticClass:"checkColor"},[t._v("考勤员:"+t._s(a.updator)),n("br")]),a.prophylacticFilling?n("span",{staticClass:"prophylacticColor"},[t._v("防保科:"+t._s(a.prophylacticFilling.updator))]):t._e()]):n("span",[t._v(t._s(a.updator))])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},r=[],o=(n("99af"),n("d81d"),n("fb6a"),n("a9e3"),n("d3b7"),n("ac1f"),n("25f0"),n("4d90"),n("1276"),n("3835")),i=n("d368"),l=n("cbd2"),c={components:{},data:function(){return{total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,recordMonth:this.getNowTime(),monthTotalDay:0,headModel:{remark:"",statue:""},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,currentNode:null,tableData:[],allData:[],treeExpandedKeys:[],tjSucceed:!1}},created:function(){this.loadTree(),this.getMonthTotalDay()},methods:{getNowTime:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth();n=n.toString().padStart(2,"0"),"00"===n&&(e-=1,n="12");var a="".concat(e,"-").concat(n);return a},getMonthTotalDay:function(){if(this.recordMonth){var t=this.recordMonth.split("-")[0],e=this.recordMonth.split("-")[1],n=new Date(t,e,0);this.monthTotalDay=n.getDate()}},loadTree:function(){var t=this;this.treeLoading=!0,i["a"].queryDeptByUser({}).then((function(e){t.treeData=e.data,t.treeExpandedKeys.push(e.data[0].id)})).catch((function(t){console.log(t)})).finally((function(){t.treeLoading=!1})),this.resetCurrentNode()},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttDayOffRecord()},dateChange:function(){this.listQuery.pageIndex=1,this.getMonthTotalDay(),this.getAttDayOffRecord()},sortChange:function(t,e,n){this.listQuery.pageIndex=1;var a="";"descending"===t.order?a="desc":"ascending"===t.order&&(a="asc"),this.listQuery.order=a?t.prop+" "+a:"",this.getAttDayOffRecord()},getAttDayOffRecord:function(){var t=this;if(this.currentNode){var e={RecordMonth:this.recordMonth,DeptId:this.currentNode.id};l["a"].getAttDayOffRecord(e).then((function(e){e.succeed?(t.headModel=e.data,t.queryCheckRecordFilling(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}else this.$notice.message("请选择部门。","info")},queryCheckRecordFilling:function(t){var e=this,n={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordMonth,Order:this.listQuery.order};l["a"].queryCheckRecordFilling(n).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},submitRecord:function(){for(var t=0;t<this.allData.length;t++){var e=this.allData[t],n=(e.h2||0)+(e.h3||0)+(e.h4||0)+(e.h5||0)+(e.h6||0)+(e.h7||0)+(e.h8||0)+(e.h9||0)+(e.h10||0)+(e.h11||0),a=this.recordMonth+"-01",r=a.split("-").map(Number),i=Object(o["a"])(r,2),l=i[0],c=i[1],u=new Date(l,c,0),s=u.getDate();if(n>s)return this.$message.error("(".concat(e.empCode,")").concat(e.empName,"的月份数据超过当月最多天数，请检查。")),void(this.tjSucceed=!1);this.tjSucceed=!0}this.tjSucceed&&this.submint()},submint:function(){var t=this;this.recordMonth&&this.currentNode&&this.$confirm("本考勤员承诺，此数据已经过科主任确认！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.fillings=t.allData,l["a"].submitAttDayOffRecord(t.headModel).then((function(e){e.succeed?(t.getAttDayOffRecord(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(e){t.getAttDayOffRecord(),console.log(e)}))})),this.currentNode?this.recordMonth||this.$notice.message("请选择月份","warning"):this.$notice.message("请选择部门","warning")}}},u=c,s=(n("d9e5"),n("2877")),d=Object(s["a"])(u,a,r,!1,null,null,null);e["default"]=d.exports},"6b75":function(t,e,n){"use strict";function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=new Array(e);n<e;n++)a[n]=t[n];return a}n.d(e,"a",(function(){return a}))},cbce:function(t,e,n){},cbd2:function(t,e,n){"use strict";var a=n("cfe3"),r="AttendanceManage",o=new a["a"](r);e["a"]={getAttMonthShiftRecord:function(t){return o.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return o.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return o.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return o.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return o.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return o.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return o.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return o.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return o.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return o.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return o.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return o.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return o.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return o.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return o.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return o.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return o.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return o.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return o.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return o.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return o.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return o.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return o.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return o.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return o.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return o.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return o.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return o.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return o.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return o.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return o.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return o.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return o.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return o.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return o.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return o.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return o.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return o.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return o.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return o.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return o.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return o.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return o.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return o.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return o.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return o.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return o.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return o.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return o.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return o.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return o.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return o.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return o.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return o.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return o.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return o.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return o.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return o.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return o.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return o.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return o.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},d368:function(t,e,n){"use strict";var a=n("cfe3"),r="Organization",o=new a["a"](r);e["a"]={QueryOrganizationHiddenTop:function(t){return o.get("QueryOrganizationHiddenTop",t)},QueryOrganization:function(t){return o.get("QueryOrganization",t)},QueryDepartment:function(t){return o.get("QueryDepartment",t)},GetDepartment:function(t){return o.get("GetDepartment",t)},AddDepartment:function(t){return o.post("AddDepartment",t)},UpdateDepartment:function(t){return o.post("UpdateDepartment",t)},MoveDepartment:function(t){return o.post("MoveDepartment",t)},MergeDepartment:function(t){return o.post("MergeDepartment",t)},DeleteDepartment:function(t){return o.post("DeleteDepartment",t)},queryPosition:function(t){return o.post("QueryPosition",t)},getPosition:function(t){return o.get("GetPosition",t)},addPosition:function(t){return o.post("AddPosition",t)},updatePosition:function(t){return o.post("UpdatePosition",t)},deletePosition:function(t){return o.post("DeletePosition",t)},GetStation:function(t){return o.get("GetStation",t)},AddStation:function(t){return o.post("AddStation",t)},UpdateStation:function(t){return o.post("UpdateStation",t)},DeleteStation:function(t){return o.post("DeleteStation",t)},QueryPositionStationTree:function(t){return o.get("QueryPositionStationTree",t)},AllocatePosition:function(t){return o.post("AllocatePosition",t)},DeletePositionStation:function(t){return o.post("DeletePositionStation",t)},queryDeptByUser:function(t){return o.get("QueryDeptByUser",t)},queryOneLevelStation:function(){return o.get("QueryOneLevelStation")},queryTwoLevelStation:function(t){return o.get("QueryTwoLevelStation",t)},querySenioritySelect:function(){return o.get("QuerySenioritySelect")},queryStationAllowance:function(t){return o.get("QueryStationAllowance",t)},downloadStationAllowanceTemplate:function(t){return o.post("DownloadStationAllowanceTemplate",{data:t,responseType:"arraybuffer"})},importStationAllowance:function(t,e){return e.append("file",t),o.postForm("ImportStationAllowance",e)},exportStationAllowance:function(t){return o.post("ExportStationAllowance",{data:t,responseType:"arraybuffer"})},getStationAllowance:function(t){return o.get("GetStationAllowance",t)},addStationAllowance:function(t){return o.post("AddStationAllowance",t)},updateStationAllowance:function(t){return o.post("UpdateStationAllowance",t)},querySeniority:function(t){return o.get("QuerySeniority",t)},importSeniority:function(t,e){return e.append("file",t),o.postForm("ImportSeniority",e)},downloadSeniorityTemplate:function(t){return o.post("DownloadSeniorityTemplate",{data:t,responseType:"arraybuffer"})},exportSeniority:function(t){return o.post("ExportSeniority",{data:t,responseType:"arraybuffer"})},getSeniority:function(t){return o.get("GetSeniority",t)},addSeniority:function(t){return o.post("AddSeniority",t)},updateSeniority:function(t){return o.post("UpdateSeniority",t)},querySalaryScale:function(t){return o.get("QuerySalaryScale",t)},getSalaryScale:function(t){return o.get("GetSalaryScale",t)},addSalaryScale:function(t){return o.post("AddSalaryScale",t)},updateSalaryScale:function(t){return o.post("UpdateSalaryScale",t)},deleteSalaryScale:function(t){return o.post("DeleteSalaryScale",t)},importSalaryScale:function(t,e){return e.append("file",t),o.postForm("ImportSalaryScale",e)},exportSalaryScale:function(t){return o.post("ExportSalaryScale",{data:t,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(t){return o.get("QuerySalaryScaleByStationId",t)},queryStationAllowanceByStationId:function(t){return o.get("QueryStationAllowanceByStationId",t)},queryCarSubsidy:function(t){return o.get("QueryCarSubsidy",t)},getCarSubsidy:function(t){return o.get("GetCarSubsidy",t)},addCarSubsidy:function(t){return o.post("AddCarSubsidy",t)},updateCarSubsidy:function(t){return o.post("UpdateCarSubsidy",t)},deleteCarSubsidy:function(t){return o.post("DeleteCarSubsidy",t)},queryTelephoneFee:function(t){return o.get("QueryTelephoneFee",t)},getTelephoneFee:function(t){return o.get("GetTelephoneFee",t)},addTelephoneFee:function(t){return o.post("AddTelephoneFee",t)},updateTelephoneFee:function(t){return o.post("UpdateTelephoneFee",t)},deleteTelephoneFee:function(t){return o.post("DeleteTelephoneFee",t)}}},d9e5:function(t,e,n){"use strict";var a=n("cbce"),r=n.n(a);r.a}}]);