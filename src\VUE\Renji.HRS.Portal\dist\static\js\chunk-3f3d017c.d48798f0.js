(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f3d017c"],{"6cc2":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:11,type:"flex"}},[a("el-col",{attrs:{span:5}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"参数"},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}})],1),a("el-col",{attrs:{span:8}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"CreateTime",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{sortable:"custom",prop:"Name",label:"参数"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.name))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"Value",label:"值","header-align":"left",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(0==n.value||null==n.value||void 0==n.value||0==n.enumSalaryDataType||null==n.enumSalaryDataType||void 0==n.enumSalaryDataType?"-":n.value+" "+n.enumSalaryDataTypeDesc.slice(2)))])]}}])}),a("el-table-column",{attrs:{sortable:"custom",prop:"Remark",label:"备注"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.remark))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.showDialog(n)}}},[e._v(" 编辑 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),a("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}})],1)},r=[],o=a("2efc"),i=a("f9ac"),l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"参数名",prop:"name"}},[a("span",[e._v(e._s(e.dataModel.name))])])],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"类型",prop:"enumSalaryDataType"}},[a("span",[e._v(e._s(e.dataModel.enumSalaryDataTypeDesc))])])],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"值",prop:"value"}},[a("el-input",{attrs:{type:"textarea",rows:1,maxlength:"500",placeholder:"值"},model:{value:e.dataModel.value,callback:function(t){e.$set(e.dataModel,"value",t)},expression:"dataModel.value"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:3,maxlength:"100",placeholder:"备注"},model:{value:e.dataModel.remark,callback:function(t){e.$set(e.dataModel,"remark",t)},expression:"dataModel.remark"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},s=[],u={data:function(){var e=function(e,t,a){if(t){var n=/^(\d|[1-9]\d+)(\.\d{1,2})?$/;n.test(t)?a():a(new Error("请输入正确格式数字,小数不超过2位"))}else a()};return{showDialog:!1,title:"",rules:{value:[{required:!0,message:"值不能为空"},{validator:e,trigger:"blur"}]},btnSaveLoading:!1,dataModel:{},salaryDataTypeList:[],salaryDataPayTypeList:[]}},methods:{initDialog:function(e){this.title="编辑基础参数",this.getData(e.id),this.showDialog=!0},getData:function(e){var t=this;o["a"].getSalaryData({id:e}).then((function(e){e.succeed&&(t.dataModel=e.data)})).catch((function(e){console.log(e)}))},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.btnSaveLoading=!0,o["a"].updateSalaryData(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"修改成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()},initSalaryDataTypeList:function(){var e=this,t={enumType:"SalaryDataType"};i["a"].getEnumInfos(t).then((function(t){e.salaryDataTypeList=t.data.datas})).catch((function(e){console.log(e)}))}}},c=u,d=a("2877"),p=Object(d["a"])(c,l,s,!1,null,null,null),g=p.exports,y={components:{editDialog:g},data:function(){return{addForm:{},dataList:[],salaryDataPayTypeList:[],total:0,listQuery:{pageIndex:1,pageSize:10,order:"+CreateTime",enumSalaryDataPayType:null},listLoading:!1}},created:function(){this.loadSalaryDataType(),this.getPageList()},methods:{getPageList:function(){var e=this;this.listLoading=!0,o["a"].querySalaryData(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},loadSalaryDataType:function(){var e=this;i["a"].getEnumInfos({enumType:"SalaryDataType"}).then((function(t){e.salaryDataTypeList=t.data.datas})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var n="";"descending"===e.order&&(n="-"),"ascending"===e.order&&(n="+"),this.listQuery.order=n+e.prop,this.getPageList()},showDialog:function(e){this.$refs.editDialog.initDialog(e)}}},f=y,h=Object(d["a"])(f,n,r,!1,null,null,null);t["default"]=h.exports},f9ac:function(e,t,a){"use strict";var n=a("cfe3"),r="SysManage",o=new n["a"](r);t["a"]={queryDict:function(e){return o.get("QueryDict",e)},queryDictType:function(e){return o.post("QueryDictType",e)},addDict:function(e){return o.post("AddDict",e)},deleteDict:function(e){return o.post("DeleteDict",e)},updateDict:function(e){return o.post("UpdateDict",e)},getDict:function(e){return o.get("GetDict",e)},querySysSetting:function(e){return o.get("QuerySysSetting",e)},addSysSetting:function(e){return o.post("AddSysSetting",e)},deleteSysSetting:function(e){return o.post("DeleteSysSetting",e)},updateSysSetting:function(e){return o.post("UpdateSysSetting",e)},getSysSetting:function(e){return o.get("GetSysSetting",e)},queryLanguage:function(e){return o.get("QueryLanguage",e)},getEnumInfos:function(e){return o.get("GetEnumInfos",e)},queryUserGroups:function(e){return o.post("QueryUserGroups",e)},saveUserGroup:function(e){return o.post("SaveUserGroup",e)},deleteUserGroup:function(e){return o.post("DeleteUserGroup",e)},dropdownUserGroups:function(e){return o.get("DropdownUserGroups",e)},queryUsers:function(e){return o.post("QueryUsers",e)},saveUser:function(e){return o.post("SaveUser",e)},deleteUser:function(e){return o.post("DeleteUser",e)},initPwd:function(e){return o.post("InitPwd",e)},getUserById:function(e){return o.get("GetUserById",e)},queryEmployees:function(e){return o.post("QueryEmployees",e)},queryModuleInfos:function(e){return o.get("QueryModuleInfos",e)},getRightSettingByUserGroup:function(e){return o.get("GetRightSettingByUserGroup",e)},saveRightSetting:function(e){return o.post("SaveRightSetting",e)},getRightOfDeptByUserGroup:function(e){return o.get("GetRightOfDeptByUserGroup",e)},saveRightOfDept:function(e){return o.post("SaveRightOfDept",e)},queryControlRight:function(e){return o.post("QueryControlRight",e)},saveControlRights:function(e){return o.post("SaveControlRights",e)},getControlRightByCurrentUser:function(e){return o.get("GetControlRightByCurrentUser",e)},queryStationTree:function(e){return o.get("QueryStationTree",e)},queryStationTypeSelector:function(){return o.get("QueryStationTypeSelector")},queryStationSelector:function(e){return o.get("QueryStationSelector",e)},querySalaryScaleSelector:function(e){return o.get("QuerySalaryScaleSelector",e)},queryTelephoneFeeSelector:function(){return o.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return o.get("QueryCarSubsidySelector")},queryStationAllowance:function(e){return o.get("QueryStationAllowance",e)}}}}]);