(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d41593c6"],{3836:function(e,t,a){"use strict";var i=a("87dc"),r=a.n(i);r.a},"80ad":function(e,t,a){},"87dc":function(e,t,a){},aec7:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[a("el-row",{staticClass:"filter-container",attrs:{gutter:10,type:"flex"}},[a("el-col",{attrs:{span:4}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month",placeholder:"请选择月份",editable:!1,clearable:!1,"value-format":"yyyy-MM"},on:{change:function(t){return e.monthChange()}},model:{value:e.listQuery.recordMonth,callback:function(t){e.$set(e.listQuery,"recordMonth",t)},expression:"listQuery.recordMonth"}})],1),a("el-col",{attrs:{span:4}},[a("c-select-tree",{attrs:{options:e.treeData,selectplaceholder:"请选择部门",placeholder:"请输入关键字","tree-props":e.treeProps},model:{value:e.listQuery.deptId,callback:function(t){e.$set(e.listQuery,"deptId",t)},expression:"listQuery.deptId"}})],1),a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{clearable:"",placeholder:"工号"},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{clearable:"",placeholder:"姓名"},model:{value:e.listQuery.empName,callback:function(t){e.$set(e.listQuery,"empName",t)},expression:"listQuery.empName"}})],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v(" 查询 ")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.exportData}},[e._v(" 导出 ")])],1)],1),a("el-table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.list,stripe:"",border:"",fit:"","highlight-current-row":"","default-sort":{prop:"EmpCode",order:"descending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":e.handleRowClass},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{prop:"EmpUid",label:"唯一码",align:"center",width:"85",sortable:"custom",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.empUid))])]}}])}),a("el-table-column",{attrs:{prop:"EmpCode",label:"工号","header-align":"center",align:"center",width:"70px",sortable:"custom",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.empCode))])]}}])}),a("el-table-column",{attrs:{prop:"EmpName",label:"姓名","header-align":"center",align:"left","min-width":"80px",sortable:"custom",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.empName))])]}}])}),a("el-table-column",{attrs:{prop:"GenderDesc",label:"性别","header-align":"center",align:"left","min-width":"70px",sortable:"custom",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.genderDesc))])]}}])}),a("el-table-column",{attrs:{label:"部门","header-align":"center",align:"left","min-width":"130px"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.empDept))])]}}])}),a("el-table-column",{attrs:{label:"月份",prop:"recordMonth",sortable:"custom","header-align":"center",align:"center","min-width":"90px"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",[e._v(e._s(i.recordMonth?new Date(i.recordMonth).Format("yyyy-MM"):""))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"病假",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h2?i.attDayOffRecordDetailQuery.h2:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h2)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h2))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"事假",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h3?i.attDayOffRecordDetailQuery.h3:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h3)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h3))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"产假",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h4?i.attDayOffRecordDetailQuery.h4:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h4)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h4))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h5?i.attDayOffRecordDetailQuery.h5:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h5)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h5))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h6?i.attDayOffRecordDetailQuery.h6:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h6)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h6))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"计生假",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h7?i.attDayOffRecordDetailQuery.h7:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h7)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h7))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h8?i.attDayOffRecordDetailQuery.h8:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h8)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h8))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h9?i.attDayOffRecordDetailQuery.h9:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h9)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h9))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h10?i.attDayOffRecordDetailQuery.h10:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h10)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h10))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"center","header-align":"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[i.isDifference?a("span",{staticClass:"hrFillingColor"},[e._v("人事科:"+e._s(i.attDayOffRecordDetailQuery&&i.attDayOffRecordDetailQuery.h11?i.attDayOffRecordDetailQuery.h11:"")),a("br")]):e._e(),i.isDifference?a("span",{staticClass:"prophylacticFillingColor"},[e._v("防保科:"+e._s(i.h11)),a("br")]):e._e(),i.isDifference?e._e():a("span",[e._v(e._s(i.h11))])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getList}})]},proxy:!0}])}),a("el-dialog",{attrs:{"append-to-body":"",title:e.viewDialogTitle,"close-on-click-modal":!1,visible:e.dialogViewFormVisible,width:"80%"},on:{close:e.onHidden}},[a("viewAttDayOffRecordProphylacticDetail",{ref:"refAttDayOffRecordProphylacticDetail",attrs:{id:e.itemId,"show-dialog":e.dialogViewFormVisible}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{icon:"el-icon-close"},on:{click:e.onHidden}},[e._v(" 关闭 ")])],1)],1)],1)},r=[],l=(a("99af"),a("d3b7"),a("25f0"),a("3ca3"),a("4d90"),a("ddb0"),a("2b3d"),a("4cf0")),n=a("d368"),o=a("cbd2"),s=a("f9ac"),c={name:"ProphylacticChange",components:{viewAttDayOffRecordProphylacticDetail:l["a"]},data:function(){return{span:4,total:0,listQuery:{pageIndex:1,pageSize:10,order:"-EmpCode",recordMonth:this.getNowTime()},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},leaveTypeList:[],holidayTypeList:[],statusList:[],listLoading:!1,list:[],dialogEditFormVisible:!1,dialogViewFormVisible:!1,dialogStatus:"",textMap:{update:"编辑防保科考勤申报",create:"新增防保科考勤申报",view:"查看防保科考勤申报"},modifyDialogTitle:"",viewDialogTitle:"",itemId:null}},created:function(){this.loadTree(),this.initLeaveTypeList(),this.initHolidayTypeList(),this.initAttDayOffRecordProphylacticDetailStatusList(),this.getList()},computed:{},methods:{getNowTime:function(){var e=new Date;e.setMonth(e.getMonth()-1);var t=e.getFullYear(),a=e.getMonth();a=a.toString().padStart(2,"0"),"00"===a&&(t-=1,a="12");var i="".concat(t,"-").concat(a);return i},handleFilter:function(){this.listQuery.pageIndex=1,this.getList()},monthChange:function(){this.handleFilter()},getList:function(){var e=this;this.listLoading=!0,o["a"].queryProphylacticChange(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.list=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},loadTree:function(){var e=this;n["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data})).catch((function(e){console.log(e)}))},initLeaveTypeList:function(){var e=this,t={enumType:"LeaveType"};s["a"].getEnumInfos(t).then((function(t){e.leaveTypeList=t.data.datas})).catch((function(e){console.log(e)}))},initHolidayTypeList:function(){var e=this,t={enumType:"HolidayType"};s["a"].getEnumInfos(t).then((function(t){e.holidayTypeList=t.data.datas})).catch((function(e){console.log(e)}))},initAttDayOffRecordProphylacticDetailStatusList:function(){var e=this,t={enumType:"AttDayOffRecordProphylacticDetailStatus"};s["a"].getEnumInfos(t).then((function(t){e.statusList=t.data.datas})).catch((function(e){console.log(e)}))},handleRowClass:function(e,t){return e.rowIndex%2===0?"cellStyle":"stripedStyle"},sortChange:function(e,t,a){this.listQuery.pageIndex=1;var i="";"descending"===e.order&&(i="-"),"ascending"===e.order&&(i="+"),this.listQuery.order=i+e.prop,this.getList()},sizeChange:function(e){this.listQuery.pageSize=e,this.handleFilter()},handleView:function(e){this.itemId=e.id,this.dialogViewFormVisible=!0,this.dialogStatus="view",this.viewDialogTitle=this.textMap[this.dialogStatus]},exportData:function(){o["a"].exportProphylacticChange(this.listQuery).then((function(e){var t=new Blob([e],{type:e.type}),a="防保科考勤变动.xlsx",i=document.createElement("a"),r=window.URL.createObjectURL(t);i.href=r,i.download=a,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(r)}))},onHidden:function(){this.dialogEditFormVisible=!1,this.dialogViewFormVisible=!1},onRefresh:function(){this.getList(),this.dialogEditFormVisible=!1}}},d=c,f=(a("3836"),a("f23a"),a("2877")),u=Object(f["a"])(d,i,r,!1,null,null,null);t["default"]=u.exports},f23a:function(e,t,a){"use strict";var i=a("80ad"),r=a.n(i);r.a}}]);