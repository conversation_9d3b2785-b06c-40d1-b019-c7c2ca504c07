(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2038084b"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"19de":function(e,t){e.exports=function(e,t,n,o){var a="undefined"!==typeof o?[o,e]:[e],r=new Blob(a,{type:n||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(r,t);else{var i=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(r):window.webkitURL.createObjectURL(r),l=document.createElement("a");l.style.display="none",l.href=i,l.setAttribute("download",t),"undefined"===typeof l.download&&l.setAttribute("target","_blank"),document.body.appendChild(l),l.click(),setTimeout((function(){document.body.removeChild(l),window.URL.revokeObjectURL(i)}),200)}}},"841c":function(e,t,n){"use strict";var o=n("d784"),a=n("825a"),r=n("1d80"),i=n("129f"),l=n("14c3");o("search",1,(function(e,t,n){return[function(t){var n=r(this),o=void 0==t?void 0:t[e];return void 0!==o?o.call(t,n):new RegExp(t)[e](String(n))},function(e){var o=n(t,e,this);if(o.done)return o.value;var r=a(e),s=String(this),u=r.lastIndex;i(u,0)||(r.lastIndex=0);var c=l(r,s);return i(r.lastIndex,u)||(r.lastIndex=u),null===c?-1:c.index}]}))},b818:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-row",{staticClass:"filter-container",attrs:{gutter:10,type:"flex"}},[n("el-col",{attrs:{span:4}},[n("el-select",{attrs:{filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入工龄","remote-method":e.remoteMethod,clearable:""},on:{clear:e.clearWorkAge},model:{value:e.listQuery.workAge,callback:function(t){e.$set(e.listQuery,"workAge",t)},expression:"listQuery.workAge"}},e._l(e.workAgeOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1),n("el-col",{staticClass:"filter-button",attrs:{span:8}},[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.showDialog()}}},[e._v("添加")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.downloadexceltemplate}},[e._v("下载模板")]),n("el-upload",{staticStyle:{"margin-left":"10px"},attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[n("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload2",type:"primary"},slot:"trigger"},[e._v("导入")])],1),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"工龄",sortable:"custom",prop:"WorkAge"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.workAge))])]}}])}),n("el-table-column",{attrs:{label:"工作量津贴",sortable:"custom",prop:"WorkAllowance"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(e._f("formatMoney2")(o.workAllowance)))])]}}])}),n("el-table-column",{attrs:{label:"护龄工资",sortable:"custom",prop:"NursingAgeWage"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(e._f("formatMoney2")(o.nursingAgeWage)))])]}}])}),n("el-table-column",{attrs:{label:"备注","min-width":"150px ","header-align":"center",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",{staticStyle:{"white-space":"pre-wrap"}},[e._v(e._s(o.memo))])]}}])}),n("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("el-button",{staticStyle:{"margin-left":"45px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.showDialog(o)}}},[e._v(" 编辑 ")])]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),n("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}})],1)},a=[],r=(n("4de4"),n("c975"),n("ac1f"),n("841c"),n("d368")),i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[n("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:20}},[n("el-form-item",{attrs:{label:"工龄",prop:"workAge"}},[n("el-input",{attrs:{disabled:e.isEdit,placeholder:"工龄",clearable:""},model:{value:e.dataModel.workAge,callback:function(t){e.$set(e.dataModel,"workAge",t)},expression:"dataModel.workAge"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:20}},[n("el-form-item",{attrs:{label:"工作量津贴",prop:"workAllowance"}},[n("el-input",{attrs:{placeholder:"工作量津贴"},model:{value:e.dataModel.workAllowance,callback:function(t){e.$set(e.dataModel,"workAllowance",t)},expression:"dataModel.workAllowance"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:20}},[n("el-form-item",{attrs:{label:"护理工资",prop:"nursingAgeWage"}},[n("el-input",{attrs:{placeholder:"护理工资"},model:{value:e.dataModel.nursingAgeWage,callback:function(t){e.$set(e.dataModel,"nursingAgeWage",t)},expression:"dataModel.nursingAgeWage"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"备注"}},[n("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"备注"},model:{value:e.dataModel.memo,callback:function(t){e.$set(e.dataModel,"memo",t)},expression:"dataModel.memo"}})],1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},l=[],s={data:function(){var e=function(e,t,n){/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/.test(t)?n():n(new Error("请输入0以上的数字"))},t=function(e,t,n){/^[+]{0,1}(\d+)$|^[+]{0,1}$/.test(t)?n():n(new Error("请输入正整数"))};return{showDialog:!1,title:"",rules:{workAge:[{required:!0,message:"请输入工龄",trigger:"blur"},{validator:t,trigger:"blur"}],workAllowance:[{required:!0,message:"请输入工作量津贴",trigger:"blur"},{validator:e,trigger:"blur"}],nursingAgeWage:[{required:!0,message:"请输入护龄工资",trigger:"blur"},{validator:e,trigger:"blur"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{}}},methods:{initDialog:function(e){e?(this.title="编辑工龄津贴",this.isEdit=!0,this.getData(e.id)):(this.title="新增工龄津贴",this.isEdit=!1),this.showDialog=!0},getData:function(e){var t=this;r["a"].getSeniority({id:e}).then((function(e){e.succeed&&(t.dataModel=e.data)})).catch((function(e){}))},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.btnSaveLoading=!0,e.isEdit?r["a"].updateSeniority(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"修改成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})):r["a"].addSeniority(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"添加成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})))}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}},u=s,c=n("2877"),d=Object(c["a"])(u,i,l,!1,null,null,null),p=d.exports,g={components:{editDialog:p},data:function(){return{addForm:{},dataList:[],workAgeList:[],workAgeOptions:[],total:0,listQuery:{pageIndex:1,pageSize:10},listLoading:!1,temp:{}}},created:function(){this.getWorkAgeist(),this.getPageList()},methods:{search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e){this.listQuery.pageIndex=1;var t="";"descending"===e.order&&(t="-"),"ascending"===e.order&&(t="+"),this.listQuery.order=t+e.prop,this.getPageList()},importExcel:function(e){var t=this,n=e.file,o=new FormData;r["a"].importSeniority(n,o).then((function(e){e.succeed&&(t.$message({message:"导入成功",type:"success"}),t.search())})).catch((function(e){t.search()}))},exportExcel:function(){var e=this;r["a"].exportSeniority(this.listQuery).then((function(t){console.log(t);var o=n("19de"),a="工龄津贴"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?o(t.data,a):o(t,a)}))},downloadexceltemplate:function(){r["a"].downloadSeniorityTemplate().then((function(e){var t=n("19de"),o="SeniorityTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},getWorkAgeist:function(){var e=this;r["a"].querySenioritySelect().then((function(t){t.succeed?(e.workAgeList=t.data.datas,e.workAgeOptions=t.data.datas):e.$notice.resultTip(t)})).catch((function(e){console.log(e)}))},remoteMethod:function(e){this.workAgeOptions=""!==e?this.workAgeList.filter((function(t){return t.label.indexOf(e)>-1})):JSON.parse(JSON.stringify(this.workAgeList))},clearWorkAge:function(){this.workAgeOptions=JSON.parse(JSON.stringify(this.workAgeList))},getPageList:function(){var e=this;this.listLoading=!0,r["a"].querySeniority(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},showDialog:function(e){this.$refs.editDialog.initDialog(e)}}},f=g,y=Object(c["a"])(f,o,a,!1,null,null,null);t["default"]=y.exports},d368:function(e,t,n){"use strict";var o=n("cfe3"),a="Organization",r=new o["a"](a);t["a"]={QueryOrganizationHiddenTop:function(e){return r.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return r.get("QueryOrganization",e)},QueryDepartment:function(e){return r.get("QueryDepartment",e)},GetDepartment:function(e){return r.get("GetDepartment",e)},AddDepartment:function(e){return r.post("AddDepartment",e)},UpdateDepartment:function(e){return r.post("UpdateDepartment",e)},MoveDepartment:function(e){return r.post("MoveDepartment",e)},MergeDepartment:function(e){return r.post("MergeDepartment",e)},DeleteDepartment:function(e){return r.post("DeleteDepartment",e)},queryPosition:function(e){return r.post("QueryPosition",e)},getPosition:function(e){return r.get("GetPosition",e)},addPosition:function(e){return r.post("AddPosition",e)},updatePosition:function(e){return r.post("UpdatePosition",e)},deletePosition:function(e){return r.post("DeletePosition",e)},GetStation:function(e){return r.get("GetStation",e)},AddStation:function(e){return r.post("AddStation",e)},UpdateStation:function(e){return r.post("UpdateStation",e)},DeleteStation:function(e){return r.post("DeleteStation",e)},QueryPositionStationTree:function(e){return r.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return r.post("AllocatePosition",e)},DeletePositionStation:function(e){return r.post("DeletePositionStation",e)},queryDeptByUser:function(e){return r.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return r.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return r.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return r.get("QuerySenioritySelect")},queryStationAllowance:function(e){return r.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return r.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),r.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return r.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return r.get("GetStationAllowance",e)},addStationAllowance:function(e){return r.post("AddStationAllowance",e)},updateStationAllowance:function(e){return r.post("UpdateStationAllowance",e)},querySeniority:function(e){return r.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),r.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return r.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return r.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return r.get("GetSeniority",e)},addSeniority:function(e){return r.post("AddSeniority",e)},updateSeniority:function(e){return r.post("UpdateSeniority",e)},querySalaryScale:function(e){return r.get("QuerySalaryScale",e)},getSalaryScale:function(e){return r.get("GetSalaryScale",e)},addSalaryScale:function(e){return r.post("AddSalaryScale",e)},updateSalaryScale:function(e){return r.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return r.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),r.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return r.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return r.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return r.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return r.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return r.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return r.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return r.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return r.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return r.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return r.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return r.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return r.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return r.post("DeleteTelephoneFee",e)}}}}]);