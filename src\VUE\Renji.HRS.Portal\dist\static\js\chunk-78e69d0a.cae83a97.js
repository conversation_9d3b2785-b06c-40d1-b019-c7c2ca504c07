(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-78e69d0a"],{"0055":function(t,e,r){"use strict";var n=r("a1aa"),o=r.n(n);o.a},1122:function(t,e,r){},"4cf0":function(t,e,r){"use strict";var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("el-form",{ref:"dataForm",staticClass:"el-dialogform",attrs:{rules:t.rules,model:t.tempFormModel,"label-position":"right","label-width":"110px"}},[r("el-row",{attrs:{gutter:10}},[r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"员工姓名",prop:"empName"}},[t._v(" "+t._s(t.tempFormModel.empName)+" ")])],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"唯一码"}},[t._v(" "+t._s(t.tempFormModel.empUid)+" ")])],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"工号"}},[t._v(" "+t._s(t.tempFormModel.empCode)+" ")])],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"性别"}},[t._v(" "+t._s(t.tempFormModel.genderDesc)+" ")])],1)],1),r("el-row",{attrs:{gutter:10}},[r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"部门"}},[t._v(" "+t._s(t.tempFormModel.empDept)+" ")])],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"院区"}},[t._v(" "+t._s(t.tempFormModel.hospitalAreaNameText)+" ")])],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"身份证号"}},[t._v(" "+t._s(t.tempFormModel.identityNumber)+" ")])],1)],1),r("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[r("span",{staticStyle:{color:"blue"}},[t._v("基本信息")])]),r("el-row",{attrs:{gutter:10}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"请假类别",prop:"enumLeaveType"}},[t._v(" "+t._s(t.tempFormModel.enumLeaveTypeDesc)+" ")])],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"就诊医院",prop:"visitingHospital"}},[t._v(" "+t._s(t.tempFormModel.visitingHospital)+" ")])],1)],1),r("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"就诊科室",prop:"visitingDepartment"}},[t._v(" "+t._s(t.tempFormModel.visitingDepartment)+" ")])],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"就诊医师",prop:"visitingPhysician"}},[t._v(" "+t._s(t.tempFormModel.visitingPhysician)+" ")])],1)],1),r("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"诊断意见",prop:"diagnostiOpinion"}},[t._v(" "+t._s(t.tempFormModel.diagnostiOpinion)+" ")])],1)],1),r("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[r("span",{staticStyle:{color:"blue"}},[t._v("防保科建议")])]),r("el-row",{attrs:{gutter:10}},[r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"假期类型",prop:"enumHolidayType"}},[t._v(" "+t._s(t.tempFormModel.enumHolidayTypeDesc)+" ")])],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"开具时间",prop:"issuingTime"}},[r("span",[t._v(t._s(t.tempFormModel.issuingTime?new Date(t.tempFormModel.issuingTime).Format("yyyy-MM-dd"):""))])])],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"休假开始日期",prop:"leaveStartDate"}},[r("span",[t._v(t._s(t.tempFormModel.leaveStartDate?new Date(t.tempFormModel.leaveStartDate).Format("yyyy-MM-dd"):""))])])],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"休假结束日期",prop:"leaveEndDate"}},[r("span",[t._v(t._s(t.tempFormModel.leaveEndDate?new Date(t.tempFormModel.leaveEndDate).Format("yyyy-MM-dd"):""))])])],1)],1),r("el-row",{attrs:{gutter:10}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"假期类型备注",prop:"holidayRemark",rules:3==t.tempFormModel.enumHolidayType?t.rules.holidayRemark:[{required:!1,message:"假期类型备注必填",trigger:"blur"}]}},[t._v(" "+t._s(t.tempFormModel.holidayRemark)+" ")])],1)],1),r("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[r("span",{staticStyle:{color:"blue"}},[t._v("休假详情")])]),r("el-row",{attrs:{gutter:10}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:t.tempFormModel.detail,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":t.handleRowClass}},[r("el-table-column",{attrs:{label:"月份",prop:"recordMonth","header-align":"center",align:"center","min-width":"90px"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.recordMonth?new Date(n.recordMonth).Format("yyyy-MM"):""))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"病假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h2)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"事假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h3)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"产假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h4)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h5)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h6)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"计生假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h7)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h8)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h9)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h10)+" ")]}}])}),r("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"right","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.h11)+" ")]}}])}),r("el-table-column",{key:50000100,attrs:{prop:"name",label:"状态",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.enumStatusDesc))])]}}])})],1)],1)],1)],1)},o=[],a=r("cbd2"),i=(r("f9ac"),{computed:{},name:"detailAttDayOffRecordProphylacticDetail",components:{},props:{showDialog:{type:Boolean,default:!1},id:{type:String,default:""},title:{type:String,default:""}},data:function(){return{span:12,rules:{},tempFormModel:{detail:[],id:"",employeeId:"",empUid:"",empCode:"",empName:"",genderDes:"",empDept:"",identityNumber:""},btnSaveLoading:!1,listLoading:!1}},watch:{id:function(t){this.tempFormModel.id=t},showDialog:{immediate:!0,handler:function(t){!0===t&&this.get()}}},mounted:function(){},created:function(){},methods:{get:function(){var t=this;this.btnSaveLoading=!0,a["a"].getAttDayOffRecordProphylacticCase({id:this.id}).then((function(e){t.tempFormModel=e.data,t.btnSaveLoading=!1})).catch((function(e){t.btnSaveLoading=!1}))},clear:function(){this.$refs["dataForm"]&&this.$refs["dataForm"].resetFields(),this.tempFormModel={detail:[],id:this.id,employeeId:"",empUid:"",empCode:"",empName:"",genderDes:"",empDept:"",identityNumber:""}},close:function(){this.clear(),this.$emit("refresh")},cancle:function(){this.clear(),this.$emit("hidden")},handleRowClass:function(t,e){return t.rowIndex%2===0?"cellStyle":"stripedStyle"}}}),l=i,u=(r("0055"),r("879d"),r("2877")),c=Object(u["a"])(l,n,o,!1,null,"0078dc0d",null);e["a"]=c.exports},"879d":function(t,e,r){"use strict";var n=r("1122"),o=r.n(n);o.a},a1aa:function(t,e,r){},cbd2:function(t,e,r){"use strict";var n=r("cfe3"),o="AttendanceManage",a=new n["a"](o);e["a"]={getAttMonthShiftRecord:function(t){return a.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return a.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return a.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return a.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return a.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return a.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return a.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return a.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return a.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return a.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return a.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return a.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return a.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return a.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return a.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return a.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return a.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return a.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return a.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return a.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return a.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return a.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return a.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return a.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return a.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return a.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return a.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return a.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return a.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return a.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return a.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return a.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return a.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return a.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return a.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return a.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return a.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return a.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return a.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return a.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return a.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return a.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return a.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return a.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return a.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return a.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return a.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return a.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return a.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return a.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return a.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return a.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return a.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return a.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return a.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return a.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return a.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return a.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return a.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return a.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return a.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},d368:function(t,e,r){"use strict";var n=r("cfe3"),o="Organization",a=new n["a"](o);e["a"]={QueryOrganizationHiddenTop:function(t){return a.get("QueryOrganizationHiddenTop",t)},QueryOrganization:function(t){return a.get("QueryOrganization",t)},QueryDepartment:function(t){return a.get("QueryDepartment",t)},GetDepartment:function(t){return a.get("GetDepartment",t)},AddDepartment:function(t){return a.post("AddDepartment",t)},UpdateDepartment:function(t){return a.post("UpdateDepartment",t)},MoveDepartment:function(t){return a.post("MoveDepartment",t)},MergeDepartment:function(t){return a.post("MergeDepartment",t)},DeleteDepartment:function(t){return a.post("DeleteDepartment",t)},queryPosition:function(t){return a.post("QueryPosition",t)},getPosition:function(t){return a.get("GetPosition",t)},addPosition:function(t){return a.post("AddPosition",t)},updatePosition:function(t){return a.post("UpdatePosition",t)},deletePosition:function(t){return a.post("DeletePosition",t)},GetStation:function(t){return a.get("GetStation",t)},AddStation:function(t){return a.post("AddStation",t)},UpdateStation:function(t){return a.post("UpdateStation",t)},DeleteStation:function(t){return a.post("DeleteStation",t)},QueryPositionStationTree:function(t){return a.get("QueryPositionStationTree",t)},AllocatePosition:function(t){return a.post("AllocatePosition",t)},DeletePositionStation:function(t){return a.post("DeletePositionStation",t)},queryDeptByUser:function(t){return a.get("QueryDeptByUser",t)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(t){return a.get("QueryTwoLevelStation",t)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(t){return a.get("QueryStationAllowance",t)},downloadStationAllowanceTemplate:function(t){return a.post("DownloadStationAllowanceTemplate",{data:t,responseType:"arraybuffer"})},importStationAllowance:function(t,e){return e.append("file",t),a.postForm("ImportStationAllowance",e)},exportStationAllowance:function(t){return a.post("ExportStationAllowance",{data:t,responseType:"arraybuffer"})},getStationAllowance:function(t){return a.get("GetStationAllowance",t)},addStationAllowance:function(t){return a.post("AddStationAllowance",t)},updateStationAllowance:function(t){return a.post("UpdateStationAllowance",t)},querySeniority:function(t){return a.get("QuerySeniority",t)},importSeniority:function(t,e){return e.append("file",t),a.postForm("ImportSeniority",e)},downloadSeniorityTemplate:function(t){return a.post("DownloadSeniorityTemplate",{data:t,responseType:"arraybuffer"})},exportSeniority:function(t){return a.post("ExportSeniority",{data:t,responseType:"arraybuffer"})},getSeniority:function(t){return a.get("GetSeniority",t)},addSeniority:function(t){return a.post("AddSeniority",t)},updateSeniority:function(t){return a.post("UpdateSeniority",t)},querySalaryScale:function(t){return a.get("QuerySalaryScale",t)},getSalaryScale:function(t){return a.get("GetSalaryScale",t)},addSalaryScale:function(t){return a.post("AddSalaryScale",t)},updateSalaryScale:function(t){return a.post("UpdateSalaryScale",t)},deleteSalaryScale:function(t){return a.post("DeleteSalaryScale",t)},importSalaryScale:function(t,e){return e.append("file",t),a.postForm("ImportSalaryScale",e)},exportSalaryScale:function(t){return a.post("ExportSalaryScale",{data:t,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(t){return a.get("QuerySalaryScaleByStationId",t)},queryStationAllowanceByStationId:function(t){return a.get("QueryStationAllowanceByStationId",t)},queryCarSubsidy:function(t){return a.get("QueryCarSubsidy",t)},getCarSubsidy:function(t){return a.get("GetCarSubsidy",t)},addCarSubsidy:function(t){return a.post("AddCarSubsidy",t)},updateCarSubsidy:function(t){return a.post("UpdateCarSubsidy",t)},deleteCarSubsidy:function(t){return a.post("DeleteCarSubsidy",t)},queryTelephoneFee:function(t){return a.get("QueryTelephoneFee",t)},getTelephoneFee:function(t){return a.get("GetTelephoneFee",t)},addTelephoneFee:function(t){return a.post("AddTelephoneFee",t)},updateTelephoneFee:function(t){return a.post("UpdateTelephoneFee",t)},deleteTelephoneFee:function(t){return a.post("DeleteTelephoneFee",t)}}},f9ac:function(t,e,r){"use strict";var n=r("cfe3"),o="SysManage",a=new n["a"](o);e["a"]={queryDict:function(t){return a.get("QueryDict",t)},queryDictType:function(t){return a.post("QueryDictType",t)},addDict:function(t){return a.post("AddDict",t)},deleteDict:function(t){return a.post("DeleteDict",t)},updateDict:function(t){return a.post("UpdateDict",t)},getDict:function(t){return a.get("GetDict",t)},querySysSetting:function(t){return a.get("QuerySysSetting",t)},addSysSetting:function(t){return a.post("AddSysSetting",t)},deleteSysSetting:function(t){return a.post("DeleteSysSetting",t)},updateSysSetting:function(t){return a.post("UpdateSysSetting",t)},getSysSetting:function(t){return a.get("GetSysSetting",t)},queryLanguage:function(t){return a.get("QueryLanguage",t)},getEnumInfos:function(t){return a.get("GetEnumInfos",t)},queryUserGroups:function(t){return a.post("QueryUserGroups",t)},saveUserGroup:function(t){return a.post("SaveUserGroup",t)},deleteUserGroup:function(t){return a.post("DeleteUserGroup",t)},dropdownUserGroups:function(t){return a.get("DropdownUserGroups",t)},queryUsers:function(t){return a.post("QueryUsers",t)},saveUser:function(t){return a.post("SaveUser",t)},deleteUser:function(t){return a.post("DeleteUser",t)},initPwd:function(t){return a.post("InitPwd",t)},getUserById:function(t){return a.get("GetUserById",t)},queryEmployees:function(t){return a.post("QueryEmployees",t)},queryModuleInfos:function(t){return a.get("QueryModuleInfos",t)},getRightSettingByUserGroup:function(t){return a.get("GetRightSettingByUserGroup",t)},saveRightSetting:function(t){return a.post("SaveRightSetting",t)},getRightOfDeptByUserGroup:function(t){return a.get("GetRightOfDeptByUserGroup",t)},saveRightOfDept:function(t){return a.post("SaveRightOfDept",t)},queryControlRight:function(t){return a.post("QueryControlRight",t)},saveControlRights:function(t){return a.post("SaveControlRights",t)},getControlRightByCurrentUser:function(t){return a.get("GetControlRightByCurrentUser",t)},queryStationTree:function(t){return a.get("QueryStationTree",t)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(t){return a.get("QueryStationSelector",t)},querySalaryScaleSelector:function(t){return a.get("QuerySalaryScaleSelector",t)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(t){return a.get("QueryStationAllowance",t)}}}}]);