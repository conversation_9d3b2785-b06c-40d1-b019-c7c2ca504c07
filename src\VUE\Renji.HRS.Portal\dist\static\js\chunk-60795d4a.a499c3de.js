(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-60795d4a"],{"3f3f":function(t,e,r){"use strict";var n=r("96f4"),o=r.n(n);o.a},"96f4":function(t,e,r){},cbd2:function(t,e,r){"use strict";var n=r("cfe3"),o="AttendanceManage",a=new n["a"](o);e["a"]={getAttMonthShiftRecord:function(t){return a.get("GetAttMonthShiftRecord",t)},queryAttMonthShiftRecordDetail:function(t){return a.get("QueryAttMonthShiftRecordDetail",t)},batchConfirmAttMonthShiftRecord:function(t){return a.post("BatchConfirmAttMonthShiftRecord",t)},saveAttMonthShiftRecord:function(t){return a.post("SaveAttMonthShiftRecord",t)},submitAttMonthShiftRecord:function(t){return a.post("SubmitAttMonthShiftRecord",t)},ConfirmAttMonthShiftRecord:function(t){return a.post("ConfirmAttMonthShiftRecord",t)},rejectAttMonthShiftRecord:function(t){return a.post("RejectAttMonthShiftRecord",t)},searchAttMonthShiftRecordDetail:function(t){return a.get("SearchAttMonthShiftRecordDetail",t)},searchAttMonthShiftRecordDetail_Update:function(t){return a.get("SearchAttMonthShiftRecordDetail_Update",t)},updateAttMonthShiftRecordDetail:function(t){return a.post("UpdateAttMonthShiftRecordDetail",t)},getColorDeptTree_MiddleNightShift:function(t){return a.get("GetColorDeptTree_MiddleNightShift",t)},get_MiddleNightShiftReportExcel:function(t){return a.getFile("Get_MiddleNightShiftReportExcel",t)},getAttHolidayOTRecord:function(t){return a.get("GetAttHolidayOTRecord",t)},queryAttHolidayOTRecordDetail:function(t){return a.get("QueryAttHolidayOTRecordDetail",t)},saveAttHolidayOTRecord:function(t){return a.post("SaveAttHolidayOTRecord",t)},batchConfirmAttHolidayOTRecord:function(t){return a.post("BatchConfirmAttHolidayOTRecord",t)},submitAttHolidayOTRecord:function(t){return a.post("SubmitAttHolidayOTRecord",t)},ConfirmAttHolidayOTRecord:function(t){return a.post("ConfirmAttHolidayOTRecord",t)},rejectAttHolidayOTRecord:function(t){return a.post("RejectAttHolidayOTRecord",t)},searchAttHolidayOTRecordDetail:function(t){return a.get("SearchAttHolidayOT",t)},searchAttHolidayOTRecordDetail_Update:function(t){return a.get("SearchAttHolidayOTRecordDetail_Update",t)},updateAttHolidayOTRecordDetail:function(t){return a.post("UpdateAttHolidayOTRecordDetail",t)},getColorDeptTree_HolidayOT:function(t){return a.get("GetColorDeptTree_HolidayOT",t)},getOTReportExcel:function(t){return a.getFile("GetOTReportExcel",t)},getAttDayOffRecord:function(t){return a.get("GetAttDayOffRecord",t)},queryAttDayOffRecordDetail:function(t){return a.get("QueryAttDayOffRecordDetail",t)},saveAttDayOffRecord:function(t){return a.post("SaveAttDayOffRecord",t)},submitAttDayOffRecord:function(t){return a.post("SubmitAttDayOffRecord",t)},updateApproveAttDayOffRecord:function(t){return a.post("UpdateApproveAttDayOffRecord",t)},rejectAttDayOffRecord:function(t){return a.post("RejectAttDayOffRecord",t)},searchAttDayOffRecordDetail:function(t){return a.get("SearchAttDayOffRecordDetail",t)},searchAttDayOffRecordDetail_Update:function(t){return a.get("SearchAttDayOffRecordDetail_Update",t)},updateAttDayOffRecordDetail:function(t){return a.post("UpdateAttDayOffRecordDetail",t)},getColorDeptTree_DayOff:function(t){return a.get("GetColorDeptTree_DayOff",t)},getDayOffReportExcel:function(t){return a.getFile("GetDayOffReportExcel",t)},searchAttDayOffRecordDetail1:function(t){return a.get("SearchAttDayOffRecordDetail1",t)},searchAttMonthWatchRecord:function(t){return a.get("SearchAttMonthWatchRecord",t)},updateAttMonthWatchRecord:function(t){return a.post("UpdateAttMonthWatchRecord",t)},getMonthWatchTReportExcel:function(t){return a.getFile("GetMonthWatchTReportExcel",t)},getAttDayOffRecordDetail1Excel:function(t){return a.getFile("GetAttDayOffRecordDetail1Excel",t)},queryEmployeeList:function(t){return a.get("QueryEmployeeList",t)},searchAttDayOffRecordDetail2:function(t){return a.get("SearchAttDayOffRecordDetail2",t)},getAttDayOffRecordDetail2Excel:function(t){return a.getFile("GetAttDayOffRecordDetail2Excel",t)},searchAttDayOffRecordDetail3:function(t){return a.get("SearchAttDayOffRecordDetail3",t)},getAttDayOffRecordDetail3Excel:function(t){return a.getFile("GetAttDayOffRecordDetail3Excel",t)},queryAttDayOffRecordProphylacticDetail:function(t){return a.get("QueryAttDayOffRecordProphylacticDetail",t)},exportAttDayOffRecordProphylacticDetail:function(t){return a.getFile("ExportAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylacticCase:function(t){return a.get("GetAttDayOffRecordProphylacticCase",t)},getAttDayOffRecordProphylacticDetail:function(t){return a.get("GetAttDayOffRecordProphylacticDetail",t)},getAttDayOffRecordProphylactic:function(t){return a.get("GetAttDayOffRecordProphylactic",t)},addAttDayOffRecordProphylactic:function(t){return a.post("AddAttDayOffRecordProphylactic",t)},updateAttDayOffRecordProphylactic:function(t){return a.post("UpdateAttDayOffRecordProphylactic",t)},deleteAttDayOffRecordProphylacticDetail:function(t){return a.post("DeleteAttDayOffRecordProphylacticDetail",t)},subjectAttDayOffRecordProphylactic:function(t){return a.post("SubjectAttDayOffRecordProphylactic",t)},queryCheckRecordFilling:function(t){return a.get("QueryCheckRecordFilling",t)},queryPersonnelAttendanceData:function(t){return a.get("QueryPersonnelAttendanceData",t)},queryProphylacticChange:function(t){return a.get("QueryProphylacticChange",t)},exportProphylacticChange:function(t){return a.getFile("ExportProphylacticChange",t)},queryPersonnelPendingApproval:function(t){return a.get("QueryPersonnelPendingApproval",t)},approveAttDayOffRecord:function(t){return a.post("ApproveAttDayOffRecord",t)},getSameDeptEmployeeWithHealthAllowance:function(t){return a.get("GetSameDeptEmployeeWithHealthAllowance",t)}}},cc51:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container"},[r("layout3",{scopedSlots:t._u([{key:"aside",fn:function(){return[r("el-button",{on:{click:t.syncTreeColor}},[t._v("颜色同步")]),r("el-button",{on:{click:t.batchConfirm}},[t._v("批量审批")]),r("c-tree",{ref:"tree",attrs:{options:t.treeData,props:t.treeProps,"expand-all":!0},on:{nodeClick:t.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[r("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.headModel}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"申请日期"}},[r("el-date-picker",{attrs:{type:"date",placeholder:"请选择申请日期","value-format":"yyyy-MM-dd"},on:{change:t.dateChange},model:{value:t.recordDate,callback:function(e){t.recordDate=e},expression:"recordDate"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"填表人"}},[t._v(" "+t._s(t.headModel.documentMaker)+" ")])],1)],1),r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"状态"}},[r("el-select",{attrs:{placeholder:"请选择"},on:{change:t.statusChanged},model:{value:t.status,callback:function(e){t.status=e},expression:"status"}},t._l(t.statusList,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"部门"}},[r("el-select",{attrs:{placeholder:"请选择"},model:{value:t.statusDept,callback:function(e){t.statusDept=e},expression:"statusDept"}},t._l(t.statusDeptList,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),r("el-row",[2==t.headModel.enumStatus?r("el-col",{attrs:{span:12}},[r("el-button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确认")]),r("el-button",{attrs:{type:"primary"},on:{click:t.reject}},[t._v("退回")])],1):t._e()],1),r("el-row",[2==t.headModel.enumStatus?r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"退回原因"}},[r("el-input",{attrs:{type:"textarea"},model:{value:t.headModel.rejectReason,callback:function(e){t.$set(t.headModel,"rejectReason",e)},expression:"headModel.rejectReason"}})],1)],1):t._e()],1)],1),r("div",{staticClass:"tip_div"},[r("span",{staticClass:"tip_content"},[t._v(t._s(t.headModel.tip))])]),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:t.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},height:"520"}},[r("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"130",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.empCode))])]}}])}),r("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"160",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.empName))])]}}])}),r("el-table-column",{attrs:{prop:"date",label:"在职方式",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.hireStyleName))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"加班类型",align:"center",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[2==t.headModel.enumStatus?r("el-select",{attrs:{placeholder:"请选择"},model:{value:n.enumOverTimeType,callback:function(e){t.$set(n,"enumOverTimeType",e)},expression:"row.enumOverTimeType"}},t._l(t.overTimeType,(function(t){return r("el-option",{key:t.value,attrs:{label:t.desc,value:t.value}})})),1):r("span",[t._v(t._s(n.enumOverTimeTypeDesc))])]}}])}),r("el-table-column",{attrs:{prop:"name",label:"人事修改人",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[r("span",[t._v(t._s(n.updator))])]}}])})],1),r("c-pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,"page-sizes":[20,50,100],page:t.listQuery.pageIndex,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageIndex",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.paginationChanged}})]},proxy:!0}])})],1)},o=[],a=(r("99af"),r("4de4"),r("fb6a"),r("d3b7"),r("25f0"),r("4d90"),r("cbd2")),i=r("f9ac"),c={components:{},data:function(){return{recordDate:this.getNowTime(),headModel:{},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeLoading:!1,currentNode:null,total:0,listQuery:{pageIndex:1,pageSize:20},listLoading:!1,tableData:[],allData:[],treeExpandedKeys:[],status:"",statusList:[{label:"未提交",value:1},{label:"已提交",value:2},{label:"人事部门已确认",value:3}],statusDept:"",statusDeptList:[],overTimeType:[],allStatusDepts:[]}},created:function(){this.syncTreeColor(),this.loadOverTimeType()},methods:{loadOverTimeType:function(){var t=this;i["a"].getEnumInfos({enumType:"OverTimeType"}).then((function(e){t.overTimeType=e.data.datas})).catch((function(t){console.log(t)}))},getNowTime:function(){var t=new Date,e=t.getFullYear(),r=t.getMonth(),n=t.getDate();r+=1,r=r.toString().padStart(2,"0");var o="".concat(e,"-").concat(r,"-").concat(n);return o},resetCurrentNode:function(){this.currentNode=null},treeNodeClick:function(t){this.currentNode=t,this.listQuery.pageIndex=1,this.getAttHolidayOTRecord()},statusChanged:function(t){var e=this.allStatusDepts.filter((function(e,r,n){return e.status===t}))[0];e&&e.depts.length>0?this.statusDeptList=e.depts:(this.statusDeptList=[],this.statusDept="")},dateChange:function(){this.listQuery.pageIndex=1,this.syncTreeColor(),this.getAttHolidayOTRecord()},getAttHolidayOTRecord:function(){var t=this;if(this.currentNode){var e={RecordDate:this.recordDate,DeptId:this.currentNode.id};a["a"].getAttHolidayOTRecord(e).then((function(e){e.succeed?(t.headModel=e.data,console.log(t.headModel),t.queryAttHolidayOTRecordDetail(e.data.id)):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}else this.$notice.message("请选择部门。","info")},queryAttHolidayOTRecordDetail:function(t){var e=this,r={RecordId:t,DeptId:this.currentNode.id,RecordMonth:this.recordDate};a["a"].queryAttHolidayOTRecordDetail(r).then((function(t){e.listLoading=!1,t.succeed?(e.allData=t.data.datas,e.total=t.data.recordCount,e.getTableData()):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},paginationChanged:function(){this.getTableData()},getTableData:function(){var t=this.listQuery.pageSize,e=this.listQuery.pageIndex;this.tableData=this.allData.slice((e-1)*t,e*t)},syncTreeColor:function(){var t=this,e={RecordDate:this.recordDate};a["a"].getColorDeptTree_HolidayOT(e).then((function(e){e.succeed&&(t.treeLoading=!1,t.treeData=e.data.colorDepts,t.treeExpandedKeys.push(e.data.colorDepts[0].id),t.allStatusDepts=e.data.statusDepts,t.currentNode&&t.$refs.tree.setCurrentKey(t.currentNode.id))})).catch((function(e){t.treeLoading=!1,console.log(e)}))},batchConfirm:function(){var t=this;if(this.recordDate){var e="你确定要确认"+this.recordDate+"的所有已经递交的节日加班费吗？";this.$confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.RecordDate=t.recordDate,a["a"].batchConfirmAttHolidayOTRecord(t.headModel).then((function(e){e.succeed?(t.$notice.message("批量确认成功","success"),t.getAttHolidayOTRecord(),t.syncTreeColor()):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))}))}else this.$notice.message("请选择申请日期","warning")},confirm:function(){var t=this;this.recordDate&&this.currentNode&&this.$confirm("确认审批吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.headModel.details=t.allData,a["a"].ConfirmAttHolidayOTRecord(t.headModel).then((function(e){e.succeed?(t.$notice.message("人事已审阅","success"),t.getAttHolidayOTRecord(),t.syncTreeColor()):t.$notice.resultTip(e)})).catch((function(t){console.log(t)}))})),this.currentNode?this.recordDate||this.$notice.message("请选择申请日期","warning"):this.$notice.message("请选择部门","warning")},reject:function(){var t=this;this.recordDate&&this.currentNode&&this.$confirm("确认退回吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){"undefined"===typeof t.headModel.rejectReason||null===t.headModel.rejectReason||""===t.headModel.rejectReason?t.$notice.message("请填写退回原因！","warning"):(t.headModel.details=t.allData,a["a"].rejectAttHolidayOTRecord(t.headModel).then((function(e){e.succeed?(t.getAttHolidayOTRecord(),t.syncTreeColor(),t.$notice.message("操作成功","success")):t.$notice.resultTip(e)})).catch((function(t){console.log(t)})))})),this.currentNode?this.recordDate||this.$notice.message("请选择申请日期","warning"):this.$notice.message("请选择部门","warning")}}},l=c,u=(r("3f3f"),r("2877")),s=Object(u["a"])(l,n,o,!1,null,null,null);e["default"]=s.exports},f9ac:function(t,e,r){"use strict";var n=r("cfe3"),o="SysManage",a=new n["a"](o);e["a"]={queryDict:function(t){return a.get("QueryDict",t)},queryDictType:function(t){return a.post("QueryDictType",t)},addDict:function(t){return a.post("AddDict",t)},deleteDict:function(t){return a.post("DeleteDict",t)},updateDict:function(t){return a.post("UpdateDict",t)},getDict:function(t){return a.get("GetDict",t)},querySysSetting:function(t){return a.get("QuerySysSetting",t)},addSysSetting:function(t){return a.post("AddSysSetting",t)},deleteSysSetting:function(t){return a.post("DeleteSysSetting",t)},updateSysSetting:function(t){return a.post("UpdateSysSetting",t)},getSysSetting:function(t){return a.get("GetSysSetting",t)},queryLanguage:function(t){return a.get("QueryLanguage",t)},getEnumInfos:function(t){return a.get("GetEnumInfos",t)},queryUserGroups:function(t){return a.post("QueryUserGroups",t)},saveUserGroup:function(t){return a.post("SaveUserGroup",t)},deleteUserGroup:function(t){return a.post("DeleteUserGroup",t)},dropdownUserGroups:function(t){return a.get("DropdownUserGroups",t)},queryUsers:function(t){return a.post("QueryUsers",t)},saveUser:function(t){return a.post("SaveUser",t)},deleteUser:function(t){return a.post("DeleteUser",t)},initPwd:function(t){return a.post("InitPwd",t)},getUserById:function(t){return a.get("GetUserById",t)},queryEmployees:function(t){return a.post("QueryEmployees",t)},queryModuleInfos:function(t){return a.get("QueryModuleInfos",t)},getRightSettingByUserGroup:function(t){return a.get("GetRightSettingByUserGroup",t)},saveRightSetting:function(t){return a.post("SaveRightSetting",t)},getRightOfDeptByUserGroup:function(t){return a.get("GetRightOfDeptByUserGroup",t)},saveRightOfDept:function(t){return a.post("SaveRightOfDept",t)},queryControlRight:function(t){return a.post("QueryControlRight",t)},saveControlRights:function(t){return a.post("SaveControlRights",t)},getControlRightByCurrentUser:function(t){return a.get("GetControlRightByCurrentUser",t)},queryStationTree:function(t){return a.get("QueryStationTree",t)},queryStationTypeSelector:function(){return a.get("QueryStationTypeSelector")},queryStationSelector:function(t){return a.get("QueryStationSelector",t)},querySalaryScaleSelector:function(t){return a.get("QuerySalaryScaleSelector",t)},queryTelephoneFeeSelector:function(){return a.get("QueryTelephoneFeeSelector")},queryCarSubsidySelector:function(){return a.get("QueryCarSubsidySelector")},queryStationAllowance:function(t){return a.get("QueryStationAllowance",t)}}}}]);