(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4b4d25fa"],{"31d3":function(e,t,a){"use strict";var l=a("4275"),i=a.n(l);i.a},"3b5e":function(e,t,a){"use strict";var l=a("722e"),i=a.n(l);i.a},4275:function(e,t,a){},"722e":function(e,t,a){},"8aeb":function(e,t,a){},a459:function(e,t,a){"use strict";var l=a("8aeb"),i=a.n(l);i.a},afb1:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{"append-to-body":"",title:e.title,width:"90%","close-on-click-modal":!1,visible:!0},on:{close:function(t){return e.cancle()}}},[a("el-form",{ref:"dataForm",staticClass:"el-dialogform",attrs:{rules:e.rules,model:e.tempFormModel,"label-position":"right","label-width":"110px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"员工姓名",prop:"empName"}},[a("table",{staticClass:"selectButton",staticStyle:{width:"100%","margin-top":"-3px"}},[a("tr",[a("td",[a("el-input",{attrs:{readonly:!0,placeholder:"请选择员工"},model:{value:e.tempFormModel.empName,callback:function(t){e.$set(e.tempFormModel,"empName",t)},expression:"tempFormModel.empName"}})],1),a("td",{staticStyle:{width:"50px"}},[e.isAdd?a("el-button",{staticStyle:{width:"50px"},attrs:{type:"primary",title:"选择员工"},on:{click:e.selectEmployeeDialog}},[e._v("选 择")]):e._e()],1)])])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"唯一码"}},[e._v(" "+e._s(e.tempFormModel.empUid)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"工号"}},[e._v(" "+e._s(e.tempFormModel.empCode)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"性别"}},[e._v(" "+e._s(e.tempFormModel.genderDesc)+" ")])],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"部门"}},[e._v(" "+e._s(e.tempFormModel.empDept)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"院区"}},[e._v(" "+e._s(e.tempFormModel.hospitalAreaNameText)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"身份证号"}},[e._v(" "+e._s(e.tempFormModel.identityNumber)+" ")])],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[e._v("基本信息")])]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"请假类别",prop:"enumLeaveType"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"请假类别"},model:{value:e.tempFormModel.enumLeaveType,callback:function(t){e.$set(e.tempFormModel,"enumLeaveType",t)},expression:"tempFormModel.enumLeaveType"}},e._l(e.leaveTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊医院",prop:"visitingHospital"}},[a("el-input",{attrs:{clearable:"",placeholder:"就诊医院",maxlength:"50"},model:{value:e.tempFormModel.visitingHospital,callback:function(t){e.$set(e.tempFormModel,"visitingHospital",t)},expression:"tempFormModel.visitingHospital"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊科室",prop:"visitingDepartment"}},[a("el-input",{attrs:{clearable:"",placeholder:"就诊科室",maxlength:"50"},model:{value:e.tempFormModel.visitingDepartment,callback:function(t){e.$set(e.tempFormModel,"visitingDepartment",t)},expression:"tempFormModel.visitingDepartment"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"就诊医师",prop:"visitingPhysician"}},[a("el-input",{attrs:{clearable:"",placeholder:"就诊医师",maxlength:"50"},model:{value:e.tempFormModel.visitingPhysician,callback:function(t){e.$set(e.tempFormModel,"visitingPhysician",t)},expression:"tempFormModel.visitingPhysician"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"诊断意见",prop:"diagnostiOpinion"}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"诊断意见",maxlength:"500"},model:{value:e.tempFormModel.diagnostiOpinion,callback:function(t){e.$set(e.tempFormModel,"diagnostiOpinion",t)},expression:"tempFormModel.diagnostiOpinion"}})],1)],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[e._v("防保科建议")])]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"假期类型",prop:"enumHolidayType"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"假期类型"},on:{change:e.recalculate},model:{value:e.tempFormModel.enumHolidayType,callback:function(t){e.$set(e.tempFormModel,"enumHolidayType",t)},expression:"tempFormModel.enumHolidayType"}},e._l(e.holidayTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.desc,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"开具时间",prop:"issuingTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"开具时间","value-format":"yyyy-MM-dd"},model:{value:e.tempFormModel.issuingTime,callback:function(t){e.$set(e.tempFormModel,"issuingTime",t)},expression:"tempFormModel.issuingTime"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"休假日期",prop:"leaveDateRange"}},[a("el-date-picker",{attrs:{clearable:!1,type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.leaveDateChange},model:{value:e.tempFormModel.leaveDateRange,callback:function(t){e.$set(e.tempFormModel,"leaveDateRange",t)},expression:"tempFormModel.leaveDateRange"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"假期类型备注",prop:"holidayRemark",rules:3==e.tempFormModel.enumHolidayType?e.rules.holidayRemark:[{required:!1,message:"假期类型备注必填",trigger:"blur"}]}},[a("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"假期类型备注",maxlength:"500"},model:{value:e.tempFormModel.holidayRemark,callback:function(t){e.$set(e.tempFormModel,"holidayRemark",t)},expression:"tempFormModel.holidayRemark"}})],1)],1)],1),a("el-divider",{staticClass:"confirm",attrs:{"content-position":"left"}},[a("span",{staticStyle:{color:"blue"}},[e._v("休假详情")])]),a("el-row",{attrs:{gutter:10}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.tempFormModel.detail,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"},"row-class-name":e.handleRowClass}},[a("el-table-column",{attrs:{label:"月份",prop:"recordMonth","header-align":"center",align:"center","min-width":"90px"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.recordMonth?new Date(l.recordMonth).Format("yyyy-MM"):""))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"病假",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h2=Math.abs(l.h2)}},model:{value:l.h2,callback:function(t){e.$set(l,"h2",e._n(t))},expression:"row.h2"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"事假",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h3=Math.abs(l.h3)}},model:{value:l.h3,callback:function(t){e.$set(l,"h3",e._n(t))},expression:"row.h3"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"产假",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h4=Math.abs(l.h4)}},model:{value:l.h4,callback:function(t){e.$set(l,"h4",e._n(t))},expression:"row.h4"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"哺乳假",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h5=Math.abs(l.h5)}},model:{value:l.h5,callback:function(t){e.$set(l,"h5",e._n(t))},expression:"row.h5"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"探亲假",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h6=Math.abs(l.h6)}},model:{value:l.h6,callback:function(t){e.$set(l,"h6",e._n(t))},expression:"row.h6"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"计生假",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h7=Math.abs(l.h7)}},model:{value:l.h7,callback:function(t){e.$set(l,"h7",e._n(t))},expression:"row.h7"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"婚丧假",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h8=Math.abs(l.h8)}},model:{value:l.h8,callback:function(t){e.$set(l,"h8",e._n(t))},expression:"row.h8"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"脱产读研",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h9=Math.abs(l.h9)}},model:{value:l.h9,callback:function(t){e.$set(l,"h9",e._n(t))},expression:"row.h9"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因公出国",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h10=Math.abs(l.h10)}},model:{value:l.h10,callback:function(t){e.$set(l,"h10",e._n(t))},expression:"row.h10"}})]}}])}),a("el-table-column",{attrs:{prop:"name",label:"因私出国",align:"right","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-input",{staticClass:"edit-input",attrs:{min:"0",type:"number",size:"mini"},on:{input:function(e){return l.h11=Math.abs(l.h11)}},model:{value:l.h11,callback:function(t){e.$set(l,"h11",e._n(t))},expression:"row.h11"}})]}}])}),e.isAdd?e._e():a("el-table-column",{key:50000100,attrs:{prop:"name",label:"状态",align:"center","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.enumStatusDesc))])]}}],null,!1,374450834)}),a("el-table-column",{attrs:{label:"操作",fixed:"right",align:"center","header-align":"center",width:"90","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[10!=l.enumStatus?a("el-button",{staticStyle:{"padding-left":"3px !important"},attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.handleDelete(l)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{icon:"el-icon-close"},on:{click:function(t){return e.cancle()}}},[e._v(" 关闭 ")]),a("el-button",{attrs:{loading:e.btnSaveLoading,type:"primary",icon:"el-icon-check"},on:{click:function(t){return e.save()}}},[e._v(" 保存 ")])],1)],1),a("selectusercomponent",{ref:"selectempc",on:{selectRow:e.setEmp}})],1)},i=[],n=(a("4de4"),a("7db0"),a("4160"),a("159b"),a("cbd2")),s=a("b113"),o=a("f9ac"),r={computed:{},name:"modifyAttDayOffRecordProphylacticDetail",components:{selectusercomponent:s["a"]},props:{id:{type:String,default:""},title:{type:String,default:""}},data:function(){var e=function(e,t,a){t&&2==t.length?a():a(new Error("休假日期区间必填"))};return{span:12,isAdd:!1,tempFormModel:{detail:[],leaveDateRange:[],id:"",employeeId:"",empUid:"",empCode:"",empName:"",genderDes:"",empDept:"",hospitalAreaNameText:"",identityNumber:""},rules:{empName:[{required:!0,message:"员工必填",trigger:"change"}],enumLeaveType:[{required:!0,message:"请假类别必填",trigger:"change"}],diagnostiOpinion:[{required:!0,message:"诊断意见必填",trigger:"blur"}],visitingHospital:[{required:!0,message:"就诊医院必填",trigger:"blur"}],enumHolidayType:[{required:!0,message:"假期类型必填",trigger:"change"}],holidayRemark:[{required:!0,message:"其他假期类型时备注必填",trigger:"blur"}],leaveDateRange:[{validator:e,trigger:"change"}],issuingTime:[{required:!0,message:"开具时间必填",trigger:"change"}]},btnSaveLoading:!1,leaveTypeList:[],holidayTypeList:[],listLoading:!1}},watch:{id:function(e){this.tempFormModel.id=e}},mounted:function(){},created:function(){this.initLeaveTypeList(),this.initHolidayTypeList(),this.init()},methods:{leaveDateChange:function(){var e=this,t=[];if(this.tempFormModel.leaveDateRange&&2==this.tempFormModel.leaveDateRange.length){var a=this.$moment(this.tempFormModel.leaveDateRange[0]),l=this.$moment(this.tempFormModel.leaveDateRange[1]),i=a.startOf("month");while(i<=l.endOf("month"))t.push(i.format("YYYY-MM")),i.add(1,"month")}t&&t.length>0&&t.forEach((function(t){var a=e.tempFormModel.detail.find((function(a){return e.$moment(a.recordMonth).format("YYYY-MM")==t}));a||e.tempFormModel.detail.push({recordMonth:e.$moment(t+"-01"),h2:null,h3:null,h4:null,h5:null,h6:null,h7:null,h8:null,h9:null,h10:null,h11:null})})),this.tempFormModel.detail=this.tempFormModel.detail.filter((function(a){var l=t.find((function(t){return e.$moment(a.recordMonth).format("YYYY-MM")==t}));return a.id||!!l})),this.tempFormModel.detail.sort((function(t,a){return e.$moment(t.recordMonth)-e.$moment(a.recordMonth)})),this.recalculate()},recalculate:function(){if(this.tempFormModel&&this.tempFormModel.enumHolidayType&&this.tempFormModel&&this.tempFormModel.leaveDateRange&&0!=this.tempFormModel.leaveDateRange.length){var e=this,t=[],a=(this.$moment(this.tempFormModel.leaveDateRange[0]),this.$moment(this.tempFormModel.leaveDateRange[1])),l=this.$moment(this.tempFormModel.leaveDateRange[0]).startOf("month");while(l<=a.endOf("month"))t.push(l.format("YYYY-MM")),l.add(1,"month");this.tempFormModel.detail.forEach((function(a,l,i){var n=t.find((function(t){return e.$moment(a.recordMonth).format("YYYY-MM")==t}));if(n&&3!=e.tempFormModel.enumHolidayType){var s=e.$moment(a.recordMonth),o=e.$moment(a.recordMonth).endOf("months"),r=e.$moment(e.tempFormModel.leaveDateRange[0]),c=e.$moment(e.tempFormModel.leaveDateRange[1]);s=r>s?r:s,o=c<o?c:o;var d=o.diff(s,"days")+1;1==e.tempFormModel.enumHolidayType?(e.$set(a,"h2",d),e.$set(a,"h4",null)):(e.$set(a,"h2",null),e.$set(a,"h4",d))}else e.$set(a,"h2",null),e.$set(a,"h4",null);e.$set(a,"h3",null),e.$set(a,"h5",null),e.$set(a,"h6",null),e.$set(a,"h7",null),e.$set(a,"h8",null),e.$set(a,"h9",null),e.$set(a,"h10",null),e.$set(a,"h11",null)}))}},init:function(){if(!this.id)return this.isAdd=!0,void this.clear();this.id&&(this.isAdd=!1,this.get(this.id))},get:function(e){var t=this;this.btnSaveLoading=!0,n["a"].getAttDayOffRecordProphylacticCase({id:this.id}).then((function(e){t.tempFormModel=e.data,t.btnSaveLoading=!1})).catch((function(e){t.btnSaveLoading=!1}))},selectEmployeeDialog:function(){this.$refs.selectempc.showEmp=!0},setEmp:function(e){this.tempFormModel.employeeId=e.id,this.tempFormModel.empUid=e.uid,this.tempFormModel.empCode=e.empCode,this.tempFormModel.empName=e.displayName,this.tempFormModel.genderDesc=e.enumGenderDesc,this.tempFormModel.empDept=e.deptName,this.tempFormModel.hospitalAreaNameText=e.hospitalAreaNameText,this.tempFormModel.identityNumber=e.identityNumber},initLeaveTypeList:function(){var e=this,t={enumType:"LeaveType"};o["a"].getEnumInfos(t).then((function(t){e.leaveTypeList=t.data.datas})).catch((function(e){console.log(e)}))},initHolidayTypeList:function(){var e=this,t={enumType:"HolidayType"};o["a"].getEnumInfos(t).then((function(t){e.holidayTypeList=t.data.datas})).catch((function(e){console.log(e)}))},save:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.tempFormModel.leaveStartDate=e.tempFormModel.leaveDateRange[0],e.tempFormModel.leaveEndDate=e.tempFormModel.leaveDateRange[1],e.tempFormModel.id?e.update():e.addNew())}))},addNew:function(){var e=this;this.btnSaveLoading=!0,n["a"].addAttDayOffRecordProphylactic(this.tempFormModel).then((function(t){e.btnSaveLoading=!1,t.succeed?(e.$notice.message("新增成功","success"),e.close(t.data.id)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.btnSaveLoading=!1,t.processed||e.$notice.message("新增失败。","error")}))},update:function(){var e=this;this.btnSaveLoading=!0,n["a"].updateAttDayOffRecordProphylactic(this.tempFormModel).then((function(t){e.btnSaveLoading=!1,t.succeed?(e.$notice.message("修改成功","success"),e.close(t.data.id)):-3!==t.type&&e.$notice.resultTip(t)})).catch((function(t){e.btnSaveLoading=!1,t.processed||e.$notice.message("修改失败。","error")}))},handleDelete:function(e){var t=this;this.$confirm("确定删除当前月份防保科考勤申报?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.id?n["a"].deleteAttDayOffRecordProphylacticDetail(e).then((function(a){a.succeed?(t.tempFormModel.detail=t.tempFormModel.detail.filter((function(t){return e.recordMonth!=t.recordMonth})),t.$notice.message("删除成功","success")):-3!==a.type&&t.$notice.resultTip(a)})).catch((function(e){e.processed||t.$notice.message("删除失败","error")})):t.tempFormModel.detail=t.tempFormModel.detail.filter((function(t){return e.recordMonth!=t.recordMonth}))})).catch((function(e){e.succeed}))},handleRowClass:function(e,t){return e.rowIndex%2===0?"cellStyle":"stripedStyle"},clear:function(){this.$refs["dataForm"]&&this.$refs["dataForm"].resetFields(),this.tempFormModel={visitingHospital:"仁济医院",detail:[],leaveDateRange:[],id:this.id,employeeId:"",empUid:"",empCode:"",empName:"",genderDes:"",empDept:"",identityNumber:""}},close:function(e){this.clear(),this.$emit("refresh",{attDayOffRecordProphylacticCaseId:e})},cancle:function(){this.clear(),this.$emit("hidden")}}},c=r,d=(a("a459"),a("3b5e"),a("2877")),p=Object(d["a"])(c,l,i,!1,null,"85424006",null);t["a"]=p.exports},b0f3:function(e,t,a){e.exports=a.p+"static/img/fbkseal.b2cd35da.png"},b113:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.showEmp,width:"80%",top:"5vh"},on:{"update:visible":function(t){e.showEmp=t}}},[a("div",[a("layout3",{scopedSlots:e._u([{key:"aside",fn:function(){return[a("c-tree",{attrs:{options:e.treeData,props:e.treeProps,"expanded-keys":e.treeExpandedKeys},on:{nodeClick:e.treeNodeClick}})]},proxy:!0},{key:"main",fn:function(){return[a("el-form",{ref:"ref_searchFrom",attrs:{inline:!0,model:e.listQuery}},[a("el-form-item",{attrs:{label:"员工编号",prop:"empCode"}},[a("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.empCode,callback:function(t){e.$set(e.listQuery,"empCode",t)},expression:"listQuery.empCode"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"displayName"}},[a("el-input",{attrs:{placeholder:"",clearable:""},model:{value:e.listQuery.displayName,callback:function(t){e.$set(e.listQuery,"displayName",t)},expression:"listQuery.displayName"}})],1),a("el-form-item",{attrs:{label:"性别",prop:"sex"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.listQuery.sex,callback:function(t){e.$set(e.listQuery,"sex",t)},expression:"listQuery.sex"}},e._l(e.genderDropdown,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.search()}}},[e._v("查询")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.pageList,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{label:"员工编号",sortable:"custom",prop:"empCode"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.empCode))])]}}])}),a("el-table-column",{attrs:{label:"中文名",sortable:"custom",prop:"displayName"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.displayName))])]}}])}),a("el-table-column",{attrs:{label:"院区"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.hospitalAreaNameText))])]}}])}),a("el-table-column",{attrs:{label:"部门",sortable:"custom",prop:"Department.Name"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("span",[e._v(e._s(l.deptName))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"选择",align:"left","header-align":"center",width:"320","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{staticStyle:{"padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.selectRow(l)}}},[e._v(" 选择 ")])]}}])})],1),a("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])})],1)])},i=[],n=(a("d3b7"),a("d368")),s=a("f9ac"),o={components:{},data:function(){return{showEmp:!1,listLoading:!1,treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},treeExpandedKeys:["1"],pageList:[],listQuery:{total:1,pageIndex:1,pageSize:10},genderDropdown:[{value:1,label:"男"},{value:2,label:"女"}]}},created:function(){this.loadTree()},methods:{treeNodeClick:function(e){this.listQuery.deptId=e.id,this.getPageList()},selectRow:function(e){this.showEmp=!1,this.$emit("selectRow",e)},sortChange:function(e){"ascending"===e.order?this.listQuery.order=e.prop+" asc":"descending"===e.order?this.listQuery.order=e.prop+" desc":this.listQuery.order="",this.listQuery.pageIndex=1,this.getPageList()},loadTree:function(){var e=this;n["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data,e.treeExpandedKeys.push(t.data[0].id)})).catch((function(e){console.log(e)}))},search:function(){this.listQuery.pageIndex=1,this.listQuery.deptId=null,this.getPageList()},getPageList:function(){this.queryEmployeesMethods(this.listQuery)},queryEmployeesMethods:function(e){var t=this;this.listLoading=!0,s["a"].queryEmployees(e).then((function(e){e.succeed?(t.pageList=e.data.datas,t.listQuery.total=e.data.recordCount,t.listQuery.pageIndex=e.data.pageIndex):t.$notice.resultTip(e)})).catch((function(e){console.log(e)})).finally((function(){t.listLoading=!1}))}}},r=o,c=a("2877"),d=Object(c["a"])(r,l,i,!1,null,"407e330e",null);t["a"]=d.exports},c126:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticStyle:{display:"none"}},[l("div",{staticClass:"table-c",staticStyle:{"FONT-FAMILY":"宋体"},attrs:{id:"prophylacticPrintContent",align:"center"}},[l("el-row",{staticStyle:{"margin-top":"10px"}},[l("el-col",{staticStyle:{"font-size":"17px","text-align":"center"},attrs:{span:24}},[e._v(" 上海交通大学医学院附属仁济医院"),l("br"),e._v("职工学生疾病报告单 ")])],1),l("table",{staticStyle:{width:"95%","margin-top":"10px","font-size":"13px"},attrs:{cellspacing:"0",cellpadding:"0"}},[l("tr",{staticClass:"trHieight"},[l("td",{staticClass:"textCenter"},[e._v(" 姓名 ")]),l("td",{staticClass:"textLeft"},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.empName))])]),l("td",{staticClass:"textCenter"},[e._v(" 性别 ")]),l("td",{staticClass:"textLeft"},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.genderDesc))])]),l("td",{staticClass:"textCenter"},[e._v(" 年龄 ")]),l("td",{staticClass:"textLeft"},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.age))])])]),l("tr",{staticClass:"trHieight"},[l("td",{staticClass:"textCenter"},[e._v(" 科室 ")]),l("td",{staticClass:"textLeft"},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.empDept))])]),l("td",{staticClass:"textCenter"},[e._v(" 工号 ")]),l("td",{staticClass:"textLeft"},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.empCode))])]),l("td",{staticClass:"textCenter"},[e._v(" 请假类别 ")]),l("td",{staticClass:"textLeft"},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.enumLeaveTypeDesc))])])]),l("tr",{staticClass:"trHieight"},[l("td",{staticClass:"textCenter"},[e._v(" 诊断 ")]),l("td",{staticClass:"textLeft",attrs:{colspan:"5"}},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.diagnostiOpinion))])])]),l("tr",{staticClass:"trHieight"},[l("td",{staticClass:"textCenter",attrs:{rowspan:"3"}},[e._v(" 防保科建议 ")]),l("td",{staticClass:"textLeft"},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.enumHolidayTypeDesc))])]),l("td",{staticClass:"textLeft",attrs:{colspan:"4"}},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.holidayRemark))])])]),l("tr",{staticClass:"trHieight"},[l("td",{staticClass:"textLeft",attrs:{colspan:"5"}},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v("休假日期: 自 "+e._s(e.model.leaveStartDate?new Date(e.model.leaveStartDate).Format("yyyy-MM-dd"):"")+" 至 "+e._s(e.model.leaveEndDate?new Date(e.model.leaveEndDate).Format("yyyy-MM-dd"):""))])])]),l("tr",{staticClass:"trHieight"},[l("td",{staticClass:"textCenter"},[e._v("病假单开具时间")]),l("td",{staticClass:"textLeft"},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.issuingTime?new Date(e.model.issuingTime).Format("yyyy-MM-dd"):""))])]),l("td",{staticClass:"textCenter"},[e._v("病假单开具医院")]),l("td",{staticClass:"textLeft",attrs:{colspan:"2"}},[l("span",{staticStyle:{"margin-left":"4px"}},[e._v(e._s(e.model.visitingHospital))])])]),l("tr",{staticClass:"trHieight"},[l("td",{staticClass:"textCenter"},[e._v(" 假期详情 ")]),l("td",{staticClass:"textLeft",attrs:{colspan:"5"}},[this.totle.h2?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 病假: "+e._s(this.totle.h2)+" ")]):e._e(),this.totle.h3?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 事假: "+e._s(this.totle.h3)+" ")]):e._e(),this.totle.h4?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 产假: "+e._s(this.totle.h4)+" ")]):e._e(),this.totle.h5?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 哺乳假:"+e._s(this.totle.h5)+" ")]):e._e(),this.totle.h6?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 探亲假:"+e._s(this.totle.h6)+" ")]):e._e(),this.totle.h7?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 计生假:"+e._s(this.totle.h7)+" ")]):e._e(),this.totle.h8?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 婚丧假:"+e._s(this.totle.h8)+" ")]):e._e(),this.totle.h9?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 脱产读研:"+e._s(this.totle.h9)+" ")]):e._e(),this.totle.h10?l("span",{staticStyle:{"margin-left":"4px"}},[e._v(" 因公出国:"+e._s(this.totle.h10)+" ")]):e._e(),this.totle.h11?l("span",{staticStyle:{"margin-left":"4px"}},[e._v("因私出国:"+e._s(this.totle.h11)+" ")]):e._e()])]),l("tr",{staticStyle:{height:"200px"}},[l("td",{staticClass:"textCenter"},[e._v("科室领导审核意见:")]),l("td",{attrs:{colspan:"5"}},[l("div",{staticClass:"outer"},[l("img",{staticClass:"img",staticStyle:{width:"181px",height:"133px"},attrs:{src:a("b0f3")}})])])])])],1),l("el-button",{directives:[{name:"print",rawName:"v-print",value:e.prophylacticprintObj,expression:"prophylacticprintObj"}],ref:"btnPrint"})],1)},i=[],n=(a("4160"),a("159b"),{data:function(){return{prophylacticprintObj:{id:"prophylacticPrintContent",popTitle:"",extraCss:"",extraHead:'<meta http-equiv="Content-Language" content="zh-cn"/>'},model:{},totle:{h2:0,h3:0,h4:0,h5:0,h6:0,h7:0,h8:0,h9:0,h10:0,h11:0}}},methods:{Print:function(e){var t=this;t.totle={h2:0,h3:0,h4:0,h5:0,h6:0,h7:0,h8:0,h9:0,h10:0,h11:0},e.detail.forEach((function(e,a,l){t.totle.h2+=e.h2?e.h2:0,t.totle.h3+=e.h3?e.h3:0,t.totle.h4+=e.h4?e.h4:0,t.totle.h5+=e.h5?e.h5:0,t.totle.h6+=e.h6?e.h6:0,t.totle.h7+=e.h7?e.h7:0,t.totle.h8+=e.h8?e.h8:0,t.totle.h9+=e.h9?e.h9:0,t.totle.h10+=e.h10?e.h10:0,t.totle.h11+=e.h11?e.h11:0}),0),this.model=e,this.$refs.btnPrint.$el.click()}}}),s=n,o=(a("31d3"),a("2877")),r=Object(o["a"])(s,l,i,!1,null,null,null);t["a"]=r.exports}}]);