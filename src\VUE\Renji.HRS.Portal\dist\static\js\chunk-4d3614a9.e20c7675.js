(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4d3614a9"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"19de":function(e,t){e.exports=function(e,t,n,o){var r="undefined"!==typeof o?[o,e]:[e],a=new Blob(r,{type:n||"application/octet-stream"});if("undefined"!==typeof window.navigator.msSaveBlob)window.navigator.msSaveBlob(a,t);else{var i=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(a):window.webkitURL.createObjectURL(a),u=document.createElement("a");u.style.display="none",u.href=i,u.setAttribute("download",t),"undefined"===typeof u.download&&u.setAttribute("target","_blank"),document.body.appendChild(u),u.click(),setTimeout((function(){document.body.removeChild(u),window.URL.revokeObjectURL(i)}),200)}}},"40fe":function(e,t,n){},"5ce7":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-row",{staticClass:"filter-container",attrs:{gutter:11,type:"flex"}},[n("el-col",{attrs:{span:4}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"岗位类型"},model:{value:e.listQuery.stationId,callback:function(t){e.$set(e.listQuery,"stationId",t)},expression:"listQuery.stationId"}},e._l(e.stationList,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-col",{attrs:{span:4}},[n("el-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"薪级",max:999999999,min:0,precision:0,controls:!1},model:{value:e.listQuery.scale,callback:function(t){e.$set(e.listQuery,"scale",t)},expression:"listQuery.scale"}})],1),n("el-col",{staticClass:"filter-button",attrs:{span:8}},[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.showDialog()}}},[e._v("添加")]),n("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.downloadexceltemplate}},[e._v("下载模板")]),n("el-upload",{staticStyle:{"margin-left":"10px"},attrs:{action:"","http-request":e.importExcel,accept:".xlsx","show-file-list":!1}},[n("el-button",{attrs:{slot:"trigger",icon:"el-icon-upload2",type:"primary"},slot:"trigger"},[e._v("导入")])],1),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",stripe:"",fit:"","highlight-current-row":"","default-sort":{prop:"Station.Name",order:"ascending"},"header-cell-style":{background:"#F5F7FA",color:"#606266"}},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{sortable:"custom",prop:"Station.Name",label:"岗位类型"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.stationName))])]}}])}),n("el-table-column",{attrs:{sortable:"custom",prop:"Scale",label:"薪级"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(o.scale))])]}}])}),n("el-table-column",{attrs:{sortable:"custom",prop:"Wage",label:"工资标准"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",[e._v(e._s(e._f("formatMoney2")(o.wage)))])]}}])}),n("el-table-column",{attrs:{label:"备注","min-width":"150px ","header-align":"center",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("span",{staticStyle:{"white-space":"pre-wrap"}},[e._v(e._s(o.memo))])]}}])}),n("el-table-column",{attrs:{label:"操作",align:"left","header-align":"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("el-button",{staticStyle:{"margin-left":"5px !important","padding-left":"5px !important"},attrs:{type:"primary",icon:"el-icon-edit",size:"mini"},on:{click:function(t){return e.showDialog(o)}}},[e._v(" 编辑 ")]),e._e()]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.getPageList}})]},proxy:!0}])}),n("editDialog",{ref:"editDialog",on:{refreshData:e.getPageList}})],1)},r=[],a=(n("ac1f"),n("841c"),n("d368")),i=n("e44c"),u=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",{attrs:{title:e.title,visible:e.showDialog,width:"40%","close-on-press-escape":!1,"close-on-click-modal":!1},on:{close:e.closeDialog}},[n("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.dataModel,"label-width":"100px"}},[n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"岗位类型",prop:"stationId"}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{disabled:e.isEdit,filterable:"",clearable:"",placeholder:"岗位类型"},model:{value:e.dataModel.stationId,callback:function(t){e.$set(e.dataModel,"stationId",t)},expression:"dataModel.stationId"}},e._l(e.stationList,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"薪级",prop:"scale"}},[n("el-input",{staticClass:"numrule",attrs:{type:"number",disabled:e.isEdit,placeholder:"薪级",clearable:""},model:{value:e.dataModel.scale,callback:function(t){e.$set(e.dataModel,"scale",t)},expression:"dataModel.scale"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"薪级工资",prop:"wage"}},[n("el-input",{staticClass:"numrule",staticStyle:{width:"100%"},attrs:{type:"number",placeholder:"薪级工资"},on:{change:e.wageChange},model:{value:e.dataModel.wage,callback:function(t){e.$set(e.dataModel,"wage",t)},expression:"dataModel.wage"}})],1)],1)],1),n("el-row",[n("el-col",[n("el-form-item",{attrs:{label:"备注"}},[n("el-input",{attrs:{type:"textarea",rows:3,clearable:"",placeholder:"备注"},model:{value:e.dataModel.memo,callback:function(t){e.$set(e.dataModel,"memo",t)},expression:"dataModel.memo"}})],1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnSaveLoading},on:{click:e.saveDialog}},[e._v("保 存")])],1)],1)],1)},l=[],c=(n("a9e3"),n("b680"),n("5319"),{data:function(){var e=function(e,t,n){/^[+]{0,1}(\d+)$|^[+]{0,1}$/.test(t)?t<=0?n(new Error("必须大于0")):n():n(new Error("请输入正整数"))};return{showDialog:!1,title:"",stationList:[],rules:{stationId:[{required:!0,message:"岗位类型必选",trigger:"change"}],scale:[{required:!0,message:"请输入薪级",trigger:"blur"},{validator:e,trigger:"blur"}],wage:[{required:!0,trigger:"blur",message:"薪级工资必填"},{pattern:/(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/,trigger:"blur",message:"薪级工资必须大于零，且最多两位小数"}]},btnSaveLoading:!1,isEdit:!1,dataModel:{}}},methods:{initDialog:function(e){this.queryOneLevelStation(),e?(this.title="编辑薪级工资",this.isEdit=!0,this.getData(e.id)):(this.title="新增薪级工资",this.isEdit=!1),this.showDialog=!0},getData:function(e){var t=this;a["a"].getSalaryScale({id:e}).then((function(e){e.succeed&&(t.dataModel=e.data)})).catch((function(e){}))},saveDialog:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&(e.btnSaveLoading=!0,e.isEdit?a["a"].updateSalaryScale(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"修改成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})):a["a"].addSalaryScale(e.dataModel).then((function(t){t.succeed&&(e.$message({message:"添加成功",type:"success"}),e.btnSaveLoading=!1,e.$emit("refreshData"),e.closeDialog())})).catch((function(t){e.btnSaveLoading=!1})))}))},wageChange:function(){var e=(this.dataModel.wage+"").replace(/,/g,""),t=Number(e).toFixed(2);this.$set(this.dataModel,"wage",Number(t))},queryOneLevelStation:function(){var e=this;a["a"].queryOneLevelStation().then((function(t){e.stationList=t.data.datas})).catch((function(e){console.log(e)}))},closeDialog:function(){this.dataModel={},this.showDialog=!1,this.$refs.dataForm.resetFields()}}}),p=c,y=(n("7ee8"),n("9a62"),n("2877")),s=Object(y["a"])(p,u,l,!1,null,"75306f3b",null),d=s.exports,m={components:{editDialog:d},data:function(){return{addForm:{},dataList:[],stationList:[],total:0,listQuery:{pageIndex:1,pageSize:10,order:"+Station.Name",stationId:null},listLoading:!1,temp:{}}},created:function(){this.queryOneLevelStation(),this.getPageList()},methods:{search:function(){this.listQuery.pageIndex=1,this.getPageList()},sortChange:function(e,t,n){this.listQuery.pageIndex=1;var o="";"descending"===e.order&&(o="-"),"ascending"===e.order&&(o="+"),this.listQuery.order=o+e.prop,this.getPageList()},importExcel:function(e){var t=this,n=e.file,o=new FormData;a["a"].importSalaryScale(n,o).then((function(e){e.succeed&&(t.$message({message:"导入成功",type:"success"}),t.search())})).catch((function(e){t.search()}))},exportExcel:function(){var e=this;a["a"].exportSalaryScale(this.listQuery).then((function(t){var o=n("19de"),r="薪级工资"+e.$moment().format("YYYYMMDDHHmmss")+".xlsx";t.data?o(t.data,r):o(t,r)}))},downloadexceltemplate:function(){i["a"].downlodaImportExcelTemplate({type:"importsalaryScale"}).then((function(e){var t=n("19de"),o="SalaryScaleTemplate.xlsx";e.data?t(e.data,o):t(e,o)})).catch((function(e){}))},getPageList:function(){var e=this;this.listLoading=!0,a["a"].querySalaryScale(this.listQuery).then((function(t){e.listLoading=!1,t.succeed?(e.dataList=t.data.datas,e.total=t.data.recordCount,e.listQuery.pageIndex=t.data.pageIndex):e.$notice.resultTip(t)})).catch((function(t){console.log(t),e.listLoading=!1}))},queryOneLevelStation:function(){var e=this;a["a"].queryOneLevelStation().then((function(t){e.stationList=t.data.datas,console.log(e.stationList)})).catch((function(e){console.log(e)}))},deleteSalaryScale:function(e){var t=this;this.$confirm("确定删除这条记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a["a"].deleteSalaryScale(e).then((function(e){e.succeed?(t.getPageList(),t.$notice.message("删除成功","success")):e.succeed||t.$notice.message("删除失败，请联系管理员","info")})).catch((function(e){t.listLoading=!1,console.log(e)}))})).catch((function(e){t.listLoading=!1}))},showDialog:function(e){this.$refs.editDialog.initDialog(e)},closeAddDialog:function(){this.$refs["ref_addForm"].resetFields(),this.$refs["ref_addForm"].clearValidate()}}},f=m,g=Object(y["a"])(f,o,r,!1,null,null,null);t["default"]=g.exports},"7ee8":function(e,t,n){"use strict";var o=n("40fe"),r=n.n(o);r.a},"841c":function(e,t,n){"use strict";var o=n("d784"),r=n("825a"),a=n("1d80"),i=n("129f"),u=n("14c3");o("search",1,(function(e,t,n){return[function(t){var n=a(this),o=void 0==t?void 0:t[e];return void 0!==o?o.call(t,n):new RegExp(t)[e](String(n))},function(e){var o=n(t,e,this);if(o.done)return o.value;var a=r(e),l=String(this),c=a.lastIndex;i(c,0)||(a.lastIndex=0);var p=u(a,l);return i(a.lastIndex,c)||(a.lastIndex=c),null===p?-1:p.index}]}))},"9a62":function(e,t,n){"use strict";var o=n("b5b1"),r=n.n(o);r.a},b5b1:function(e,t,n){},d368:function(e,t,n){"use strict";var o=n("cfe3"),r="Organization",a=new o["a"](r);t["a"]={QueryOrganizationHiddenTop:function(e){return a.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return a.get("QueryOrganization",e)},QueryDepartment:function(e){return a.get("QueryDepartment",e)},GetDepartment:function(e){return a.get("GetDepartment",e)},AddDepartment:function(e){return a.post("AddDepartment",e)},UpdateDepartment:function(e){return a.post("UpdateDepartment",e)},MoveDepartment:function(e){return a.post("MoveDepartment",e)},MergeDepartment:function(e){return a.post("MergeDepartment",e)},DeleteDepartment:function(e){return a.post("DeleteDepartment",e)},queryPosition:function(e){return a.post("QueryPosition",e)},getPosition:function(e){return a.get("GetPosition",e)},addPosition:function(e){return a.post("AddPosition",e)},updatePosition:function(e){return a.post("UpdatePosition",e)},deletePosition:function(e){return a.post("DeletePosition",e)},GetStation:function(e){return a.get("GetStation",e)},AddStation:function(e){return a.post("AddStation",e)},UpdateStation:function(e){return a.post("UpdateStation",e)},DeleteStation:function(e){return a.post("DeleteStation",e)},QueryPositionStationTree:function(e){return a.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return a.post("AllocatePosition",e)},DeletePositionStation:function(e){return a.post("DeletePositionStation",e)},queryDeptByUser:function(e){return a.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return a.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return a.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return a.get("QuerySenioritySelect")},queryStationAllowance:function(e){return a.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return a.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),a.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return a.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return a.get("GetStationAllowance",e)},addStationAllowance:function(e){return a.post("AddStationAllowance",e)},updateStationAllowance:function(e){return a.post("UpdateStationAllowance",e)},querySeniority:function(e){return a.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),a.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return a.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return a.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return a.get("GetSeniority",e)},addSeniority:function(e){return a.post("AddSeniority",e)},updateSeniority:function(e){return a.post("UpdateSeniority",e)},querySalaryScale:function(e){return a.get("QuerySalaryScale",e)},getSalaryScale:function(e){return a.get("GetSalaryScale",e)},addSalaryScale:function(e){return a.post("AddSalaryScale",e)},updateSalaryScale:function(e){return a.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return a.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),a.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return a.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return a.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return a.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return a.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return a.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return a.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return a.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return a.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return a.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return a.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return a.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return a.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return a.post("DeleteTelephoneFee",e)}}},e44c:function(e,t,n){"use strict";n("4160"),n("b64b"),n("159b");var o=n("cfe3"),r="HR",a=new o["a"](r);t["a"]={queryEmployee:function(e){return a.get("QueryEmployee",e)},queryEmployeeByCommonCondition:function(e){return a.post("QueryEmployeeByCommonCondition",e)},queryEmployeeStatus:function(){return a.get("QueryEmployeeStatus")},queryRank:function(){return a.get("QueryRank")},queryAdministrativePosition:function(){return a.get("queryAdministrativePosition")},queryMajorTechnical:function(){return a.get("queryMajorTechnical")},queryOfficialRank:function(){return a.get("QueryOfficialRank")},queryHireStyle:function(){return a.get("QueryHireStyle")},queryLeaveStyle:function(){return a.get("QueryLeaveStyle")},queryMarryList:function(){return a.get("QueryMarryList")},queryNationality:function(){return a.get("QueryNationality")},queryRegisterType:function(){return a.get("QueryRegisterType")},deleteEmployee:function(e){return a.post("DeleteEmployee",e)},queryDocumentType:function(){return a.get("QueryDocumentType")},addEmployee:function(e){return a.post("AddEmployee",e)},isEmpCodeExists:function(e){var t={id:e.id,empCode:e.empCode};return a.get("IsEmpCodeExists",t)},checkIdentityNumber:function(e){var t={id:e.id,identityNumber:e.identityNumber};return a.get("checkIdentityNumber",t)},getEmployee:function(e){return a.get("GetEmployee",e)},updateEmployee:function(e){return a.post("UpdateEmployee",e)},getPersonnelInformation:function(e){return a.get("GetPersonnelInformation",e)},postAddDocumentInformation:function(e){return a.get("PostAddDocumentInformation",e)},postUpdateDocumentInformation:function(e){return a.get("PostUpdateDocumentInformation",e)},postDeleteDocumentInformation:function(e){return a.get("PostDeleteDocumentInformation",e)},queryEmployeeByConditions:function(e){return a.post("QueryEmployeeByConditions",e)},queryEmployeeByCommonConditions:function(e){return a.post("QueryEmployeeByCommonConditions",e)},queryEmployeeQuerySetting:function(e){return a.get("QueryEmployeeQuerySetting",e)},querySettingColumns:function(){return a.get("QueryEmployeeInfoQuerySettingColumns")},queryOperations:function(e){return a.get("QueryOperationByColumnType",e)},addEmployeeQuerySetting:function(e){return a.post("AddEmployeeQuerySetting",e)},deleteEmployeeQuerySetting:function(e){return a.post("DeleteEmployeeQuerySetting",e)},queryDegrees:function(){return a.get("QueryDegrees")},queryEducation:function(){return a.get("QueryEducation")},QuerySocialSecurity:function(){return a.get("QuerySocialSecurity")},queryParty:function(){return a.get("QueryParty")},queryRecruitmentCategory:function(){return a.get("QueryRecruitmentCategory")},queryRecruitmentCompany:function(){return a.get("QueryRecruitmentCompany")},getEmployeeHR:function(e){return a.get("GetEmployeeHR",e)},updateEmployeeHR:function(e){return a.post("UpdateEmployeeHR",e)},updateCompanyAge:function(){return a.post("UpdateCompanyAge")},calculateGeneralHoliday:function(){return a.get("CalculateGeneralHoliday")},queryStation:function(e){return a.get("QueryStation",e)},queryPositionStation:function(e){return a.get("QueryPositionStation",e)},queryEmployeeStation:function(e){return a.get("QueryEmployeeStation",e)},addEmployeeStation:function(e){return a.post("AddEmployeeStation",e)},updateEmployeeStation:function(e){return a.post("UpdateEmployeeStation",e)},deleteEmployeeStation:function(e){return a.post("DeleteEmployeeStation",e)},queryLevel:function(){return a.get("QueryLevel")},queryEmployeeCertify:function(e){return a.get("QueryEmployeeCertify",e)},addEmployeeCertify:function(e){return a.post("AddEmployeeCertify",e)},updateEmployeeCertify:function(e){return a.post("UpdateEmployeeCertify",e)},deleteEmployeeCertify:function(e){return a.post("DeleteEmployeeCertify",e)},queryGraduation:function(){return a.get("QueryGraduation")},queryLearnWay:function(){return a.get("QueryLearnWay")},queryEmployeeEducation:function(e){return a.get("QueryEmployeeEducation",e)},addEmployeeEducation:function(e){return a.post("AddEmployeeEducation",e)},updateEmployeeEducation:function(e){return a.post("UpdateEmployeeEducation",e)},deleteEmployeeEducation:function(e){return a.post("DeleteEmployeeEducation",e)},queryEmployeeWork:function(e){return a.get("QueryEmployeeWork",e)},addEmployeeWork:function(e){return a.post("AddEmployeeWork",e)},updateEmployeeWork:function(e){return a.post("UpdateEmployeeWork",e)},deleteEmployeeWork:function(e){return a.post("DeleteEmployeeWork",e)},queryAbroadType:function(){return a.get("QueryAbroadType")},queryEmployeeAbroad:function(e){return a.get("QueryEmployeeAbroadInfo",e)},addEmployeeAbroad:function(e){return a.post("AddEmployeeAbroadInfo",e)},updateEmployeeAbroad:function(e){return a.post("UpdateEmployeeAbroadInfo",e)},deleteEmployeeAbroad:function(e){return a.post("DeleteEmployeeAbroadInfo",e)},queryContractType:function(){return a.get("QueryContractType")},queryEmployeeContract:function(e){return a.get("QueryEmployeeContract",e)},queryRenewEmployeeContract:function(e){return a.get("QueryRenewEmployeeContract",e)},updateNextTimeRemind:function(e){return a.post("UpdateNextTimeRemind",e)},sendEmailForRenewRemind:function(e){return a.post("SendEmailForRenewRemind",e)},batchUpdateNextTimeRemind:function(e){return a.post("BatchUpdateNextTimeRemind",e)},batchSendEmailForRenewRemind:function(e){return a.post("BatchSendEmailForRenewRemind",e)},addEmployeeContract:function(e){return a.post("AddEmployeeContract",e)},updateEmployeeContract:function(e){return a.post("UpdateEmployeeContract",e)},deleteEmployeeContract:function(e){return a.post("DeleteEmployeeContract",e)},queryTrainLevel:function(){return a.get("QueryTrainLevel")},queryEmployeeTrain:function(e){return a.get("QueryEmployeeTrain",e)},addEmployeeTrain:function(e){return a.post("AddEmployeeTrain",e)},updateEmployeeTrain:function(e){return a.post("UpdateEmployeeTrain",e)},deleteEmployeeTrain:function(e){return a.post("DeleteEmployeeTrain",e)},queryYearList:function(){return a.get("QueryYearList")},queryEvaluateResult:function(){return a.get("QueryEvaluateResult")},queryEmployeeAssessment:function(e){return a.get("QueryEmployeeAssessment",e)},addEmployeeAssessment:function(e){return a.post("AddEmployeeAssessment",e)},updateEmployeeAssessment:function(e){return a.post("UpdateEmployeeAssessment",e)},deleteEmployeeAssessment:function(e){return a.post("DeleteEmployeeAssessment",e)},queryEmployeeDeduct:function(e){return a.get("QueryEmployeeDeduct",e)},addEmployeeDeduct:function(e){return a.post("AddEmployeeDeduct",e)},updateEmployeeDeduct:function(e){return a.get("UpdateEmployeeDeduct",e)},deleteEmployeeDeduct:function(e){return a.post("DeleteEmployeeDeduct",e)},downloadEmployeeDeductTemplate:function(e){return a.getFile("DownlodaEmployeeDeductTemplate",e)},importEmployeeDeduct:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),a.postForm("ImportEmployeeDeduct",n)},queryEmployeeDeductUnCalculate:function(e){return a.get("QueryEmployeeDeductUnCalculate",e)},queryEmployeeDeductCalculate:function(e){return a.get("QueryEmployeeDeductCalculate",e)},getEmployeeDeductCalculate:function(e){return a.get("GetEmployeeDeductCalculate",e)},updateEmployeeDeductCalculate:function(e){return a.post("UpdateEmployeeDeductCalculate",e)},exportEmployeeDeductCalculate:function(e){return a.post("ExportEmployeeDeductCalculate",{data:e,responseType:"arraybuffer"})},queryEmployeeDeductWorkingAge:function(e){return a.get("QueryEmployeeDeductWorkingAge",e)},getEmployeeDeductWorkingAge:function(e){return a.get("GetEmployeeDeductWorkingAge",e)},addEmployeeDeductWorkingAge:function(e){return a.post("AddEmployeeDeductWorkingAge",e)},updateEmployeeDeductWorkingAge:function(e){return a.post("UpdateEmployeeDeductWorkingAge",e)},deleteEmployeeDeductWorkingAge:function(e){return a.post("DeleteEmployeeDeductWorkingAge",e)},queryIncentType:function(){return a.get("QueryIncentType")},queryIncentLevel:function(){return a.get("QueryIncentLevel")},queryEmployeeIncentive:function(e){return a.get("QueryEmployeeIncentive",e)},addEmployeeIncentive:function(e){return a.post("AddEmployeeIncentive",e)},updateEmployeeIncentive:function(e){return a.post("UpdateEmployeeIncentive",e)},deleteEmployeeIncentive:function(e){return a.post("DeleteEmployeeIncentive",e)},getEmployeeHealth:function(e){return a.get("GetEmployeeHealth",e)},updateEmployeeHealth:function(e){return a.post("UpdateEmployeeHealth",e)},queryAccidentType:function(){return a.get("QueryAccidentType")},queryEmployeeAccident:function(e){return a.get("QueryEmployeeAccident",e)},addEmployeeAccident:function(e){return a.post("AddEmployeeAccident",e)},updateEmployeeAccident:function(e){return a.post("UpdateEmployeeAccident",e)},deleteEmployeeAccident:function(e){return a.post("DeleteEmployeeAccident",e)},queryEmployeeTeach:function(e){return a.get("QueryEmployeeTeach",e)},addEmployeeTeach:function(e){return a.post("AddEmployeeTeach",e)},updateEmployeeTeach:function(e){return a.post("UpdateEmployeeTeach",e)},deleteEmployeeTeach:function(e){return a.post("DeleteEmployeeTeach",e)},queryEmployeeRelation:function(e){return a.get("QueryEmployeeRelation",e)},addEmployeeRelation:function(e){return a.post("AddEmployeeRelation",e)},updateEmployeeRelation:function(e){return a.post("UpdateEmployeeRelation",e)},deleteEmployeeRelation:function(e){return a.post("DeleteEmployeeRelation",e)},queryIncomeType:function(){return a.get("QueryIncomeType")},queryEmployeeArticle:function(e){return a.get("QueryEmployeeArticle",e)},addEmployeeArticle:function(e){return a.post("AddEmployeeArticle",e)},updateEmployeeArticle:function(e){return a.post("UpdateEmployeeArticle",e)},deleteEmployeeArticle:function(e){return a.post("DeleteEmployeeArticle",e)},queryClassLevel:function(){return a.get("QueryClassLevel")},queryEmployeeClass:function(e){return a.get("QueryEmployeeClass",e)},addEmployeeClass:function(e){return a.post("AddEmployeeClass",e)},updateEmployeeClass:function(e){return a.post("UpdateEmployeeClass",e)},deleteEmployeeClass:function(e){return a.post("DeleteEmployeeClass",e)},queryEmployeePatent:function(e){return a.get("QueryEmployeePatent",e)},addEmployeePatent:function(e){return a.post("AddEmployeePatent",e)},updateEmployeePatent:function(e){return a.post("UpdateEmployeePatent",e)},deleteEmployeePatent:function(e){return a.post("DeleteEmployeePatent",e)},queryTeacherType:function(){return a.get("QueryTeacherType")},queryEmployeeTeacher:function(e){return a.get("QueryEmployeeTeacher",e)},addEmployeeTeacher:function(e){return a.post("AddEmployeeTeacher",e)},updateEmployeeTeacher:function(e){return a.post("UpdateEmployeeTeacher",e)},deleteEmployeeTeacher:function(e){return a.post("DeleteEmployeeTeacher",e)},queryAwardLevel:function(){return a.get("QueryAwardLevel")},queryDictByParentCode:function(e){return a.get("QueryDictByParentCode",e)},queryHighTalent:function(){return a.get("QueryHighTalent")},queryEmployeeAward:function(e){return a.get("QueryEmployeeAward",e)},addEmployeeAward:function(e){return a.post("AddEmployeeAward",e)},updateEmployeeAward:function(e){return a.post("UpdateEmployeeAward",e)},deleteEmployeeAward:function(e){return a.post("DeleteEmployeeAward",e)},addEmployeeHighTalent:function(e){return a.post("AddEmployeeHighTalent",e)},updateEmployeeHighTalent:function(e){return a.post("UpdateEmployeeHighTalent",e)},queryEmployeeHighTalent:function(e){return a.get("QueryEmployeeHighTalent",e)},deleteEmployeeHighTalent:function(e){return a.post("DeleteEmployeeHighTalent",e)},queryEmployeeDeptHistory:function(e){return a.get("QueryEmployeeDeptHistory",e)},updateEmployeeDept:function(e){return a.post("UpdateEmployeeDept",e)},batchUpdateEmployeeDept:function(e){return a.post("BatchUpdateEmployeeDept",e)},queryEmployeeList:function(e){return a.post("QueryEmployeeList",e)},queryEmployeeListTree:function(e){return a.get("QueryEmployeeListTree",e)},queryDictsSetting:function(e){return a.get("QueryDictsSetting",e)},queryEmployeeListSettingTree:function(e){return a.get("QueryEmployeeListSettingTree",e)},queryAdvancedQueryType:function(){return a.get("QueryAdvancedQueryType")},saveEmployeeList:function(e){return a.post("SaveEmployeeList",e)},deleteEmployeeList:function(e){return a.post("DeleteEmployeeList",e)},getEmployeeList:function(e){return a.get("GetEmployeeList",e)},getEmployeeListExcel:function(e){return a.getFile("GetEmployeeListExcel",e)},queryDeptPrincipal:function(e){return a.get("QueryDeptPrincipal",e)},queryOtherEmployeeInfo:function(e){return a.post("QueryOtherEmployeeInfo",e)},saveOtherEmployeeInfo:function(e){return a.post("SaveOtherEmployeeInfo",e)},deleteOtherEmployeeInfo:function(e){return a.post("DeleteOtherEmployeeInfo",e)},queryOtherEmpTypes:function(e){return a.get("QueryOtherEmpTypes",e)},importExcel:function(e,t){var n=new FormData;return t&&Object.keys(t).forEach((function(e){return n.append(e,t[e])})),n.append("file",e),a.postForm("ImportExcel",n)},downlodaImportExcelTemplate:function(e){return a.getFile("DownlodaImportExcelTemplate",e)},getSocialSecurityInfo:function(e){return a.get("getSocialSecurityInfo",e)},updateEmployeeSocialSecurity:function(e){return a.post("updateEmployeeSocialSecurity",e)},queryPayRollOrgClass:function(){return a.get("QueryPayRollOrgClass")},queryPayRollCompGroup:function(){return a.get("QueryPayRollCompGroup")},queryPayRollOrgSalary:function(e){return a.get("QueryPayRollOrgSalary",e)},queryPayRollOrgSalaryLevel:function(e){return a.get("QueryPayRollOrgSalaryLevel",e)},queryPayRollOrgPositionSalarys:function(e){return a.get("QueryPayRollOrgPositionSalarys",e)},queryWorkState:function(){return a.get("QueryWorkState")},getWagesInfo:function(e){return a.get("GetWagesInfo",e)},updateEmployeeBenefit:function(e){return a.post("UpdateEmployeeBenefit",e)},queryWagesHistory:function(e){return a.get("QueryWagesHistory",e)},getEmployeePayrollPrint:function(e){return a.get("GetEmployeePayrollPrint",e)},updateEmployeePayrollPrint:function(e){return a.post("UpdateEmployeePayrollPrint",e)},deleteEmployeePayrollPrint:function(e){return a.post("DeleteEmployeePayrollPrint",e)},getPrintDetailsInfo:function(e){return a.get("GetPrintDetailsInfo",e)},getEmployeeWage:function(e){return a.get("GetEmployeeWage",e)},editEmployeeWage:function(e){return a.post("EditEmployeeWage",e)},editEmployeeParty:function(e){return a.post("EditEmployeeParty",e)},queryEmployeeHRAffiliatedBranch:function(e){return a.get("QueryEmployeeHRAffiliatedBranch",e)},getEmployeeHRAffiliatedBranch:function(e){return a.get("GetEmployeeHRAffiliatedBranch",e)},addEmployeeHRAffiliatedBranch:function(e){return a.post("AddEmployeeHRAffiliatedBranch",e)},updateEmployeeHRAffiliatedBranch:function(e){return a.post("UpdateEmployeeHRAffiliatedBranch",e)},deleteEmployeeHRAffiliatedBranch:function(e){return a.post("DeleteEmployeeHRAffiliatedBranch",e)},queryEmployeeHRDict:function(e){return a.get("QueryEmployeeHRDict",e)},getEmployeeHRDict:function(e){return a.get("GetEmployeeHRDict",e)},addEmployeeHRDict:function(e){return a.post("AddEmployeeHRDict",e)},updateEmployeeHRDict:function(e){return a.post("UpdateEmployeeHRDict",e)},deleteEmployeeHRDict:function(e){return a.post("DeleteEmployeeHRDict",e)},queryEmployeeHRPartyMemberHonor:function(e){return a.get("QueryEmployeeHRPartyMemberHonor",e)},getEmployeeHRPartyMemberHonor:function(e){return a.get("GetEmployeeHRPartyMemberHonor",e)},addEmployeeHRPartyMemberHonor:function(e){return a.post("AddEmployeeHRPartyMemberHonor",e)},updateEmployeeHRPartyMemberHonor:function(e){return a.post("UpdateEmployeeHRPartyMemberHonor",e)},deleteEmployeeHRPartyMemberHonor:function(e){return a.post("DeleteEmployeeHRPartyMemberHonor",e)}}}}]);