(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2bd5dc36"],{"1d57":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("layout4",{scopedSlots:e._u([{key:"main",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.socialInsuranceModel}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"年份"}},[n("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",editable:!1,clearable:!1,"value-format":"yyyy"},model:{value:e.socialInsuranceModel.year,callback:function(t){e.$set(e.socialInsuranceModel,"year",t)},expression:"socialInsuranceModel.year"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"部门"}},[n("c-select-tree",{attrs:{options:e.treeData,"tree-props":e.treeProps},model:{value:e.socialInsuranceModel.deptId,callback:function(t){e.$set(e.socialInsuranceModel,"deptId",t)},expression:"socialInsuranceModel.deptId"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"工号"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.socialInsuranceModel.empCode,callback:function(t){e.$set(e.socialInsuranceModel,"empCode",t)},expression:"socialInsuranceModel.empCode"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"姓名"}},[n("el-input",{staticClass:"edit-input",attrs:{size:"small"},model:{value:e.socialInsuranceModel.displayName,callback:function(t){e.$set(e.socialInsuranceModel,"displayName",t)},expression:"socialInsuranceModel.displayName"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:""}},[n("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),n("el-button",{attrs:{type:"primary"},on:{click:e.exportData}},[e._v("导出")])],1)],1)],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",stripe:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F5F7FA",color:"#606266"}}},[n("el-table-column",{attrs:{prop:"date",label:"唯一码",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.uid))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"工号",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.empCode))])]}}])}),n("el-table-column",{attrs:{prop:"data",label:"工资单号",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.paySlipNumber))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"姓名",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.displayName))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"参加工作日期",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.SocietyDate))])]}}])}),n("el-table-column",{attrs:{prop:"date",label:"部门",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.empDept))])]}}])}),n("el-table-column",{attrs:{prop:"data",label:"年份",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.year))])]}}])}),n("el-table-column",{attrs:{prop:"data",label:"上年度实际月工资",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[n("span")]}}])}),n("el-table-column",{attrs:{prop:"data",label:"本年度月缴费基数",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[n("span")]}}])}),n("el-table-column",{attrs:{prop:"data",label:"个人养老保险缴纳额",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[n("span")]}}])}),n("el-table-column",{attrs:{prop:"data",label:"个人医疗保险缴纳额",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[n("span")]}}])}),n("el-table-column",{attrs:{prop:"data",label:"个人失业保险缴纳额",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[n("span")]}}])}),n("el-table-column",{attrs:{prop:"data",label:"缴费总计",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[n("span")]}}])}),n("el-table-column",{attrs:{prop:"data",label:"缴费状态",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){e.row;return[n("span")]}}])})],1),n("c-pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,"page-sizes":[10,20,50],page:e.listQuery.pageIndex,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageIndex",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.search}})]},proxy:!0}])})],1)},r=[],o=(n("99af"),n("d3b7"),n("25f0"),n("4d90"),n("d368")),l={components:{},data:function(){return{socialInsuranceModel:{},treeData:[],treeProps:{parent:"parentId",value:"id",label:"name",children:"children"},total:0,listQuery:{pageIndex:1,pageSize:10},listLoading:!1,tableData:[]}},created:function(){this.loadTree()},methods:{getNowTime:function(){var e=new Date,t=e.getFullYear(),n=e.getMonth();n+=1,n=n.toString().padStart(2,"0");var a="".concat(t,"-").concat(n);return a},loadTree:function(){var e=this;o["a"].queryDeptByUser({}).then((function(t){e.treeData=t.data})).catch((function(e){}))},search:function(){},exportData:function(){}}},i=l,u=n("2877"),s=Object(u["a"])(i,a,r,!1,null,null,null);t["default"]=s.exports},d368:function(e,t,n){"use strict";var a=n("cfe3"),r="Organization",o=new a["a"](r);t["a"]={QueryOrganizationHiddenTop:function(e){return o.get("QueryOrganizationHiddenTop",e)},QueryOrganization:function(e){return o.get("QueryOrganization",e)},QueryDepartment:function(e){return o.get("QueryDepartment",e)},GetDepartment:function(e){return o.get("GetDepartment",e)},AddDepartment:function(e){return o.post("AddDepartment",e)},UpdateDepartment:function(e){return o.post("UpdateDepartment",e)},MoveDepartment:function(e){return o.post("MoveDepartment",e)},MergeDepartment:function(e){return o.post("MergeDepartment",e)},DeleteDepartment:function(e){return o.post("DeleteDepartment",e)},queryPosition:function(e){return o.post("QueryPosition",e)},getPosition:function(e){return o.get("GetPosition",e)},addPosition:function(e){return o.post("AddPosition",e)},updatePosition:function(e){return o.post("UpdatePosition",e)},deletePosition:function(e){return o.post("DeletePosition",e)},GetStation:function(e){return o.get("GetStation",e)},AddStation:function(e){return o.post("AddStation",e)},UpdateStation:function(e){return o.post("UpdateStation",e)},DeleteStation:function(e){return o.post("DeleteStation",e)},QueryPositionStationTree:function(e){return o.get("QueryPositionStationTree",e)},AllocatePosition:function(e){return o.post("AllocatePosition",e)},DeletePositionStation:function(e){return o.post("DeletePositionStation",e)},queryDeptByUser:function(e){return o.get("QueryDeptByUser",e)},queryOneLevelStation:function(){return o.get("QueryOneLevelStation")},queryTwoLevelStation:function(e){return o.get("QueryTwoLevelStation",e)},querySenioritySelect:function(){return o.get("QuerySenioritySelect")},queryStationAllowance:function(e){return o.get("QueryStationAllowance",e)},downloadStationAllowanceTemplate:function(e){return o.post("DownloadStationAllowanceTemplate",{data:e,responseType:"arraybuffer"})},importStationAllowance:function(e,t){return t.append("file",e),o.postForm("ImportStationAllowance",t)},exportStationAllowance:function(e){return o.post("ExportStationAllowance",{data:e,responseType:"arraybuffer"})},getStationAllowance:function(e){return o.get("GetStationAllowance",e)},addStationAllowance:function(e){return o.post("AddStationAllowance",e)},updateStationAllowance:function(e){return o.post("UpdateStationAllowance",e)},querySeniority:function(e){return o.get("QuerySeniority",e)},importSeniority:function(e,t){return t.append("file",e),o.postForm("ImportSeniority",t)},downloadSeniorityTemplate:function(e){return o.post("DownloadSeniorityTemplate",{data:e,responseType:"arraybuffer"})},exportSeniority:function(e){return o.post("ExportSeniority",{data:e,responseType:"arraybuffer"})},getSeniority:function(e){return o.get("GetSeniority",e)},addSeniority:function(e){return o.post("AddSeniority",e)},updateSeniority:function(e){return o.post("UpdateSeniority",e)},querySalaryScale:function(e){return o.get("QuerySalaryScale",e)},getSalaryScale:function(e){return o.get("GetSalaryScale",e)},addSalaryScale:function(e){return o.post("AddSalaryScale",e)},updateSalaryScale:function(e){return o.post("UpdateSalaryScale",e)},deleteSalaryScale:function(e){return o.post("DeleteSalaryScale",e)},importSalaryScale:function(e,t){return t.append("file",e),o.postForm("ImportSalaryScale",t)},exportSalaryScale:function(e){return o.post("ExportSalaryScale",{data:e,responseType:"arraybuffer"})},querySalaryScaleByStationId:function(e){return o.get("QuerySalaryScaleByStationId",e)},queryStationAllowanceByStationId:function(e){return o.get("QueryStationAllowanceByStationId",e)},queryCarSubsidy:function(e){return o.get("QueryCarSubsidy",e)},getCarSubsidy:function(e){return o.get("GetCarSubsidy",e)},addCarSubsidy:function(e){return o.post("AddCarSubsidy",e)},updateCarSubsidy:function(e){return o.post("UpdateCarSubsidy",e)},deleteCarSubsidy:function(e){return o.post("DeleteCarSubsidy",e)},queryTelephoneFee:function(e){return o.get("QueryTelephoneFee",e)},getTelephoneFee:function(e){return o.get("GetTelephoneFee",e)},addTelephoneFee:function(e){return o.post("AddTelephoneFee",e)},updateTelephoneFee:function(e){return o.post("UpdateTelephoneFee",e)},deleteTelephoneFee:function(e){return o.post("DeleteTelephoneFee",e)}}}}]);